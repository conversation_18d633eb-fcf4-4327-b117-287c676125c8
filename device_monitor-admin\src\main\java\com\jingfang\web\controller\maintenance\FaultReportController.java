package com.jingfang.web.controller.maintenance;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.common.core.controller.BaseController;
import com.jingfang.common.core.domain.AjaxResult;
import com.jingfang.fault_report.module.dto.FaultReportDto;
import com.jingfang.fault_report.module.request.FaultReportSearchRequest;
import com.jingfang.fault_report.module.vo.FaultReportVo;
import com.jingfang.fault_report.service.FaultReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 资产故障申报控制器
 */
@Slf4j
@RestController
@RequestMapping("/maintenance/fault")
public class FaultReportController extends BaseController {
    
    @Autowired
    private FaultReportService faultReportService;
    
    /**
     * 提交故障申报
     */
    @PostMapping("/report")
    public AjaxResult submitFaultReport(@Validated @RequestBody FaultReportDto faultReportDto) {
        try {
            log.info("提交故障申报请求参数：{}", faultReportDto);
            Map<String, String> result = faultReportService.submitFaultReport(faultReportDto);
            return AjaxResult.success("故障申报成功", result);
        } catch (Exception e) {
            log.error("提交故障申报异常：", e);
            return AjaxResult.error("故障申报失败：" + e.getMessage());
        }
    }
    
    /**
     * 分页查询故障申报列表
     */
    @PreAuthorize("@ss.hasPermi('maintenance:fault:list')")
    @GetMapping("/list")
    public AjaxResult list(FaultReportSearchRequest request) {
        try {
            IPage<FaultReportVo> page = faultReportService.getFaultReportPage(request);
            return AjaxResult.success(page);
        } catch (Exception e) {
            log.error("查询故障申报列表异常：", e);
            return AjaxResult.error("查询故障申报列表失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取故障申报详细信息
     */
    @GetMapping("/{faultId}")
    public AjaxResult getInfo(@PathVariable("faultId") String faultId) {
        try {
            FaultReportVo faultReportVo = faultReportService.getFaultReportById(faultId);
            return AjaxResult.success(faultReportVo);
        } catch (Exception e) {
            log.error("查询故障申报详情异常：", e);
            return AjaxResult.error("查询故障申报详情失败：" + e.getMessage());
        }
    }
    
    /**
     * 查询我的故障申报
     */
    @GetMapping("/my-reports")
    public AjaxResult getMyFaultReports(@RequestParam(required = false) List<Integer> statusList,
                                       @RequestParam(defaultValue = "1") Integer pageNum,
                                       @RequestParam(defaultValue = "10") Integer pageSize) {
        try {
            // 如果传入了分页参数，使用分页查询
            if (pageNum != null && pageSize != null) {
                FaultReportSearchRequest request = new FaultReportSearchRequest();
                request.setMyReports(true);
                request.setStatusList(statusList);
                request.setPageNum(pageNum);
                request.setPageSize(pageSize);
                IPage<FaultReportVo> page = faultReportService.getFaultReportPage(request);
                return AjaxResult.success(page);
            } else {
                // 否则返回全部数据
                List<FaultReportVo> faultReports = faultReportService.getMyFaultReports(statusList);
                return AjaxResult.success(faultReports);
            }
        } catch (Exception e) {
            log.error("查询我的故障申报异常：", e);
            return AjaxResult.error("查询我的故障申报失败：" + e.getMessage());
        }
    }
    
    /**
     * 查询我处理的故障申报
     */
    @PreAuthorize("@ss.hasPermi('maintenance:fault:handle')")
    @GetMapping("/my-handling")
    public AjaxResult getMyHandlingFaultReports(@RequestParam(required = false) List<Integer> statusList) {
        try {
            List<FaultReportVo> faultReports = faultReportService.getMyHandlingFaultReports(statusList);
            return AjaxResult.success(faultReports);
        } catch (Exception e) {
            log.error("查询我处理的故障申报异常：", e);
            return AjaxResult.error("查询我处理的故障申报失败：" + e.getMessage());
        }
    }
    
    /**
     * 受理故障申报
     */
    @PreAuthorize("@ss.hasPermi('maintenance:fault:accept')")
    @PostMapping("/accept")
    public AjaxResult acceptFaultReport(@RequestBody Map<String, Object> params) {
        try {
            String faultId = (String) params.get("faultId");
            Long handlerId = Long.valueOf(params.get("handlerId").toString());
            String handleDescription = (String) params.get("handleDescription");
            
            boolean success = faultReportService.acceptFaultReport(faultId, handlerId, handleDescription);
            return success ? AjaxResult.success("受理成功") : AjaxResult.error("受理失败");
        } catch (Exception e) {
            log.error("受理故障申报异常：", e);
            return AjaxResult.error("受理故障申报失败：" + e.getMessage());
        }
    }
    
    /**
     * 更新故障处理进度
     */
    @PreAuthorize("@ss.hasPermi('maintenance:fault:handle')")
    @PostMapping("/progress")
    public AjaxResult updateFaultProgress(@RequestBody Map<String, Object> params) {
        try {
            String faultId = (String) params.get("faultId");
            String handleDescription = (String) params.get("handleDescription");
            
            boolean success = faultReportService.updateFaultProgress(faultId, handleDescription);
            return success ? AjaxResult.success("更新成功") : AjaxResult.error("更新失败");
        } catch (Exception e) {
            log.error("更新故障处理进度异常：", e);
            return AjaxResult.error("更新故障处理进度失败：" + e.getMessage());
        }
    }
    
    /**
     * 完成故障处理
     */
    @PreAuthorize("@ss.hasPermi('maintenance:fault:handle')")
    @PostMapping("/complete")
    public AjaxResult completeFaultReport(@RequestBody Map<String, Object> params) {
        try {
            String faultId = (String) params.get("faultId");
            String handleDescription = (String) params.get("handleDescription");
            
            boolean success = faultReportService.completeFaultReport(faultId, handleDescription);
            return success ? AjaxResult.success("完成成功") : AjaxResult.error("完成失败");
        } catch (Exception e) {
            log.error("完成故障处理异常：", e);
            return AjaxResult.error("完成故障处理失败：" + e.getMessage());
        }
    }
    
    /**
     * 关闭故障申报
     */
    @PreAuthorize("@ss.hasPermi('maintenance:fault:close')")
    @PostMapping("/close")
    public AjaxResult closeFaultReport(@RequestBody Map<String, Object> params) {
        try {
            String faultId = (String) params.get("faultId");
            String closeReason = (String) params.get("closeReason");
            
            boolean success = faultReportService.closeFaultReport(faultId, closeReason);
            return success ? AjaxResult.success("关闭成功") : AjaxResult.error("关闭失败");
        } catch (Exception e) {
            log.error("关闭故障申报异常：", e);
            return AjaxResult.error("关闭故障申报失败：" + e.getMessage());
        }
    }
    
    /**
     * 查询紧急故障申报
     */
    @PreAuthorize("@ss.hasPermi('maintenance:fault:list')")
    @GetMapping("/urgent")
    public AjaxResult getUrgentFaultReports() {
        try {
            List<FaultReportVo> faultReports = faultReportService.getUrgentFaultReports();
            return AjaxResult.success(faultReports);
        } catch (Exception e) {
            log.error("查询紧急故障申报异常：", e);
            return AjaxResult.error("查询紧急故障申报失败：" + e.getMessage());
        }
    }
    
    /**
     * 查询未处理的故障申报
     */
    @PreAuthorize("@ss.hasPermi('maintenance:fault:list')")
    @GetMapping("/unhandled")
    public AjaxResult getUnhandledFaultReports() {
        try {
            List<FaultReportVo> faultReports = faultReportService.getUnhandledFaultReports();
            return AjaxResult.success(faultReports);
        } catch (Exception e) {
            log.error("查询未处理故障申报异常：", e);
            return AjaxResult.error("查询未处理故障申报失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据资产ID查询故障申报
     */
    @GetMapping("/asset/{assetId}")
    public AjaxResult getFaultReportsByAssetId(@PathVariable("assetId") String assetId) {
        try {
            List<FaultReportVo> faultReports = faultReportService.getFaultReportsByAssetId(assetId);
            return AjaxResult.success(faultReports);
        } catch (Exception e) {
            log.error("根据资产ID查询故障申报异常：", e);
            return AjaxResult.error("查询故障申报失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取故障申报统计信息
     */
    @PreAuthorize("@ss.hasPermi('maintenance:fault:list')")
    @GetMapping("/statistics")
    public AjaxResult getFaultReportStatistics() {
        try {
            Map<String, Integer> statistics = faultReportService.getFaultReportStatistics();
            return AjaxResult.success(statistics);
        } catch (Exception e) {
            log.error("获取故障申报统计信息异常：", e);
            return AjaxResult.error("获取统计信息失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取故障类型选项
     */
    @GetMapping("/types")
    public AjaxResult getFaultTypeOptions() {
        try {
            List<Map<String, Object>> options = faultReportService.getFaultTypeOptions();
            return AjaxResult.success(options);
        } catch (Exception e) {
            log.error("获取故障类型选项异常：", e);
            return AjaxResult.error("获取故障类型选项失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取紧急程度选项
     */
    @GetMapping("/urgency-levels")
    public AjaxResult getUrgencyLevelOptions() {
        try {
            List<Map<String, Object>> options = faultReportService.getUrgencyLevelOptions();
            return AjaxResult.success(options);
        } catch (Exception e) {
            log.error("获取紧急程度选项异常：", e);
            return AjaxResult.error("获取紧急程度选项失败：" + e.getMessage());
        }
    }
}
