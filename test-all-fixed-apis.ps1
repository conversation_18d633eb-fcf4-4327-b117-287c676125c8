# 测试所有已修复的库存消耗报表接口
$token = "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiZjM0NDU2YTktYmU2Mi00MTY1LTk4ZjUtN2VhODNlM2VhNjBhIn0.xBoYuAG7vY_hqXeL7Siz_xiIG3ZjEOh_49L5cmvIFUvTHGWul0C33aZWLUbhXj6a4NHWO5aiIggbxC6MXerh_A"

Write-Host "=== 测试所有已修复的库存消耗报表接口 ===" -ForegroundColor Green
Write-Host "测试时间: $(Get-Date)" -ForegroundColor Yellow
Write-Host ""

# 测试函数
function Test-API {
    param(
        [string]$Method,
        [string]$Url,
        [string]$Description,
        [string]$Body = $null
    )
    
    Write-Host "测试: $Description" -ForegroundColor Cyan
    
    try {
        $headers = @{
            "Authorization" = "Bearer $token"
            "Content-Type" = "application/json"
        }
        
        if ($Body) {
            $response = Invoke-RestMethod -Uri $Url -Method $Method -Headers $headers -Body $Body
        } else {
            $response = Invoke-RestMethod -Uri $Url -Method $Method -Headers $headers
        }
        
        Write-Host "✅ 成功 - $($response.msg)" -ForegroundColor Green
        if ($response.data -and $response.data.Count) {
            Write-Host "   数据条数: $($response.data.Count)" -ForegroundColor White
        } elseif ($response.data -eq $null -or ($response.data -is [array] -and $response.data.Count -eq 0)) {
            Write-Host "   返回空数据（正常，因为没有实际数据）" -ForegroundColor Yellow
        }
        Write-Host ""
        return $true
    }
    catch {
        Write-Host "❌ 失败 - $($_.Exception.Message)" -ForegroundColor Red
        if ($_.Exception.Response) {
            $statusCode = $_.Exception.Response.StatusCode
            Write-Host "   状态码: $statusCode" -ForegroundColor Red
        }
        Write-Host ""
        return $false
    }
}

# 1. 测试消耗趋势数据接口（已修复GROUP BY问题）
Write-Host "=== 1. 消耗趋势数据接口测试 ===" -ForegroundColor Yellow
$url1 = "http://localhost:8080/item/consumption-report/trend?itemId=TEST_ITEM&startDate=2024-01-01&endDate=2024-12-31&timeDimension=3"
Test-API -Method "GET" -Url $url1 -Description "查询月度消耗趋势数据"

# 2. 测试消耗效率排行榜接口（已修复SQL语法问题）
Write-Host "=== 2. 消耗效率排行榜接口测试 ===" -ForegroundColor Yellow
$body2 = @{
    startDate = "2024-01-01"
    endDate = "2024-12-31"
    analysisPeriod = "monthly"
} | ConvertTo-Json

$url2 = "http://localhost:8080/item/consumption-report/efficiency-ranking?limit=5"
Test-API -Method "POST" -Url $url2 -Description "查询消耗效率排行榜" -Body $body2

# 3. 测试消耗量排行榜接口（已修复SQL语法问题）
Write-Host "=== 3. 消耗量排行榜接口测试 ===" -ForegroundColor Yellow
$body3 = @{
    startDate = "2024-01-01"
    endDate = "2024-12-31"
} | ConvertTo-Json

$url3 = "http://localhost:8080/item/consumption-report/consumption-ranking?limit=5"
Test-API -Method "POST" -Url $url3 -Description "查询消耗量排行榜" -Body $body3

# 4. 测试其他基础接口
Write-Host "=== 4. 其他基础接口测试 ===" -ForegroundColor Yellow

# 4.1 获取优化建议
$url4_1 = "http://localhost:8080/item/consumption-report/optimization-suggestions?itemId=TEST_ITEM&analysisPeriod=monthly"
Test-API -Method "GET" -Url $url4_1 -Description "获取效率优化建议"

# 4.2 生成消耗汇总数据
$url4_2 = "http://localhost:8080/item/consumption-report/generate-summary?startDate=2024-01-01&endDate=2024-12-31"
Test-API -Method "POST" -Url $url4_2 -Description "生成消耗汇总数据"

# 4.3 查询消耗报表列表
$body4_3 = @{
    pageNum = 1
    pageSize = 10
    startDate = "2024-01-01"
    endDate = "2024-12-31"
} | ConvertTo-Json

$url4_3 = "http://localhost:8080/item/consumption-report/list"
Test-API -Method "POST" -Url $url4_3 -Description "分页查询消耗报表数据" -Body $body4_3

# 4.4 查询消耗统计数据
$body4_4 = @{
    startDate = "2024-01-01"
    endDate = "2024-12-31"
    analysisPeriod = "monthly"
} | ConvertTo-Json

$url4_4 = "http://localhost:8080/item/consumption-report/statistics"
Test-API -Method "POST" -Url $url4_4 -Description "查询消耗统计数据" -Body $body4_4

# 4.5 生成图表数据
$body4_5 = @{
    startDate = "2024-01-01"
    endDate = "2024-12-31"
    timeDimension = 3
} | ConvertTo-Json

$url4_5 = "http://localhost:8080/item/consumption-report/chart-data"
Test-API -Method "POST" -Url $url4_5 -Description "生成图表数据" -Body $body4_5

Write-Host "=== 测试完成 ===" -ForegroundColor Green
Write-Host "所有接口测试完毕，请检查上述结果" -ForegroundColor Yellow
Write-Host "如果返回空数据是正常的，因为数据库中可能没有实际的消耗数据" -ForegroundColor Yellow
