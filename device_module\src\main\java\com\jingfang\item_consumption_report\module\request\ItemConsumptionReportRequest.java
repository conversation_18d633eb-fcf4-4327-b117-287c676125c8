package com.jingfang.item_consumption_report.module.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 库存消耗报表查询请求参数
 */
@Data
public class ItemConsumptionReportRequest {
    
    /**
     * 页码
     */
    private Integer pageNum = 1;
    
    /**
     * 页大小
     */
    private Integer pageSize = 10;
    
    /**
     * 物品ID列表
     */
    private List<String> itemIds;
    
    /**
     * 物品名称（模糊查询）
     */
    private String itemName;
    
    /**
     * 物品编码（模糊查询）
     */
    private String itemCode;
    
    /**
     * 物品类型(1-消耗品,2-备品备件)
     */
    private Integer itemType;
    
    /**
     * 仓库ID列表
     */
    private List<Integer> warehouseIds;
    
    /**
     * 消耗类型列表(1-生产消耗,2-维护消耗,3-领用消耗,4-其他消耗)
     */
    private List<Integer> consumptionTypes;
    
    /**
     * 开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;
    
    /**
     * 结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;
    
    /**
     * 分析周期(daily/weekly/monthly/quarterly/yearly)
     */
    private String analysisPeriod;
    
    /**
     * 时间维度(1-日,2-周,3-月,4-季度,5-年)
     */
    private Integer timeDimension;
    
    /**
     * 效率等级过滤(excellent/good/average/poor)
     */
    private List<String> efficiencyLevels;
    
    /**
     * 最小消耗量
     */
    private Double minConsumption;
    
    /**
     * 最大消耗量
     */
    private Double maxConsumption;
    
    /**
     * 最小效率评分
     */
    private Double minEfficiencyScore;
    
    /**
     * 最大效率评分
     */
    private Double maxEfficiencyScore;
    
    /**
     * 部门名称（用于领用消耗分析）
     */
    private String deptName;
    
    /**
     * 领用人姓名（用于领用消耗分析）
     */
    private String recipientName;
    
    /**
     * 排序字段
     */
    private String orderBy;
    
    /**
     * 排序方向(asc/desc)
     */
    private String orderDirection = "desc";
    
    /**
     * 是否包含效率分析
     */
    private Boolean includeEfficiencyAnalysis = false;
    
    /**
     * 是否包含趋势分析
     */
    private Boolean includeTrendAnalysis = false;
    
    /**
     * 是否包含对比分析
     */
    private Boolean includeComparisonAnalysis = false;
}
