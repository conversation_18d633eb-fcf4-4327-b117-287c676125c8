package com.jingfang.maintenance_report.module.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 维护报表请求DTO
 */
@Data
public class MaintenanceReportRequest {
    
    /**
     * 报表类型(1-完成率,2-响应时间,3-成本分析,4-综合报表)
     */
    @NotNull(message = "报表类型不能为空")
    private Integer reportType;
    
    /**
     * 开始日期
     */
    @NotNull(message = "开始日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startDate;
    
    /**
     * 结束日期
     */
    @NotNull(message = "结束日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endDate;
    
    /**
     * 时间维度(1-日,2-周,3-月,4-季,5-年)
     */
    private Integer timeDimension;
    
    /**
     * 部门ID
     */
    private String deptId;
    
    /**
     * 部门ID列表（多选）
     */
    private List<String> deptIds;
    
    /**
     * 资产ID
     */
    private String assetId;
    
    /**
     * 资产ID列表（多选）
     */
    private List<String> assetIds;
    
    /**
     * 资产类型
     */
    private String assetType;
    
    /**
     * 资产类型列表（多选）
     */
    private List<String> assetTypes;
    
    /**
     * 任务优先级(1-低,2-中,3-高,4-紧急)
     */
    private Integer priority;
    
    /**
     * 任务优先级列表（多选）
     */
    private List<Integer> priorities;
    
    /**
     * 故障类型(1-电气故障,2-机械故障,3-控制系统故障,4-安全故障,5-其他故障)
     */
    private Integer faultType;
    
    /**
     * 故障类型列表（多选）
     */
    private List<Integer> faultTypes;
    
    /**
     * 紧急程度(1-一般,2-较急,3-紧急,4-特急)
     */
    private Integer urgencyLevel;
    
    /**
     * 紧急程度列表（多选）
     */
    private List<Integer> urgencyLevels;
    
    /**
     * 处理人员ID
     */
    private Long handlerId;
    
    /**
     * 处理人员ID列表（多选）
     */
    private List<Long> handlerIds;
    
    /**
     * 成本类型(1-人工成本,2-材料成本,3-外包成本,4-停机成本)
     */
    private Integer costType;
    
    /**
     * 成本类型列表（多选）
     */
    private List<Integer> costTypes;
    
    /**
     * 是否包含已完成任务
     */
    private Boolean includeCompleted;
    
    /**
     * 是否包含逾期任务
     */
    private Boolean includeOverdue;
    
    /**
     * 是否包含预防性维护
     */
    private Boolean includePreventive;
    
    /**
     * 是否包含故障维护
     */
    private Boolean includeCorrective;
    
    /**
     * 最小成本金额
     */
    private Double minCostAmount;
    
    /**
     * 最大成本金额
     */
    private Double maxCostAmount;
    
    /**
     * 图表类型(line-折线图,bar-柱状图,pie-饼图,mixed-混合图)
     */
    private String chartType;
    
    /**
     * 是否显示图例
     */
    private Boolean showLegend;
    
    /**
     * 图表颜色配置
     */
    private List<String> chartColors;
    
    /**
     * 导出格式(excel,pdf,csv)
     */
    private String exportFormat;
    
    /**
     * 是否包含图表
     */
    private Boolean includeChart;
    
    /**
     * 自定义筛选条件
     */
    private Map<String, Object> filters;
    
    /**
     * 排序字段
     */
    private String orderBy;
    
    /**
     * 排序方向(asc,desc)
     */
    private String orderDirection;
    
    /**
     * 分页页码
     */
    private Integer pageNum;
    
    /**
     * 分页大小
     */
    private Integer pageSize;
    
    /**
     * 报表类型枚举
     */
    public enum ReportType {
        TASK_COMPLETION(1, "任务完成率报表"),
        FAULT_RESPONSE(2, "故障响应时间报表"),
        MAINTENANCE_COST(3, "维护成本分析报表"),
        COMPREHENSIVE(4, "综合报表");
        
        private final Integer code;
        private final String description;
        
        ReportType(Integer code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public Integer getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
        
        public static ReportType fromCode(Integer code) {
            for (ReportType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }
    
    /**
     * 时间维度枚举
     */
    public enum TimeDimension {
        DAILY(1, "日"),
        WEEKLY(2, "周"),
        MONTHLY(3, "月"),
        QUARTERLY(4, "季"),
        YEARLY(5, "年");
        
        private final Integer code;
        private final String description;
        
        TimeDimension(Integer code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public Integer getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
        
        public static TimeDimension fromCode(Integer code) {
            for (TimeDimension dimension : values()) {
                if (dimension.code.equals(code)) {
                    return dimension;
                }
            }
            return null;
        }
    }
}
