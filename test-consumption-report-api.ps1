# 库存消耗报表API测试脚本
# 测试所有库存消耗报表相关的接口

# 配置参数
$baseUrl = "http://localhost:8080"
$token = "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiZjM0NDU2YTktYmU2Mi00MTY1LTk4ZjUtN2VhODNlM2VhNjBhIn0.xBoYuAG7vY_hqXeL7Siz_xiIG3ZjEOh_49L5cmvIFUvTHGWul0C33aZWLUbhXj6a4NHWO5aiIggbxC6MXerh_A"

# 设置请求头
$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

Write-Host "=== 库存消耗报表API测试开始 ===" -ForegroundColor Green
Write-Host "服务地址: $baseUrl" -ForegroundColor Yellow
Write-Host "Token: $($token.Substring(0,50))..." -ForegroundColor Yellow
Write-Host ""

# 测试函数
function Test-API {
    param(
        [string]$Method,
        [string]$Url,
        [string]$Description,
        [object]$Body = $null
    )
    
    Write-Host "测试: $Description" -ForegroundColor Cyan
    Write-Host "请求: $Method $Url" -ForegroundColor Gray
    
    try {
        if ($Body) {
            $jsonBody = $Body | ConvertTo-Json -Depth 10
            Write-Host "请求体: $jsonBody" -ForegroundColor Gray
            $response = Invoke-RestMethod -Uri $Url -Method $Method -Headers $headers -Body $jsonBody
        } else {
            $response = Invoke-RestMethod -Uri $Url -Method $Method -Headers $headers
        }
        
        Write-Host "✅ 成功" -ForegroundColor Green
        Write-Host "响应: $($response | ConvertTo-Json -Depth 3)" -ForegroundColor White
        Write-Host ""
        return $true
    }
    catch {
        Write-Host "❌ 失败" -ForegroundColor Red
        Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
        if ($_.Exception.Response) {
            $statusCode = $_.Exception.Response.StatusCode
            Write-Host "状态码: $statusCode" -ForegroundColor Red
        }
        Write-Host ""
        return $false
    }
}

# 1. 测试数据管理接口 - 生成消耗汇总数据
Write-Host "=== 1. 数据管理接口测试 ===" -ForegroundColor Yellow

$generateSummaryUrl = "$baseUrl/item/consumption-report/generate-summary?startDate=2024-01-01&endDate=2024-12-31"
Test-API -Method "POST" -Url $generateSummaryUrl -Description "生成消耗汇总数据"

# 2. 测试分页查询消耗报表数据
Write-Host "=== 2. 消耗报表查询接口测试 ===" -ForegroundColor Yellow

$listRequest = @{
    pageNum = 1
    pageSize = 10
    startDate = "2024-01-01"
    endDate = "2024-12-31"
    timeDimension = 3
    includeEfficiencyAnalysis = $true
    includeTrendAnalysis = $false
    includeComparisonAnalysis = $false
}

$listUrl = "$baseUrl/item/consumption-report/list"
Test-API -Method "POST" -Url $listUrl -Description "分页查询消耗报表数据" -Body $listRequest

# 3. 测试消耗统计数据查询
$statisticsRequest = @{
    startDate = "2024-01-01"
    endDate = "2024-12-31"
    analysisPeriod = "monthly"
}

$statisticsUrl = "$baseUrl/item/consumption-report/statistics"
Test-API -Method "POST" -Url $statisticsUrl -Description "查询消耗统计数据" -Body $statisticsRequest

# 4. 测试图表数据生成
$chartRequest = @{
    startDate = "2024-01-01"
    endDate = "2024-12-31"
    timeDimension = 3
}

$chartUrl = "$baseUrl/item/consumption-report/chart-data"
Test-API -Method "POST" -Url $chartUrl -Description "生成图表数据" -Body $chartRequest

# 5. 测试效率排行榜
$efficiencyRankingRequest = @{
    startDate = "2024-01-01"
    endDate = "2024-12-31"
    analysisPeriod = "monthly"
}

$efficiencyRankingUrl = "$baseUrl/item/consumption-report/efficiency-ranking?limit=5"
Test-API -Method "POST" -Url $efficiencyRankingUrl -Description "查询消耗效率排行榜" -Body $efficiencyRankingRequest

# 6. 测试消耗量排行榜
$consumptionRankingRequest = @{
    startDate = "2024-01-01"
    endDate = "2024-12-31"
}

$consumptionRankingUrl = "$baseUrl/item/consumption-report/consumption-ranking?limit=5"
Test-API -Method "POST" -Url $consumptionRankingUrl -Description "查询消耗量排行榜" -Body $consumptionRankingRequest

# 7. 测试分析报告生成
$analysisRequest = @{
    startDate = "2024-01-01"
    endDate = "2024-12-31"
    analysisPeriod = "monthly"
    includeEfficiencyAnalysis = $true
}

$analysisUrl = "$baseUrl/item/consumption-report/analysis-report"
Test-API -Method "POST" -Url $analysisUrl -Description "生成消耗分析报告" -Body $analysisRequest

# 8. 测试预警数据获取
$alertsRequest = @{
    startDate = "2024-01-01"
    endDate = "2024-12-31"
    maxEfficiencyScore = 60.0
}

$alertsUrl = "$baseUrl/item/consumption-report/alerts"
Test-API -Method "POST" -Url $alertsUrl -Description "获取消耗预警数据" -Body $alertsRequest

# 9. 测试优化建议获取
$suggestionsUrl = "$baseUrl/item/consumption-report/optimization-suggestions?itemId=TEST_ITEM_001&analysisPeriod=monthly"
Test-API -Method "GET" -Url $suggestionsUrl -Description "获取效率优化建议"

# 10. 测试导出功能
$exportRequest = @{
    startDate = "2024-01-01"
    endDate = "2024-12-31"
    pageNum = 1
    pageSize = 100
}

$exportUrl = "$baseUrl/item/consumption-report/export"
Test-API -Method "POST" -Url $exportUrl -Description "导出消耗报表数据" -Body $exportRequest

Write-Host "=== 库存消耗报表API测试完成 ===" -ForegroundColor Green
Write-Host "请检查上述测试结果，确认接口功能是否正常" -ForegroundColor Yellow
