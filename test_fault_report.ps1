# 故障申报功能测试脚本

$baseUrl = "http://localhost:8080"

Write-Host "=== 故障申报功能测试 ===" -ForegroundColor Green

# 1. 测试服务器连接
Write-Host "`n1. 测试服务器连接..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl" -Method GET -TimeoutSec 5
    Write-Host "✓ 服务器连接正常" -ForegroundColor Green
    Write-Host "响应: $response" -ForegroundColor Gray
} catch {
    Write-Host "✗ 服务器连接失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 2. 测试验证码接口
Write-Host "`n2. 测试验证码接口..." -ForegroundColor Yellow
try {
    $captchaResponse = Invoke-RestMethod -Uri "$baseUrl/captchaImage" -Method GET -TimeoutSec 5
    Write-Host "✓ 验证码接口正常" -ForegroundColor Green
    Write-Host "验证码状态: $($captchaResponse.captchaEnabled)" -ForegroundColor Gray
} catch {
    Write-Host "✗ 验证码接口失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 3. 尝试登录
Write-Host "`n3. 尝试登录..." -ForegroundColor Yellow
$loginBody = @{
    username = "admin"
    password = "admin123"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/login" -Method POST -ContentType "application/json" -Body $loginBody -TimeoutSec 10
    Write-Host "✓ 登录成功" -ForegroundColor Green
    Write-Host "登录响应: $($loginResponse | ConvertTo-Json -Depth 2)" -ForegroundColor Gray
    
    # 提取token
    $token = $loginResponse.token
    if ($token) {
        Write-Host "Token: $token" -ForegroundColor Cyan
        
        # 4. 测试故障申报接口
        Write-Host "`n4. 测试故障申报接口..." -ForegroundColor Yellow
        
        # 设置认证头
        $headers = @{
            "Authorization" = "Bearer $token"
            "Content-Type" = "application/json"
        }
        
        # 测试获取故障类型选项
        try {
            $faultTypesResponse = Invoke-RestMethod -Uri "$baseUrl/maintenance/fault/types" -Method GET -Headers $headers -TimeoutSec 5
            Write-Host "✓ 故障类型接口正常" -ForegroundColor Green
            Write-Host "故障类型: $($faultTypesResponse.data | ConvertTo-Json -Compress)" -ForegroundColor Gray
        } catch {
            Write-Host "✗ 故障类型接口失败: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        # 测试获取紧急程度选项
        try {
            $urgencyLevelsResponse = Invoke-RestMethod -Uri "$baseUrl/maintenance/fault/urgency-levels" -Method GET -Headers $headers -TimeoutSec 5
            Write-Host "✓ 紧急程度接口正常" -ForegroundColor Green
            Write-Host "紧急程度: $($urgencyLevelsResponse.data | ConvertTo-Json -Compress)" -ForegroundColor Gray
        } catch {
            Write-Host "✗ 紧急程度接口失败: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        # 测试基础功能检查
        try {
            $checkResponse = Invoke-RestMethod -Uri "$baseUrl/maintenance/fault/test/check" -Method GET -Headers $headers -TimeoutSec 5
            Write-Host "✓ 基础功能检查正常" -ForegroundColor Green
            Write-Host "检查结果: $($checkResponse.msg)" -ForegroundColor Gray
        } catch {
            Write-Host "✗ 基础功能检查失败: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        # 测试故障申报提交
        Write-Host "`n5. 测试故障申报提交..." -ForegroundColor Yellow
        $faultReportBody = @{
            assetId = "AS20230001"
            assetName = "测试设备"
            faultTitle = "测试故障申报"
            faultDescription = "这是一个API测试故障申报"
            faultType = 2
            urgencyLevel = 3
            reportLocation = "测试位置"
            contactPhone = "13800138000"
            images = @("test1.jpg", "test2.jpg")
            remark = "API测试"
        } | ConvertTo-Json
        
        try {
            $submitResponse = Invoke-RestMethod -Uri "$baseUrl/maintenance/fault/report" -Method POST -Headers $headers -Body $faultReportBody -TimeoutSec 10
            Write-Host "✓ 故障申报提交成功" -ForegroundColor Green
            Write-Host "申报结果: $($submitResponse | ConvertTo-Json -Depth 2)" -ForegroundColor Gray
            
            # 测试查询我的故障申报
            Write-Host "`n6. 测试查询我的故障申报..." -ForegroundColor Yellow
            try {
                $myReportsResponse = Invoke-RestMethod -Uri "$baseUrl/maintenance/fault/my-reports" -Method GET -Headers $headers -TimeoutSec 5
                Write-Host "✓ 查询我的故障申报成功" -ForegroundColor Green
                Write-Host "申报数量: $($myReportsResponse.data.total)" -ForegroundColor Gray
            } catch {
                Write-Host "✗ 查询我的故障申报失败: $($_.Exception.Message)" -ForegroundColor Red
            }
            
        } catch {
            Write-Host "✗ 故障申报提交失败: $($_.Exception.Message)" -ForegroundColor Red
            if ($_.Exception.Response) {
                $errorStream = $_.Exception.Response.GetResponseStream()
                $reader = New-Object System.IO.StreamReader($errorStream)
                $errorBody = $reader.ReadToEnd()
                Write-Host "错误详情: $errorBody" -ForegroundColor Red
            }
        }
        
    } else {
        Write-Host "✗ 未获取到token" -ForegroundColor Red
    }
    
} catch {
    Write-Host "✗ 登录失败: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $errorStream = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($errorStream)
        $errorBody = $reader.ReadToEnd()
        Write-Host "登录错误详情: $errorBody" -ForegroundColor Red
    }
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
