package com.jingfang.item_consumption_report.module.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 库存消耗报表展示对象
 */
@Data
public class ItemConsumptionReportVo {
    
    /**
     * 物品ID
     */
    private String itemId;
    
    /**
     * 物品名称
     */
    private String itemName;
    
    /**
     * 物品编码
     */
    private String itemCode;
    
    /**
     * 物品类型
     */
    private Integer itemType;
    
    /**
     * 物品类型名称
     */
    private String itemTypeName;
    
    /**
     * 规格型号
     */
    private String specModel;
    
    /**
     * 单位
     */
    private String unit;
    
    /**
     * 仓库ID
     */
    private Integer warehouseId;
    
    /**
     * 仓库名称
     */
    private String warehouseName;
    
    /**
     * 统计周期开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date periodStartDate;
    
    /**
     * 统计周期结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date periodEndDate;
    
    /**
     * 总消耗数量
     */
    private BigDecimal totalConsumption;
    
    /**
     * 总消耗金额
     */
    private BigDecimal totalAmount;
    
    /**
     * 平均单价
     */
    private BigDecimal avgUnitPrice;
    
    /**
     * 消耗次数
     */
    private Integer consumptionCount;
    
    /**
     * 生产消耗数量
     */
    private BigDecimal productionConsumption;
    
    /**
     * 维护消耗数量
     */
    private BigDecimal maintenanceConsumption;
    
    /**
     * 领用消耗数量
     */
    private BigDecimal requisitionConsumption;
    
    /**
     * 其他消耗数量
     */
    private BigDecimal otherConsumption;
    
    /**
     * 关联生产产量
     */
    private BigDecimal relatedProductionOutput;
    
    /**
     * 单位产量消耗
     */
    private BigDecimal consumptionPerUnit;
    
    /**
     * 效率评分(0-100)
     */
    private BigDecimal efficiencyScore;
    
    /**
     * 效率等级
     */
    private String efficiencyLevel;
    
    /**
     * 效率等级名称
     */
    private String efficiencyLevelName;
    
    /**
     * 基准消耗量
     */
    private BigDecimal benchmarkConsumption;
    
    /**
     * 效率偏差
     */
    private BigDecimal efficiencyVariance;
    
    /**
     * 效率偏差百分比
     */
    private BigDecimal efficiencyVariancePercent;
    
    /**
     * 单位产量成本
     */
    private BigDecimal costPerUnit;
    
    /**
     * 节约金额
     */
    private BigDecimal savingsAmount;
    
    /**
     * 优化建议
     */
    private String optimizationSuggestions;
    
    /**
     * 消耗趋势数据
     */
    private List<ConsumptionTrendVo> trendData;
    
    /**
     * 消耗类型分布数据
     */
    private List<ConsumptionTypeVo> typeDistribution;
    
    /**
     * 消耗趋势数据内部类
     */
    @Data
    public static class ConsumptionTrendVo {
        /**
         * 日期
         */
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        private Date date;
        
        /**
         * 日期标签（用于图表显示）
         */
        private String dateLabel;
        
        /**
         * 消耗数量
         */
        private BigDecimal consumption;
        
        /**
         * 消耗金额
         */
        private BigDecimal amount;
        
        /**
         * 单位产量消耗
         */
        private BigDecimal consumptionPerUnit;
        
        /**
         * 效率评分
         */
        private BigDecimal efficiencyScore;
    }
    
    /**
     * 消耗类型分布数据内部类
     */
    @Data
    public static class ConsumptionTypeVo {
        /**
         * 消耗类型
         */
        private Integer consumptionType;
        
        /**
         * 消耗类型名称
         */
        private String consumptionTypeName;
        
        /**
         * 消耗数量
         */
        private BigDecimal consumption;
        
        /**
         * 消耗金额
         */
        private BigDecimal amount;
        
        /**
         * 占比百分比
         */
        private BigDecimal percentage;
    }
}
