package com.jingfang.fault_report.module.dto;

import com.jingfang.fault_report.module.entity.FaultReport;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 资产故障申报DTO
 */
@Data
public class FaultReportDto {
    
    /**
     * 故障申报ID
     */
    private String faultId;
    
    /**
     * 故障标题
     */
    @NotBlank(message = "故障标题不能为空")
    private String faultTitle;
    
    /**
     * 故障描述
     */
    @NotBlank(message = "故障描述不能为空")
    private String faultDescription;
    
    /**
     * 资产ID
     */
    @NotBlank(message = "资产ID不能为空")
    private String assetId;
    
    /**
     * 资产名称（用于显示）
     */
    private String assetName;
    
    /**
     * 故障类型(1-电气故障, 2-机械故障, 3-控制系统故障, 4-安全故障, 5-其他故障)
     */
    @NotNull(message = "故障类型不能为空")
    private Integer faultType;
    
    /**
     * 紧急程度(1-一般, 2-较急, 3-紧急, 4-特急)
     */
    @NotNull(message = "紧急程度不能为空")
    private Integer urgencyLevel;
    
    /**
     * 申报位置
     */
    private String reportLocation;
    
    /**
     * 联系电话
     */
    private String contactPhone;
    
    /**
     * 故障图片列表
     */
    private List<String> images;
    
    /**
     * 备注说明
     */
    private String remark;
    
    /**
     * 转换为实体类
     */
    public FaultReport toEntity() {
        FaultReport faultReport = new FaultReport();
        faultReport.setFaultId(this.faultId);
        faultReport.setFaultTitle(this.faultTitle);
        faultReport.setFaultDescription(this.faultDescription);
        faultReport.setAssetId(this.assetId);
        faultReport.setFaultType(this.faultType);
        faultReport.setUrgencyLevel(this.urgencyLevel);
        faultReport.setReportLocation(this.reportLocation);
        faultReport.setContactPhone(this.contactPhone);
        faultReport.setRemark(this.remark);
        return faultReport;
    }
}
