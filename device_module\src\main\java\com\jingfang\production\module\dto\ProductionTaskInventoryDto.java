package com.jingfang.production.module.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 生产任务库存关联DTO
 */
@Data
public class ProductionTaskInventoryDto implements Serializable {
    
    /**
     * 关联ID
     */
    private String relationId;
    
    /**
     * 任务ID
     */
    @NotBlank(message = "任务ID不能为空")
    private String taskId;
    
    /**
     * 物品ID
     */
    @NotBlank(message = "物品ID不能为空")
    private String itemId;
    
    /**
     * 物品编码
     */
    private String itemCode;
    
    /**
     * 物品名称
     */
    private String itemName;
    
    /**
     * 预计使用数量
     */
    @NotNull(message = "预计使用数量不能为空")
    private BigDecimal plannedQuantity;
    
    /**
     * 备注
     */
    private String remark;
    
    private static final long serialVersionUID = 1L;
}
