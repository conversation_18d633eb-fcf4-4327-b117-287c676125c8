package com.jingfang.web.controller.production;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.common.annotation.Log;
import com.jingfang.common.core.controller.BaseController;
import com.jingfang.common.core.domain.AjaxResult;
import com.jingfang.common.core.page.TableDataInfo;
import com.jingfang.common.enums.BusinessType;
import com.jingfang.production.module.dto.ProductionTaskDto;
import com.jingfang.production.module.dto.ProductionTaskQueryDto;
import com.jingfang.production.module.service.IProductionTaskService;
import com.jingfang.production.module.vo.ProductionTaskVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 生产任务Controller
 */
@Slf4j
@RestController
@RequestMapping("/production/task")
public class ProductionTaskController extends BaseController {

    @Autowired
    private IProductionTaskService productionTaskService;

    /**
     * 查询生产任务列表
     */
    @PreAuthorize("@ss.hasPermi('production:task:query')")
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody ProductionTaskQueryDto queryDto) {
        try {
            IPage<ProductionTaskVo> page = productionTaskService.selectProductionTaskList(queryDto);
            TableDataInfo dataTable = new TableDataInfo();
            dataTable.setCode(200);
            dataTable.setMsg("查询成功");
            dataTable.setRows(page.getRecords());
            dataTable.setTotal(page.getTotal());
            return dataTable;
        } catch (Exception e) {
            log.error("查询生产任务列表失败", e);
            return getDataTable(List.of());
        }
    }

    /**
     * 获取生产任务详细信息
     */
    @PreAuthorize("@ss.hasPermi('production:task:query')")
    @GetMapping("/{taskId}")
    public AjaxResult getInfo(@PathVariable String taskId) {
        try {
            ProductionTaskVo taskVo = productionTaskService.selectProductionTaskById(taskId);
            return success(taskVo);
        } catch (Exception e) {
            log.error("查询生产任务详情失败，任务ID：{}", taskId, e);
            return error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 新增生产任务
     */
    @PreAuthorize("@ss.hasPermi('production:task:add')")
    @Log(title = "生产任务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody ProductionTaskDto taskDto) {
        try {
            boolean result = productionTaskService.insertProductionTask(taskDto, getUsername());
            return toAjax(result);
        } catch (Exception e) {
            log.error("新增生产任务失败", e);
            return error("新增失败：" + e.getMessage());
        }
    }

    /**
     * 修改生产任务
     */
    @PreAuthorize("@ss.hasPermi('production:task:edit')")
    @Log(title = "生产任务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody ProductionTaskDto taskDto) {
        try {
            boolean result = productionTaskService.updateProductionTask(taskDto, getUsername());
            return toAjax(result);
        } catch (Exception e) {
            log.error("修改生产任务失败，任务ID：{}", taskDto.getTaskId(), e);
            return error("修改失败：" + e.getMessage());
        }
    }

    /**
     * 删除生产任务
     */
    @PreAuthorize("@ss.hasPermi('production:task:remove')")
    @Log(title = "生产任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{taskIds}")
    public AjaxResult remove(@PathVariable String[] taskIds) {
        try {
            boolean result = productionTaskService.deleteProductionTasks(List.of(taskIds));
            return toAjax(result);
        } catch (Exception e) {
            log.error("删除生产任务失败", e);
            return error("删除失败：" + e.getMessage());
        }
    }

    /**
     * 开始执行任务
     */
    @PreAuthorize("@ss.hasPermi('production:task:edit')")
    @Log(title = "生产任务", businessType = BusinessType.UPDATE)
    @PostMapping("/start/{taskId}")
    public AjaxResult startTask(@PathVariable String taskId) {
        try {
            boolean result = productionTaskService.startTask(taskId, getUsername());
            return toAjax(result);
        } catch (Exception e) {
            log.error("开始执行任务失败，任务ID：{}", taskId, e);
            return error("开始执行失败：" + e.getMessage());
        }
    }

    /**
     * 暂停任务
     */
    @PreAuthorize("@ss.hasPermi('production:task:edit')")
    @Log(title = "生产任务", businessType = BusinessType.UPDATE)
    @PostMapping("/pause/{taskId}")
    public AjaxResult pauseTask(@PathVariable String taskId) {
        try {
            boolean result = productionTaskService.pauseTask(taskId, getUsername());
            return toAjax(result);
        } catch (Exception e) {
            log.error("暂停任务失败，任务ID：{}", taskId, e);
            return error("暂停失败：" + e.getMessage());
        }
    }

    /**
     * 完成任务
     */
    @PreAuthorize("@ss.hasPermi('production:task:edit')")
    @Log(title = "生产任务", businessType = BusinessType.UPDATE)
    @PostMapping("/complete/{taskId}")
    public AjaxResult completeTask(@PathVariable String taskId) {
        try {
            boolean result = productionTaskService.completeTask(taskId, getUsername());
            return toAjax(result);
        } catch (Exception e) {
            log.error("完成任务失败，任务ID：{}", taskId, e);
            return error("完成失败：" + e.getMessage());
        }
    }

    /**
     * 取消任务
     */
    @PreAuthorize("@ss.hasPermi('production:task:edit')")
    @Log(title = "生产任务", businessType = BusinessType.UPDATE)
    @PostMapping("/cancel/{taskId}")
    public AjaxResult cancelTask(@PathVariable String taskId) {
        try {
            boolean result = productionTaskService.cancelTask(taskId, getUsername());
            return toAjax(result);
        } catch (Exception e) {
            log.error("取消任务失败，任务ID：{}", taskId, e);
            return error("取消失败：" + e.getMessage());
        }
    }

    /**
     * 更新任务进度
     */
    @PreAuthorize("@ss.hasPermi('production:task:edit')")
    @Log(title = "生产任务", businessType = BusinessType.UPDATE)
    @PostMapping("/progress/{taskId}")
    public AjaxResult updateProgress(@PathVariable String taskId, @RequestParam BigDecimal progressRate) {
        try {
            boolean result = productionTaskService.updateTaskProgress(taskId, progressRate);
            return toAjax(result);
        } catch (Exception e) {
            log.error("更新任务进度失败，任务ID：{}", taskId, e);
            return error("更新进度失败：" + e.getMessage());
        }
    }

    /**
     * 分配任务
     */
    @PreAuthorize("@ss.hasPermi('production:task:edit')")
    @Log(title = "生产任务", businessType = BusinessType.UPDATE)
    @PostMapping("/assign/{taskId}")
    public AjaxResult assignTask(@PathVariable String taskId, @RequestParam Long responsibleUserId) {
        try {
            boolean result = productionTaskService.assignTask(taskId, responsibleUserId, getUsername());
            return toAjax(result);
        } catch (Exception e) {
            log.error("分配任务失败，任务ID：{}", taskId, e);
            return error("分配失败：" + e.getMessage());
        }
    }

    /**
     * 查询任务状态统计
     */
    @PreAuthorize("@ss.hasPermi('production:task:query')")
    @GetMapping("/statistics/status")
    public AjaxResult getStatusStatistics() {
        try {
            List<Map<String, Object>> statistics = productionTaskService.selectTaskStatusStatistics();
            return success(statistics);
        } catch (Exception e) {
            log.error("查询任务状态统计失败", e);
            return error("查询统计失败：" + e.getMessage());
        }
    }

    /**
     * 查询任务进度统计
     */
    @PreAuthorize("@ss.hasPermi('production:task:query')")
    @GetMapping("/statistics/progress")
    public AjaxResult getProgressStatistics() {
        try {
            List<Map<String, Object>> statistics = productionTaskService.selectTaskProgressStatistics();
            return success(statistics);
        } catch (Exception e) {
            log.error("查询任务进度统计失败", e);
            return error("查询统计失败：" + e.getMessage());
        }
    }

    /**
     * 查询即将到期的任务
     */
    @PreAuthorize("@ss.hasPermi('production:task:query')")
    @GetMapping("/upcoming")
    public AjaxResult getUpcomingTasks(@RequestParam(defaultValue = "7") Integer days) {
        try {
            List<ProductionTaskVo> tasks = productionTaskService.selectUpcomingTasks(days);
            return success(tasks);
        } catch (Exception e) {
            log.error("查询即将到期任务失败", e);
            return error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 查询已过期的任务
     */
    @PreAuthorize("@ss.hasPermi('production:task:query')")
    @GetMapping("/overdue")
    public AjaxResult getOverdueTasks() {
        try {
            List<ProductionTaskVo> tasks = productionTaskService.selectOverdueTasks();
            return success(tasks);
        } catch (Exception e) {
            log.error("查询已过期任务失败", e);
            return error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 查询我的任务
     */
    @PreAuthorize("@ss.hasPermi('production:task:query')")
    @GetMapping("/my")
    public AjaxResult getMyTasks(@RequestParam(required = false) Integer status) {
        try {
            List<ProductionTaskVo> tasks = productionTaskService.selectTasksByResponsibleUser(getUserId(), status);
            return success(tasks);
        } catch (Exception e) {
            log.error("查询我的任务失败", e);
            return error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 查询任务资源使用情况
     */
    @PreAuthorize("@ss.hasPermi('production:task:query')")
    @GetMapping("/resource/{taskId}")
    public AjaxResult getTaskResourceUsage(@PathVariable String taskId) {
        try {
            Map<String, Object> usage = productionTaskService.selectTaskResourceUsage(taskId);
            return success(usage);
        } catch (Exception e) {
            log.error("查询任务资源使用情况失败，任务ID：{}", taskId, e);
            return error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 复制任务
     */
    @PreAuthorize("@ss.hasPermi('production:task:add')")
    @Log(title = "生产任务", businessType = BusinessType.INSERT)
    @PostMapping("/copy/{sourceTaskId}")
    public AjaxResult copyTask(@PathVariable String sourceTaskId, @RequestParam String taskName) {
        try {
            boolean result = productionTaskService.copyTask(sourceTaskId, taskName, getUsername());
            return toAjax(result);
        } catch (Exception e) {
            log.error("复制任务失败，源任务ID：{}", sourceTaskId, e);
            return error("复制失败：" + e.getMessage());
        }
    }

    /**
     * 导出任务数据
     */
    @PreAuthorize("@ss.hasPermi('production:task:export')")
    @Log(title = "生产任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public AjaxResult export(@RequestBody ProductionTaskQueryDto queryDto) {
        try {
            List<ProductionTaskVo> list = productionTaskService.exportTaskData(queryDto);
            // 这里应该调用Excel导出工具类
            return success("导出成功", list);
        } catch (Exception e) {
            log.error("导出任务数据失败", e);
            return error("导出失败：" + e.getMessage());
        }
    }
}