package com.jingfang.production.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 任务节点VO
 */
@Data
public class ProductionTaskNodeVo implements Serializable {
    
    /**
     * 节点ID
     */
    private String nodeId;
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 任务名称
     */
    private String taskName;
    
    /**
     * 节点名称
     */
    private String nodeName;
    
    /**
     * 节点类型(1-开始,2-处理,3-检验,4-决策,5-结束)
     */
    private Integer nodeType;
    
    /**
     * 节点类型名称
     */
    private String nodeTypeName;
    
    /**
     * 节点序号
     */
    private Integer sequenceNo;
    
    /**
     * 是否必需(1-是,0-否)
     */
    private Integer isRequired;
    
    /**
     * 是否必需名称
     */
    private String isRequiredName;
    
    /**
     * 预计耗时(分钟)
     */
    private Integer estimatedDuration;
    
    /**
     * 实际耗时(分钟)
     */
    private Integer actualDuration;
    
    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;
    
    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;
    
    /**
     * 状态(1-待执行,2-执行中,3-已完成,4-跳过,5-异常)
     */
    private Integer status;
    
    /**
     * 状态名称
     */
    private String statusName;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    
    /**
     * 附件数量
     */
    private Integer attachmentCount;
    
    /**
     * 是否可以执行（基于前置节点状态）
     */
    private Boolean canExecute;
    
    /**
     * 执行进度描述
     */
    private String progressDescription;
    
    private static final long serialVersionUID = 1L;
}
