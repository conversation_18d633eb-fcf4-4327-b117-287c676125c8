<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.production.mapper.ProductionTaskNodeMapper">
    
    <resultMap type="com.jingfang.production.vo.ProductionTaskNodeVo" id="ProductionTaskNodeVoResult">
        <result property="nodeId" column="node_id" />
        <result property="taskId" column="task_id" />
        <result property="taskName" column="task_name" />
        <result property="nodeName" column="node_name" />
        <result property="nodeType" column="node_type" />
        <result property="nodeTypeName" column="node_type_name" />
        <result property="sequenceNo" column="sequence_no" />
        <result property="isRequired" column="is_required" />
        <result property="isRequiredName" column="is_required_name" />
        <result property="estimatedDuration" column="estimated_duration" />
        <result property="actualDuration" column="actual_duration" />
        <result property="startTime" column="start_time" />
        <result property="endTime" column="end_time" />
        <result property="status" column="status" />
        <result property="statusName" column="status_name" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
        <result property="attachmentCount" column="attachment_count" />
        <result property="canExecute" column="can_execute" />
        <result property="progressDescription" column="progress_description" />
    </resultMap>
    
    <sql id="selectProductionTaskNodeVo">
        SELECT 
            ptn.node_id,
            ptn.task_id,
            pt.task_name,
            ptn.node_name,
            ptn.node_type,
            CASE ptn.node_type 
                WHEN 1 THEN '开始'
                WHEN 2 THEN '处理'
                WHEN 3 THEN '检验'
                WHEN 4 THEN '决策'
                WHEN 5 THEN '结束'
                ELSE '未知'
            END as node_type_name,
            ptn.sequence_no,
            ptn.is_required,
            CASE ptn.is_required 
                WHEN 1 THEN '是'
                WHEN 0 THEN '否'
                ELSE '未知'
            END as is_required_name,
            ptn.estimated_duration,
            ptn.actual_duration,
            ptn.start_time,
            ptn.end_time,
            ptn.status,
            CASE ptn.status 
                WHEN 1 THEN '待执行'
                WHEN 2 THEN '执行中'
                WHEN 3 THEN '已完成'
                WHEN 4 THEN '跳过'
                WHEN 5 THEN '异常'
                ELSE '未知'
            END as status_name,
            ptn.create_time,
            ptn.update_time,
            COALESCE(att_count.attachment_count, 0) as attachment_count,
            CASE 
                WHEN ptn.sequence_no = 1 THEN 1
                WHEN EXISTS (
                    SELECT 1 FROM production_task_node prev 
                    WHERE prev.task_id = ptn.task_id 
                    AND prev.sequence_no = ptn.sequence_no - 1 
                    AND prev.status = 3
                ) THEN 1
                ELSE 0
            END as can_execute,
            CASE 
                WHEN ptn.status = 1 THEN '等待执行'
                WHEN ptn.status = 2 THEN CONCAT('执行中，已用时', COALESCE(TIMESTAMPDIFF(MINUTE, ptn.start_time, NOW()), 0), '分钟')
                WHEN ptn.status = 3 THEN CONCAT('已完成，用时', COALESCE(ptn.actual_duration, 0), '分钟')
                WHEN ptn.status = 4 THEN '已跳过'
                WHEN ptn.status = 5 THEN '执行异常'
                ELSE '状态未知'
            END as progress_description
        FROM production_task_node ptn
        LEFT JOIN production_task pt ON ptn.task_id = pt.task_id
        LEFT JOIN (
            SELECT node_id, COUNT(*) as attachment_count
            FROM production_task_node_attachment 
            GROUP BY node_id
        ) att_count ON ptn.node_id = att_count.node_id
    </sql>
    
    <select id="selectNodesByTaskId" resultMap="ProductionTaskNodeVoResult">
        <include refid="selectProductionTaskNodeVo"/>
        WHERE ptn.task_id = #{taskId}
        ORDER BY ptn.sequence_no ASC
    </select>
    
    <select id="selectNodeById" resultMap="ProductionTaskNodeVoResult">
        <include refid="selectProductionTaskNodeVo"/>
        WHERE ptn.node_id = #{nodeId}
    </select>
    
    <select id="selectNodeStatisticsByTaskId" resultType="java.util.Map">
        SELECT 
            COUNT(*) as total_nodes,
            SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as pending_nodes,
            SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as executing_nodes,
            SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as completed_nodes,
            SUM(CASE WHEN status = 4 THEN 1 ELSE 0 END) as skipped_nodes,
            SUM(CASE WHEN status = 5 THEN 1 ELSE 0 END) as error_nodes,
            SUM(CASE WHEN is_required = 1 THEN 1 ELSE 0 END) as required_nodes,
            SUM(COALESCE(estimated_duration, 0)) as total_estimated_duration,
            SUM(COALESCE(actual_duration, 0)) as total_actual_duration
        FROM production_task_node
        WHERE task_id = #{taskId}
    </select>
    
    <select id="selectNodeStatusStatistics" resultType="java.util.Map">
        SELECT 
            status,
            CASE status 
                WHEN 1 THEN '待执行'
                WHEN 2 THEN '执行中'
                WHEN 3 THEN '已完成'
                WHEN 4 THEN '跳过'
                WHEN 5 THEN '异常'
                ELSE '未知'
            END as status_name,
            COUNT(*) as count
        FROM production_task_node
        <if test="taskId != null and taskId != ''">
            WHERE task_id = #{taskId}
        </if>
        GROUP BY status
        ORDER BY status
    </select>
    
    <select id="selectExecutableNodes" resultMap="ProductionTaskNodeVoResult">
        <include refid="selectProductionTaskNodeVo"/>
        WHERE ptn.task_id = #{taskId}
        AND ptn.status = 1
        AND (
            ptn.sequence_no = 1
            OR EXISTS (
                SELECT 1 FROM production_task_node prev 
                WHERE prev.task_id = ptn.task_id 
                AND prev.sequence_no = ptn.sequence_no - 1 
                AND prev.status = 3
            )
        )
        ORDER BY ptn.sequence_no ASC
    </select>
    
    <select id="selectExecutingNodes" resultMap="ProductionTaskNodeVoResult">
        <include refid="selectProductionTaskNodeVo"/>
        WHERE ptn.status = 2
        <if test="taskId != null and taskId != ''">
            AND ptn.task_id = #{taskId}
        </if>
        ORDER BY ptn.start_time ASC
    </select>
    
    <update id="updateNodeStatus">
        UPDATE production_task_node 
        SET status = #{status}, update_time = #{updateTime}
        WHERE node_id = #{nodeId}
    </update>
    
    <update id="startNodeExecution">
        UPDATE production_task_node 
        SET status = 2, start_time = #{startTime}, update_time = #{startTime}
        WHERE node_id = #{nodeId}
    </update>
    
    <update id="completeNodeExecution">
        UPDATE production_task_node 
        SET status = 3, end_time = #{endTime}, actual_duration = #{actualDuration}, update_time = #{endTime}
        WHERE node_id = #{nodeId}
    </update>
    
    <select id="selectNextNode" resultMap="ProductionTaskNodeVoResult">
        <include refid="selectProductionTaskNodeVo"/>
        WHERE ptn.task_id = #{taskId}
        AND ptn.sequence_no > #{currentSequenceNo}
        ORDER BY ptn.sequence_no ASC
        LIMIT 1
    </select>
    
    <select id="selectPreviousNodes" resultMap="ProductionTaskNodeVoResult">
        <include refid="selectProductionTaskNodeVo"/>
        WHERE ptn.task_id = #{taskId}
        AND ptn.sequence_no &lt; #{sequenceNo}
        ORDER BY ptn.sequence_no ASC
    </select>
    
    <update id="batchUpdateNodeSequence">
        <foreach collection="nodeList" item="node" separator=";">
            UPDATE production_task_node 
            SET sequence_no = #{node.sequenceNo}, update_time = NOW()
            WHERE node_id = #{node.nodeId}
        </foreach>
    </update>

</mapper>
