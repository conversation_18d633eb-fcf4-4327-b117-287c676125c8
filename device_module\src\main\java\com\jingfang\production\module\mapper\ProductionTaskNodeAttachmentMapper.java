package com.jingfang.production.module.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jingfang.production.module.entity.ProductionTaskNodeAttachment;
import com.jingfang.production.module.vo.ProductionTaskNodeAttachmentVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 任务节点附件Mapper接口
 */
@Mapper
public interface ProductionTaskNodeAttachmentMapper extends BaseMapper<ProductionTaskNodeAttachment> {
    
    /**
     * 根据节点ID查询附件列表
     * 
     * @param nodeId 节点ID
     * @return 附件列表
     */
    List<ProductionTaskNodeAttachmentVo> selectAttachmentsByNodeId(@Param("nodeId") String nodeId);
    
    /**
     * 根据任务ID查询所有附件
     * 
     * @param taskId 任务ID
     * @return 附件列表
     */
    List<ProductionTaskNodeAttachmentVo> selectAttachmentsByTaskId(@Param("taskId") String taskId);
    
    /**
     * 根据附件ID查询附件详情
     * 
     * @param attachmentId 附件ID
     * @return 附件详情
     */
    ProductionTaskNodeAttachmentVo selectAttachmentById(@Param("attachmentId") String attachmentId);
    
    /**
     * 统计节点的附件数量
     * 
     * @param nodeId 节点ID
     * @return 附件数量
     */
    int countAttachmentsByNodeId(@Param("nodeId") String nodeId);
    
    /**
     * 统计任务的附件数量
     * 
     * @param taskId 任务ID
     * @return 附件数量
     */
    int countAttachmentsByTaskId(@Param("taskId") String taskId);
    
    /**
     * 根据节点ID删除所有附件
     * 
     * @param nodeId 节点ID
     * @return 删除结果
     */
    int deleteAttachmentsByNodeId(@Param("nodeId") String nodeId);
    
    /**
     * 根据任务ID删除所有附件
     * 
     * @param taskId 任务ID
     * @return 删除结果
     */
    int deleteAttachmentsByTaskId(@Param("taskId") String taskId);
    
    /**
     * 批量插入附件
     * 
     * @param attachmentList 附件列表
     * @return 插入结果
     */
    int batchInsertAttachments(@Param("attachmentList") List<ProductionTaskNodeAttachment> attachmentList);
}
