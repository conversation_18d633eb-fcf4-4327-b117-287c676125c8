<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.production.mapper.ProductionTaskMapper">
    
    <resultMap type="com.jingfang.production.module.entity.vo.ProductionTaskVo" id="ProductionTaskVoResult">
        <result property="taskId" column="task_id" />
        <result property="taskName" column="task_name" />
        <result property="taskCode" column="task_code" />
        <result property="taskType" column="task_type" />
        <result property="taskTypeName" column="task_type_name" />
        <result property="productName" column="product_name" />
        <result property="planStartDate" column="plan_start_date" />
        <result property="planEndDate" column="plan_end_date" />
        <result property="actualStartTime" column="actual_start_time" />
        <result property="actualEndTime" column="actual_end_time" />
        <result property="priorityLevel" column="priority_level" />
        <result property="priorityLevelName" column="priority_level_name" />
        <result property="responsibleUserId" column="responsible_user_id" />
        <result property="responsibleUserName" column="responsible_user_name" />
        <result property="estimatedHours" column="estimated_hours" />
        <result property="actualHours" column="actual_hours" />
        <result property="status" column="status" />
        <result property="statusName" column="status_name" />
        <result property="progressRate" column="progress_rate" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="remark" column="remark" />
        <result property="totalNodes" column="total_nodes" />
        <result property="completedNodes" column="completed_nodes" />
        <result property="assetCount" column="asset_count" />
        <result property="inventoryCount" column="inventory_count" />
    </resultMap>
    
    <sql id="selectProductionTaskVo">
        SELECT 
            pt.task_id,
            pt.task_name,
            pt.task_code,
            pt.task_type,
            CASE pt.task_type 
                WHEN 1 THEN '正常生产'
                WHEN 2 THEN '紧急生产'
                WHEN 3 THEN '试产'
                ELSE '未知'
            END as task_type_name,
            pt.product_name,
            pt.plan_start_date,
            pt.plan_end_date,
            pt.actual_start_time,
            pt.actual_end_time,
            pt.priority_level,
            CASE pt.priority_level 
                WHEN 1 THEN '高'
                WHEN 2 THEN '中'
                WHEN 3 THEN '低'
                ELSE '未知'
            END as priority_level_name,
            pt.responsible_user_id,
            su.nick_name as responsible_user_name,
            pt.estimated_hours,
            pt.actual_hours,
            pt.status,
            CASE pt.status 
                WHEN 1 THEN '待执行'
                WHEN 2 THEN '已分配'
                WHEN 3 THEN '执行中'
                WHEN 4 THEN '质检中'
                WHEN 5 THEN '异常处理'
                WHEN 6 THEN '已完工'
                WHEN 7 THEN '已核算'
                ELSE '未知'
            END as status_name,
            pt.progress_rate,
            pt.create_by,
            pt.create_time,
            pt.update_by,
            pt.update_time,
            pt.remark,
            COALESCE(node_stats.total_nodes, 0) as total_nodes,
            COALESCE(node_stats.completed_nodes, 0) as completed_nodes,
            COALESCE(asset_stats.asset_count, 0) as asset_count,
            COALESCE(inventory_stats.inventory_count, 0) as inventory_count
        FROM production_task pt
        LEFT JOIN sys_user su ON pt.responsible_user_id = su.user_id
        LEFT JOIN (
            SELECT 
                task_id,
                COUNT(*) as total_nodes,
                SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as completed_nodes
            FROM production_task_node 
            GROUP BY task_id
        ) node_stats ON pt.task_id = node_stats.task_id
        LEFT JOIN (
            SELECT task_id, COUNT(*) as asset_count
            FROM production_task_asset 
            GROUP BY task_id
        ) asset_stats ON pt.task_id = asset_stats.task_id
        LEFT JOIN (
            SELECT task_id, COUNT(*) as inventory_count
            FROM production_task_inventory 
            GROUP BY task_id
        ) inventory_stats ON pt.task_id = inventory_stats.task_id
    </sql>
    
    <select id="selectProductionTaskList" resultMap="ProductionTaskVoResult">
        <include refid="selectProductionTaskVo"/>
        <where>
            <if test="query.taskName != null and query.taskName != ''">
                AND pt.task_name LIKE CONCAT('%', #{query.taskName}, '%')
            </if>
            <if test="query.taskCode != null and query.taskCode != ''">
                AND pt.task_code LIKE CONCAT('%', #{query.taskCode}, '%')
            </if>
            <if test="query.taskType != null">
                AND pt.task_type = #{query.taskType}
            </if>
            <if test="query.productName != null and query.productName != ''">
                AND pt.product_name LIKE CONCAT('%', #{query.productName}, '%')
            </if>
            <if test="query.priorityLevel != null">
                AND pt.priority_level = #{query.priorityLevel}
            </if>
            <if test="query.responsibleUserId != null">
                AND pt.responsible_user_id = #{query.responsibleUserId}
            </if>
            <if test="query.status != null">
                AND pt.status = #{query.status}
            </if>
            <if test="query.statusList != null and query.statusList.length > 0">
                AND pt.status IN
                <foreach collection="query.statusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="query.planStartDateBegin != null">
                AND DATE(pt.plan_start_date) >= #{query.planStartDateBegin}
            </if>
            <if test="query.planStartDateEnd != null">
                AND DATE(pt.plan_start_date) &lt;= #{query.planStartDateEnd}
            </if>
            <if test="query.planEndDateBegin != null">
                AND DATE(pt.plan_end_date) >= #{query.planEndDateBegin}
            </if>
            <if test="query.planEndDateEnd != null">
                AND DATE(pt.plan_end_date) &lt;= #{query.planEndDateEnd}
            </if>
            <if test="query.createTimeBegin != null">
                AND DATE(pt.create_time) >= #{query.createTimeBegin}
            </if>
            <if test="query.createTimeEnd != null">
                AND DATE(pt.create_time) &lt;= #{query.createTimeEnd}
            </if>
            <if test="query.createBy != null and query.createBy != ''">
                AND pt.create_by = #{query.createBy}
            </if>
        </where>
        <if test="query.orderByColumn != null and query.orderByColumn != ''">
            ORDER BY ${query.orderByColumn}
            <if test="query.isAsc != null and query.isAsc != ''">
                ${query.isAsc}
            </if>
        </if>
        <if test="query.orderByColumn == null or query.orderByColumn == ''">
            ORDER BY pt.create_time DESC
        </if>
    </select>
    
    <select id="selectProductionTaskById" resultMap="ProductionTaskVoResult">
        <include refid="selectProductionTaskVo"/>
        WHERE pt.task_id = #{taskId}
    </select>

    <select id="selectTaskStatusStatistics" resultType="java.util.Map">
        SELECT
            status,
            CASE status
                WHEN 1 THEN '待执行'
                WHEN 2 THEN '已分配'
                WHEN 3 THEN '执行中'
                WHEN 4 THEN '质检中'
                WHEN 5 THEN '异常处理'
                WHEN 6 THEN '已完工'
                WHEN 7 THEN '已核算'
                ELSE '未知'
            END as status_name,
            COUNT(*) as count
        FROM production_task
        GROUP BY status
        ORDER BY status
    </select>

    <select id="selectTaskProgressStatistics" resultType="java.util.Map">
        SELECT
            CASE
                WHEN progress_rate = 0 THEN '未开始'
                WHEN progress_rate > 0 AND progress_rate < 50 THEN '进行中(0-50%)'
                WHEN progress_rate >= 50 AND progress_rate < 100 THEN '进行中(50-99%)'
                WHEN progress_rate = 100 THEN '已完成'
                ELSE '未知'
            END as progress_range,
            COUNT(*) as count
        FROM production_task
        GROUP BY
            CASE
                WHEN progress_rate = 0 THEN '未开始'
                WHEN progress_rate > 0 AND progress_rate < 50 THEN '进行中(0-50%)'
                WHEN progress_rate >= 50 AND progress_rate < 100 THEN '进行中(50-99%)'
                WHEN progress_rate = 100 THEN '已完成'
                ELSE '未知'
            END
    </select>

    <select id="selectUpcomingTasks" resultMap="ProductionTaskVoResult">
        <include refid="selectProductionTaskVo"/>
        WHERE pt.plan_end_date BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL #{days} DAY)
        AND pt.status IN (1, 2, 3, 4, 5)
        ORDER BY pt.plan_end_date ASC
    </select>

    <select id="selectOverdueTasks" resultMap="ProductionTaskVoResult">
        <include refid="selectProductionTaskVo"/>
        WHERE pt.plan_end_date &lt; NOW()
        AND pt.status IN (1, 2, 3, 4, 5)
        ORDER BY pt.plan_end_date ASC
    </select>

    <select id="selectTasksByResponsibleUser" resultMap="ProductionTaskVoResult">
        <include refid="selectProductionTaskVo"/>
        WHERE pt.responsible_user_id = #{userId}
        <if test="status != null">
            AND pt.status = #{status}
        </if>
        ORDER BY pt.create_time DESC
    </select>

    <update id="updateTaskProgress">
        UPDATE production_task
        SET progress_rate = #{progressRate}, update_time = NOW()
        WHERE task_id = #{taskId}
    </update>

    <update id="updateTaskStatus">
        UPDATE production_task
        SET status = #{status}, update_by = #{updateBy}, update_time = NOW()
        WHERE task_id = #{taskId}
    </update>

    <select id="selectTaskResourceUsage" resultType="java.util.Map">
        SELECT
            (SELECT COUNT(*) FROM production_task_asset WHERE task_id = #{taskId}) as asset_count,
            (SELECT COUNT(*) FROM production_task_inventory WHERE task_id = #{taskId}) as inventory_count,
            (SELECT COUNT(*) FROM production_task_node WHERE task_id = #{taskId}) as node_count,
            (SELECT COUNT(*) FROM production_task_node WHERE task_id = #{taskId} AND status = 3) as completed_node_count
    </select>

</mapper>
