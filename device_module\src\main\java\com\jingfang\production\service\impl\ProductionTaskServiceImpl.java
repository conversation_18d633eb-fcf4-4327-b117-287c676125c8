package com.jingfang.production.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jingfang.common.utils.BusinessCodeGenerator;
import com.jingfang.common.utils.uuid.IdUtils;
import com.jingfang.production.mapper.ProductionTaskAssetMapper;
import com.jingfang.production.mapper.ProductionTaskInventoryMapper;
import com.jingfang.production.mapper.ProductionTaskMapper;
import com.jingfang.production.mapper.ProductionTaskNodeMapper;
import com.jingfang.production.dto.ProductionTaskDto;
import com.jingfang.production.dto.ProductionTaskQueryDto;
import com.jingfang.production.module.entity.ProductionTask;
import com.jingfang.production.module.entity.ProductionTaskAsset;
import com.jingfang.production.module.entity.ProductionTaskInventory;
import com.jingfang.production.vo.ProductionTaskVo;
import com.jingfang.production.service.ProductionTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 生产任务Service实现类
 *
 * <AUTHOR>
 * @date 2025-01-16
 */
@Slf4j
@Service
public class ProductionTaskServiceImpl extends ServiceImpl<ProductionTaskMapper, ProductionTask>
        implements ProductionTaskService {

    @Autowired
    private ProductionTaskMapper productionTaskMapper;

    @Autowired
    private ProductionTaskNodeMapper productionTaskNodeMapper;

    @Autowired
    private ProductionTaskAssetMapper productionTaskAssetMapper;

    @Autowired
    private ProductionTaskInventoryMapper productionTaskInventoryMapper;

    @Autowired
    private BusinessCodeGenerator codeGenerator;

    @Override
    @Transactional(readOnly = true)
    public IPage<ProductionTaskVo> selectProductionTaskList(ProductionTaskQueryDto queryDto) {
        Page<ProductionTaskVo> page = new Page<>(queryDto.getPageNum(), queryDto.getPageSize());
        return productionTaskMapper.selectProductionTaskList(page, queryDto);
    }

    @Override
    @Transactional(readOnly = true)
    public ProductionTaskVo selectProductionTaskById(String taskId) {
        return productionTaskMapper.selectProductionTaskById(taskId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertProductionTask(ProductionTaskDto taskDto, String createBy) {
        try {
            // 生成任务ID和编码
            String taskId = IdUtils.fastSimpleUUID();
            String taskCode = codeGenerator.generateCode("SCRW");

            // 转换为实体类
            ProductionTask task = new ProductionTask();
            BeanUtils.copyProperties(taskDto, task);
            task.setTaskId(taskId);
            task.setTaskCode(taskCode);
            task.setStatus(1); // 待执行
            task.setProgressRate(BigDecimal.ZERO);
            task.setCreateBy(createBy);
            task.setCreateTime(new Date());

            // 保存任务基本信息
            boolean result = this.save(task);
            if (!result) {
                return false;
            }

            // 保存关联的资产信息
            if (taskDto.getAssetList() != null && !taskDto.getAssetList().isEmpty()) {
                List<ProductionTaskAsset> assetList = taskDto.getAssetList().stream()
                    .map(assetDto -> {
                        ProductionTaskAsset asset = new ProductionTaskAsset();
                        BeanUtils.copyProperties(assetDto, asset);
                        asset.setRelationId(IdUtils.fastSimpleUUID());
                        asset.setTaskId(taskId);
                        asset.setCreateTime(new Date());
                        return asset;
                    }).toList();

                productionTaskAssetMapper.batchInsertTaskAssets(assetList);
            }

            // 保存关联的库存信息
            if (taskDto.getInventoryList() != null && !taskDto.getInventoryList().isEmpty()) {
                List<ProductionTaskInventory> inventoryList = taskDto.getInventoryList().stream()
                    .map(inventoryDto -> {
                        ProductionTaskInventory inventory = new ProductionTaskInventory();
                        BeanUtils.copyProperties(inventoryDto, inventory);
                        inventory.setRelationId(IdUtils.fastSimpleUUID());
                        inventory.setTaskId(taskId);
                        inventory.setCreateTime(new Date());
                        return inventory;
                    }).toList();

                productionTaskInventoryMapper.batchInsertTaskInventories(inventoryList);
            }

            log.info("创建生产任务成功，任务ID：{}，任务编码：{}", taskId, taskCode);
            return true;

        } catch (Exception e) {
            log.error("创建生产任务失败", e);
            throw new RuntimeException("创建生产任务失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateProductionTask(ProductionTaskDto taskDto, String updateBy) {
        try {
            // 更新任务基本信息
            ProductionTask task = new ProductionTask();
            BeanUtils.copyProperties(taskDto, task);
            task.setUpdateBy(updateBy);
            task.setUpdateTime(new Date());

            boolean result = this.updateById(task);
            if (!result) {
                return false;
            }

            // 删除原有的资产关联
            productionTaskAssetMapper.deleteAssetsByTaskId(taskDto.getTaskId());

            // 重新保存资产关联
            if (taskDto.getAssetList() != null && !taskDto.getAssetList().isEmpty()) {
                List<ProductionTaskAsset> assetList = taskDto.getAssetList().stream()
                    .map(assetDto -> {
                        ProductionTaskAsset asset = new ProductionTaskAsset();
                        BeanUtils.copyProperties(assetDto, asset);
                        asset.setRelationId(IdUtils.fastSimpleUUID());
                        asset.setTaskId(taskDto.getTaskId());
                        asset.setCreateTime(new Date());
                        return asset;
                    }).toList();

                productionTaskAssetMapper.batchInsertTaskAssets(assetList);
            }

            log.info("更新生产任务成功，任务ID：{}", taskDto.getTaskId());
            return true;

        } catch (Exception e) {
            log.error("更新生产任务失败，任务ID：{}", taskDto.getTaskId(), e);
            throw new RuntimeException("更新生产任务失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteProductionTask(String taskId) {
        try {
            // 删除任务关联的资产
            productionTaskAssetMapper.deleteAssetsByTaskId(taskId);

            // 删除任务关联的库存
            productionTaskInventoryMapper.deleteInventoriesByTaskId(taskId);

            // 删除任务主记录
            boolean result = this.removeById(taskId);

            log.info("删除生产任务成功，任务ID：{}", taskId);
            return result;

        } catch (Exception e) {
            log.error("删除生产任务失败，任务ID：{}", taskId, e);
            throw new RuntimeException("删除生产任务失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteProductionTasks(List<String> taskIds) {
        try {
            for (String taskId : taskIds) {
                deleteProductionTask(taskId);
            }
            return true;
        } catch (Exception e) {
            log.error("批量删除生产任务失败", e);
            throw new RuntimeException("批量删除生产任务失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean startTask(String taskId, String updateBy) {
        try {
            int result = productionTaskMapper.updateTaskStatus(taskId, 3, updateBy); // 执行中
            log.info("开始执行任务成功，任务ID：{}", taskId);
            return result > 0;
        } catch (Exception e) {
            log.error("开始执行任务失败，任务ID：{}", taskId, e);
            throw new RuntimeException("开始执行任务失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean pauseTask(String taskId, String updateBy) {
        try {
            int result = productionTaskMapper.updateTaskStatus(taskId, 2, updateBy); // 已分配
            log.info("暂停任务成功，任务ID：{}", taskId);
            return result > 0;
        } catch (Exception e) {
            log.error("暂停任务失败，任务ID：{}", taskId, e);
            throw new RuntimeException("暂停任务失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean completeTask(String taskId, String updateBy) {
        try {
            // 更新任务状态为已完工，进度为100%
            ProductionTask task = new ProductionTask();
            task.setTaskId(taskId);
            task.setStatus(6); // 已完工
            task.setProgressRate(new BigDecimal("100"));
            task.setActualEndTime(new Date());
            task.setUpdateBy(updateBy);
            task.setUpdateTime(new Date());

            boolean result = this.updateById(task);
            log.info("完成任务成功，任务ID：{}", taskId);
            return result;
        } catch (Exception e) {
            log.error("完成任务失败，任务ID：{}", taskId, e);
            throw new RuntimeException("完成任务失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelTask(String taskId, String updateBy) {
        try {
            int result = productionTaskMapper.updateTaskStatus(taskId, 5, updateBy); // 异常处理
            log.info("取消任务成功，任务ID：{}", taskId);
            return result > 0;
        } catch (Exception e) {
            log.error("取消任务失败，任务ID：{}", taskId, e);
            throw new RuntimeException("取消任务失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTaskProgress(String taskId, BigDecimal progressRate) {
        try {
            int result = productionTaskMapper.updateTaskProgress(taskId, progressRate);
            log.info("更新任务进度成功，任务ID：{}，进度：{}%", taskId, progressRate);
            return result > 0;
        } catch (Exception e) {
            log.error("更新任务进度失败，任务ID：{}", taskId, e);
            throw new RuntimeException("更新任务进度失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean assignTask(String taskId, Long responsibleUserId, String updateBy) {
        try {
            ProductionTask task = new ProductionTask();
            task.setTaskId(taskId);
            task.setResponsibleUserId(responsibleUserId);
            task.setStatus(2); // 已分配
            task.setUpdateBy(updateBy);
            task.setUpdateTime(new Date());

            boolean result = this.updateById(task);
            log.info("分配任务成功，任务ID：{}，负责人ID：{}", taskId, responsibleUserId);
            return result;
        } catch (Exception e) {
            log.error("分配任务失败，任务ID：{}", taskId, e);
            throw new RuntimeException("分配任务失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<Map<String, Object>> selectTaskStatusStatistics() {
        return productionTaskMapper.selectTaskStatusStatistics();
    }

    @Override
    @Transactional(readOnly = true)
    public List<Map<String, Object>> selectTaskProgressStatistics() {
        return productionTaskMapper.selectTaskProgressStatistics();
    }

    @Override
    @Transactional(readOnly = true)
    public List<ProductionTaskVo> selectUpcomingTasks(Integer days) {
        return productionTaskMapper.selectUpcomingTasks(days);
    }

    @Override
    @Transactional(readOnly = true)
    public List<ProductionTaskVo> selectOverdueTasks() {
        return productionTaskMapper.selectOverdueTasks();
    }

    @Override
    @Transactional(readOnly = true)
    public List<ProductionTaskVo> selectTasksByResponsibleUser(Long userId, Integer status) {
        return productionTaskMapper.selectTasksByResponsibleUser(userId, status);
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> selectTaskResourceUsage(String taskId) {
        return productionTaskMapper.selectTaskResourceUsage(taskId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean copyTask(String sourceTaskId, String taskName, String createBy) {
        try {
            // 查询源任务
            ProductionTask sourceTask = this.getById(sourceTaskId);
            if (sourceTask == null) {
                throw new RuntimeException("源任务不存在");
            }

            // 创建新任务
            String newTaskId = IdUtils.fastSimpleUUID();
            String newTaskCode = codeGenerator.generateCode("SCRW");

            ProductionTask newTask = new ProductionTask();
            BeanUtils.copyProperties(sourceTask, newTask);
            newTask.setTaskId(newTaskId);
            newTask.setTaskCode(newTaskCode);
            newTask.setTaskName(taskName);
            newTask.setStatus(1); // 待执行
            newTask.setProgressRate(BigDecimal.ZERO);
            newTask.setActualStartTime(null);
            newTask.setActualEndTime(null);
            newTask.setActualHours(null);
            newTask.setCreateBy(createBy);
            newTask.setCreateTime(new Date());
            newTask.setUpdateBy(null);
            newTask.setUpdateTime(null);

            boolean result = this.save(newTask);

            log.info("复制任务成功，源任务ID：{}，新任务ID：{}", sourceTaskId, newTaskId);
            return result;

        } catch (Exception e) {
            log.error("复制任务失败，源任务ID：{}", sourceTaskId, e);
            throw new RuntimeException("复制任务失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<ProductionTaskVo> exportTaskData(ProductionTaskQueryDto queryDto) {
        // 设置大的页面大小以获取所有数据
        queryDto.setPageNum(1);
        queryDto.setPageSize(10000);
        IPage<ProductionTaskVo> page = selectProductionTaskList(queryDto);
        return page.getRecords();
    }
}