package com.jingfang.production.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jingfang.production.module.entity.ProductionTaskNodeAttachment;
import com.jingfang.production.vo.ProductionTaskNodeAttachmentVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 任务节点附件Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-16
 */
@Mapper
public interface ProductionTaskNodeAttachmentMapper extends BaseMapper<ProductionTaskNodeAttachment> {

    /**
     * 根据节点ID查询附件列表
     *
     * @param nodeId 节点ID
     * @return 附件列表
     */
    List<ProductionTaskNodeAttachmentVo> selectAttachmentsByNodeId(@Param("nodeId") String nodeId);

    /**
     * 根据任务ID查询所有附件
     *
     * @param taskId 任务ID
     * @return 附件列表
     */
    List<ProductionTaskNodeAttachmentVo> selectAttachmentsByTaskId(@Param("taskId") String taskId);

    /**
     * 根据附件ID查询附件详情
     *
     * @param attachmentId 附件ID
     * @return 附件详情
     */
    ProductionTaskNodeAttachmentVo selectAttachmentById(@Param("attachmentId") String attachmentId);

    /**
     * 批量删除节点附件
     *
     * @param nodeId 节点ID
     * @return 删除结果
     */
    int deleteAttachmentsByNodeId(@Param("nodeId") String nodeId);

    /**
     * 批量删除任务附件
     *
     * @param taskId 任务ID
     * @return 删除结果
     */
    int deleteAttachmentsByTaskId(@Param("taskId") String taskId);
}