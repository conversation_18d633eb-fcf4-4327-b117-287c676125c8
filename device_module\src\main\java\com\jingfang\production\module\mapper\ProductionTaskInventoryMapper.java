package com.jingfang.production.module.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jingfang.production.module.entity.ProductionTaskInventory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 生产任务库存关联Mapper接口
 */
@Mapper
public interface ProductionTaskInventoryMapper extends BaseMapper<ProductionTaskInventory> {
    
    /**
     * 根据任务ID查询关联的库存列表
     * 
     * @param taskId 任务ID
     * @return 库存关联列表
     */
    List<ProductionTaskInventory> selectInventoriesByTaskId(@Param("taskId") String taskId);
    
    /**
     * 根据物品ID查询关联的任务列表
     * 
     * @param itemId 物品ID
     * @return 任务关联列表
     */
    List<ProductionTaskInventory> selectTasksByItemId(@Param("itemId") String itemId);
    
    /**
     * 查询任务的库存使用统计
     * 
     * @param taskId 任务ID
     * @return 库存使用统计
     */
    List<Map<String, Object>> selectInventoryUsageStatistics(@Param("taskId") String taskId);
    
    /**
     * 查询物品的任务使用情况
     * 
     * @param itemId 物品ID
     * @return 任务使用情况
     */
    List<Map<String, Object>> selectItemTaskUsage(@Param("itemId") String itemId);
    
    /**
     * 查询库存消耗汇总
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 消耗汇总数据
     */
    List<Map<String, Object>> selectInventoryConsumptionSummary(@Param("startDate") String startDate, @Param("endDate") String endDate);
    
    /**
     * 批量插入任务库存关联
     * 
     * @param inventoryList 库存关联列表
     * @return 插入结果
     */
    int batchInsertTaskInventories(@Param("inventoryList") List<ProductionTaskInventory> inventoryList);
    
    /**
     * 根据任务ID删除所有库存关联
     * 
     * @param taskId 任务ID
     * @return 删除结果
     */
    int deleteInventoriesByTaskId(@Param("taskId") String taskId);
    
    /**
     * 检查库存是否足够
     * 
     * @param itemId 物品ID
     * @param requiredQuantity 需要数量
     * @return 可用库存数量
     */
    BigDecimal checkInventoryAvailability(@Param("itemId") String itemId, @Param("requiredQuantity") BigDecimal requiredQuantity);
}
