package com.jingfang.web.controller.item;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.common.annotation.Log;
import com.jingfang.common.core.controller.BaseController;
import com.jingfang.common.core.domain.AjaxResult;
import com.jingfang.common.enums.BusinessType;
import com.jingfang.item_consumption_report.module.request.ItemConsumptionReportRequest;
import com.jingfang.item_consumption_report.module.vo.ItemConsumptionReportVo;
import com.jingfang.item_consumption_report.module.vo.ItemConsumptionStatisticsVo;
import com.jingfang.item_consumption_report.service.ItemConsumptionReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 库存消耗报表控制器
 */
@Slf4j
@RestController
@RequestMapping("/item/consumption-report")
public class ItemConsumptionReportController extends BaseController {
    
    @Autowired
    private ItemConsumptionReportService consumptionReportService;
    
    /**
     * 分页查询库存消耗报表数据
     */
    @PreAuthorize("@ss.hasPermi('item:consumption:report:list')")
    @PostMapping("/list")
    public AjaxResult list(@RequestBody @Validated ItemConsumptionReportRequest request) {
        try {
            log.info("查询库存消耗报表数据，请求参数：{}", request);
            IPage<ItemConsumptionReportVo> page = consumptionReportService.selectConsumptionReportList(request);
            return AjaxResult.success(page);
        } catch (Exception e) {
            log.error("查询库存消耗报表数据失败", e);
            return AjaxResult.error("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 查询库存消耗统计数据
     */
    @PreAuthorize("@ss.hasPermi('item:consumption:report:statistics')")
    @PostMapping("/statistics")
    public AjaxResult statistics(@RequestBody @Validated ItemConsumptionReportRequest request) {
        try {
            log.info("查询库存消耗统计数据，请求参数：{}", request);
            ItemConsumptionStatisticsVo statistics = consumptionReportService.selectConsumptionStatistics(request);
            return AjaxResult.success(statistics);
        } catch (Exception e) {
            log.error("查询库存消耗统计数据失败", e);
            return AjaxResult.error("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 查询消耗趋势数据
     */
    @PreAuthorize("@ss.hasPermi('item:consumption:report:trend')")
    @GetMapping("/trend")
    public AjaxResult getTrend(@RequestParam String itemId,
                              @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
                              @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
                              @RequestParam(defaultValue = "3") Integer timeDimension) {
        try {
            log.info("查询消耗趋势数据，物品ID：{}，时间维度：{}", itemId, timeDimension);
            List<ItemConsumptionReportVo.ConsumptionTrendVo> trendData = 
                consumptionReportService.selectConsumptionTrend(itemId, startDate, endDate, timeDimension);
            return AjaxResult.success(trendData);
        } catch (Exception e) {
            log.error("查询消耗趋势数据失败", e);
            return AjaxResult.error("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 查询消耗类型分布数据
     */
    @PreAuthorize("@ss.hasPermi('item:consumption:report:distribution')")
    @GetMapping("/type-distribution")
    public AjaxResult getTypeDistribution(@RequestParam String itemId,
                                         @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
                                         @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {
        try {
            log.info("查询消耗类型分布数据，物品ID：{}", itemId);
            List<ItemConsumptionReportVo.ConsumptionTypeVo> typeDistribution = 
                consumptionReportService.selectConsumptionTypeDistribution(itemId, startDate, endDate);
            return AjaxResult.success(typeDistribution);
        } catch (Exception e) {
            log.error("查询消耗类型分布数据失败", e);
            return AjaxResult.error("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 查询消耗效率排行榜
     */
    @PreAuthorize("@ss.hasPermi('item:consumption:report:ranking')")
    @PostMapping("/efficiency-ranking")
    public AjaxResult getEfficiencyRanking(@RequestBody @Validated ItemConsumptionReportRequest request,
                                          @RequestParam(defaultValue = "10") Integer limit) {
        try {
            log.info("查询消耗效率排行榜，限制数量：{}", limit);
            List<ItemConsumptionReportVo> ranking = 
                consumptionReportService.selectEfficiencyRanking(request, limit);
            return AjaxResult.success(ranking);
        } catch (Exception e) {
            log.error("查询消耗效率排行榜失败", e);
            return AjaxResult.error("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 查询消耗量排行榜
     */
    @PreAuthorize("@ss.hasPermi('item:consumption:report:ranking')")
    @PostMapping("/consumption-ranking")
    public AjaxResult getConsumptionRanking(@RequestBody @Validated ItemConsumptionReportRequest request,
                                           @RequestParam(defaultValue = "10") Integer limit) {
        try {
            log.info("查询消耗量排行榜，限制数量：{}", limit);
            List<ItemConsumptionReportVo> ranking = 
                consumptionReportService.selectConsumptionRanking(request, limit);
            return AjaxResult.success(ranking);
        } catch (Exception e) {
            log.error("查询消耗量排行榜失败", e);
            return AjaxResult.error("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 生成图表数据
     */
    @PreAuthorize("@ss.hasPermi('item:consumption:report:chart')")
    @PostMapping("/chart-data")
    public AjaxResult generateChartData(@RequestBody @Validated ItemConsumptionReportRequest request) {
        try {
            log.info("生成图表数据，请求参数：{}", request);
            Map<String, Object> chartData = consumptionReportService.generateChartData(request);
            return AjaxResult.success(chartData);
        } catch (Exception e) {
            log.error("生成图表数据失败", e);
            return AjaxResult.error("生成失败：" + e.getMessage());
        }
    }
    
    /**
     * 生成消耗分析报告
     */
    @PreAuthorize("@ss.hasPermi('item:consumption:report:analysis')")
    @PostMapping("/analysis-report")
    public AjaxResult generateAnalysisReport(@RequestBody @Validated ItemConsumptionReportRequest request) {
        try {
            log.info("生成消耗分析报告，请求参数：{}", request);
            Map<String, Object> report = consumptionReportService.generateConsumptionAnalysisReport(request);
            return AjaxResult.success(report);
        } catch (Exception e) {
            log.error("生成消耗分析报告失败", e);
            return AjaxResult.error("生成失败：" + e.getMessage());
        }
    }
    
    /**
     * 导出消耗报表数据
     */
    @PreAuthorize("@ss.hasPermi('item:consumption:report:export')")
    @Log(title = "库存消耗报表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public AjaxResult export(@RequestBody @Validated ItemConsumptionReportRequest request) {
        try {
            log.info("导出消耗报表数据，请求参数：{}", request);
            List<ItemConsumptionReportVo> data = consumptionReportService.exportConsumptionReport(request);
            return AjaxResult.success("导出成功", data);
        } catch (Exception e) {
            log.error("导出消耗报表数据失败", e);
            return AjaxResult.error("导出失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取消耗预警数据
     */
    @PreAuthorize("@ss.hasPermi('item:consumption:report:alert')")
    @PostMapping("/alerts")
    public AjaxResult getConsumptionAlerts(@RequestBody @Validated ItemConsumptionReportRequest request) {
        try {
            log.info("获取消耗预警数据，请求参数：{}", request);
            List<ItemConsumptionReportVo> alerts = consumptionReportService.getConsumptionAlerts(request);
            return AjaxResult.success(alerts);
        } catch (Exception e) {
            log.error("获取消耗预警数据失败", e);
            return AjaxResult.error("获取失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取效率优化建议
     */
    // @PreAuthorize("@ss.hasPermi('item:consumption:report:suggestion')")
    @GetMapping("/optimization-suggestions")
    public AjaxResult getOptimizationSuggestions(@RequestParam String itemId,
                                                 @RequestParam(defaultValue = "monthly") String analysisPeriod) {
        try {
            log.info("获取效率优化建议，物品ID：{}，分析周期：{}", itemId, analysisPeriod);
            String suggestions = consumptionReportService.getEfficiencyOptimizationSuggestions(itemId, analysisPeriod);
            return AjaxResult.success(suggestions);
        } catch (Exception e) {
            log.error("获取效率优化建议失败", e);
            return AjaxResult.error("获取失败：" + e.getMessage());
        }
    }

    // ==================== 数据管理接口 ====================

    /**
     * 生成消耗汇总数据
     */
    // @PreAuthorize("@ss.hasPermi('item:consumption:report:manage')")
    @Log(title = "库存消耗报表", businessType = BusinessType.UPDATE)
    @PostMapping("/generate-summary")
    public AjaxResult generateSummary(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
                                     @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {
        try {
            log.info("生成消耗汇总数据，时间范围：{} - {}", startDate, endDate);
            int count = consumptionReportService.generateConsumptionSummary(startDate, endDate);
            return AjaxResult.success("生成消耗汇总数据成功，生成" + count + "条记录");
        } catch (Exception e) {
            log.error("生成消耗汇总数据失败", e);
            return AjaxResult.error("生成失败：" + e.getMessage());
        }
    }

    /**
     * 更新效率分析数据
     */
    @PreAuthorize("@ss.hasPermi('item:consumption:report:manage')")
    @Log(title = "库存消耗报表", businessType = BusinessType.UPDATE)
    @PostMapping("/update-efficiency")
    public AjaxResult updateEfficiency(@RequestParam String itemId,
                                      @RequestParam(defaultValue = "monthly") String analysisPeriod,
                                      @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
                                      @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {
        try {
            log.info("更新效率分析数据，物品ID：{}，分析周期：{}", itemId, analysisPeriod);
            int count = consumptionReportService.updateEfficiencyAnalysis(itemId, analysisPeriod, startDate, endDate);
            return AjaxResult.success("更新效率分析数据成功，更新" + count + "条记录");
        } catch (Exception e) {
            log.error("更新效率分析数据失败", e);
            return AjaxResult.error("更新失败：" + e.getMessage());
        }
    }

    /**
     * 批量更新消耗汇总数据
     */
    @PreAuthorize("@ss.hasPermi('item:consumption:report:manage')")
    @Log(title = "库存消耗报表", businessType = BusinessType.UPDATE)
    @PostMapping("/batch-update-summary")
    public AjaxResult batchUpdateSummary(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
                                        @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {
        try {
            log.info("批量更新消耗汇总数据，时间范围：{} - {}", startDate, endDate);
            boolean success = consumptionReportService.batchUpdateConsumptionSummary(startDate, endDate);
            if (success) {
                return AjaxResult.success("批量更新消耗汇总数据成功");
            } else {
                return AjaxResult.error("批量更新消耗汇总数据失败");
            }
        } catch (Exception e) {
            log.error("批量更新消耗汇总数据失败", e);
            return AjaxResult.error("更新失败：" + e.getMessage());
        }
    }

    /**
     * 批量计算效率分析数据
     */
    @PreAuthorize("@ss.hasPermi('item:consumption:report:manage')")
    @Log(title = "库存消耗报表", businessType = BusinessType.UPDATE)
    @PostMapping("/batch-calculate-efficiency")
    public AjaxResult batchCalculateEfficiency(@RequestParam(defaultValue = "monthly") String analysisPeriod,
                                              @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
                                              @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {
        try {
            log.info("批量计算效率分析数据，分析周期：{}，时间范围：{} - {}", analysisPeriod, startDate, endDate);
            boolean success = consumptionReportService.batchCalculateEfficiencyAnalysis(analysisPeriod, startDate, endDate);
            if (success) {
                return AjaxResult.success("批量计算效率分析数据成功");
            } else {
                return AjaxResult.error("批量计算效率分析数据失败");
            }
        } catch (Exception e) {
            log.error("批量计算效率分析数据失败", e);
            return AjaxResult.error("计算失败：" + e.getMessage());
        }
    }
}
