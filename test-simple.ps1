# 简单测试脚本
$token = "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiZjM0NDU2YTktYmU2Mi00MTY1LTk4ZjUtN2VhODNlM2VhNjBhIn0.xBoYuAG7vY_hqXeL7Siz_xiIG3ZjEOh_49L5cmvIFUvTHGWul0C33aZWLUbhXj6a4NHWO5aiIggbxC6MXerh_A"

Write-Host "开始测试库存消耗报表API..." -ForegroundColor Green

# 测试1: 生成消耗汇总数据
Write-Host "1. 测试生成消耗汇总数据" -ForegroundColor Yellow
try {
    $headers = @{}
    $headers.Add("Authorization", "Bearer $token")
    
    $response = Invoke-WebRequest -Uri "http://localhost:8080/item/consumption-report/generate-summary?startDate=2024-01-01&endDate=2024-12-31" -Method POST -Headers $headers
    Write-Host "状态码: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "响应: $($response.Content)" -ForegroundColor Cyan
} catch {
    Write-Host "失败: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "状态码: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}

# 测试2: 查询消耗报表列表
Write-Host "`n2. 测试查询消耗报表列表" -ForegroundColor Yellow
try {
    $headers = @{}
    $headers.Add("Authorization", "Bearer $token")
    $headers.Add("Content-Type", "application/json")
    
    $body = @{
        pageNum = 1
        pageSize = 10
        startDate = "2024-01-01"
        endDate = "2024-12-31"
    } | ConvertTo-Json
    
    $response = Invoke-WebRequest -Uri "http://localhost:8080/item/consumption-report/list" -Method POST -Headers $headers -Body $body
    Write-Host "状态码: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "响应: $($response.Content)" -ForegroundColor Cyan
} catch {
    Write-Host "失败: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "状态码: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}

# 测试3: 查询消耗统计数据
Write-Host "`n3. 测试查询消耗统计数据" -ForegroundColor Yellow
try {
    $headers = @{}
    $headers.Add("Authorization", "Bearer $token")
    $headers.Add("Content-Type", "application/json")
    
    $body = @{
        startDate = "2024-01-01"
        endDate = "2024-12-31"
    } | ConvertTo-Json
    
    $response = Invoke-WebRequest -Uri "http://localhost:8080/item/consumption-report/statistics" -Method POST -Headers $headers -Body $body
    Write-Host "状态码: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "响应: $($response.Content)" -ForegroundColor Cyan
} catch {
    Write-Host "失败: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "状态码: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}

# 测试4: 获取优化建议
Write-Host "`n4. 测试获取优化建议" -ForegroundColor Yellow
try {
    $headers = @{}
    $headers.Add("Authorization", "Bearer $token")
    
    $response = Invoke-WebRequest -Uri "http://localhost:8080/item/consumption-report/optimization-suggestions?itemId=TEST_ITEM&analysisPeriod=monthly" -Method GET -Headers $headers
    Write-Host "状态码: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "响应: $($response.Content)" -ForegroundColor Cyan
} catch {
    Write-Host "失败: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "状态码: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}

Write-Host "`n测试完成!" -ForegroundColor Green
