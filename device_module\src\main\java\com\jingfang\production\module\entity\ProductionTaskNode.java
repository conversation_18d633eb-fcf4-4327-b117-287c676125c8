package com.jingfang.production.module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 任务节点表
 * @TableName production_task_node
 */
@TableName(value = "production_task_node")
@Data
public class ProductionTaskNode implements Serializable {
    
    /**
     * 节点ID
     */
    @TableId(type = IdType.INPUT)
    private String nodeId;
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 节点名称
     */
    private String nodeName;
    
    /**
     * 节点类型(1-开始,2-处理,3-检验,4-决策,5-结束)
     */
    private Integer nodeType;
    
    /**
     * 节点序号
     */
    private Integer sequenceNo;
    
    /**
     * 是否必需(1-是,0-否)
     */
    private Integer isRequired;
    
    /**
     * 预计耗时(分钟)
     */
    private Integer estimatedDuration;
    
    /**
     * 实际耗时(分钟)
     */
    private Integer actualDuration;
    
    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;
    
    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;
    
    /**
     * 状态(1-待执行,2-执行中,3-已完成,4-跳过,5-异常)
     */
    private Integer status;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    
    private static final long serialVersionUID = 1L;
}