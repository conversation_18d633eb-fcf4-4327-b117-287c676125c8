# 测试现有API是否正常工作
$token = "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiZjM0NDU2YTktYmU2Mi00MTY1LTk4ZjUtN2VhODNlM2VhNjBhIn0.xBoYuAG7vY_hqXeL7Siz_xiIG3ZjEOh_49L5cmvIFUvTHGWul0C33aZWLUbhXj6a4NHWO5aiIggbxC6MXerh_A"

Write-Host "测试现有API..." -ForegroundColor Green

# 测试现有的物品管理接口
Write-Host "1. 测试物品列表接口" -ForegroundColor Yellow
try {
    $headers = @{}
    $headers.Add("Authorization", "Bearer $token")
    $headers.Add("Content-Type", "application/json")
    
    $body = @{
        pageNum = 1
        pageSize = 10
    } | ConvertTo-Json
    
    $response = Invoke-WebRequest -Uri "http://localhost:8080/item/list" -Method POST -Headers $headers -Body $body
    Write-Host "状态码: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "响应: $($response.Content.Substring(0, [Math]::Min(200, $response.Content.Length)))..." -ForegroundColor Cyan
} catch {
    Write-Host "失败: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "状态码: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}

# 测试用户信息接口
Write-Host "`n2. 测试用户信息接口" -ForegroundColor Yellow
try {
    $headers = @{}
    $headers.Add("Authorization", "Bearer $token")
    
    $response = Invoke-WebRequest -Uri "http://localhost:8080/getInfo" -Method GET -Headers $headers
    Write-Host "状态码: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "响应: $($response.Content.Substring(0, [Math]::Min(200, $response.Content.Length)))..." -ForegroundColor Cyan
} catch {
    Write-Host "失败: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "状态码: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}

Write-Host "`n测试完成!" -ForegroundColor Green
