package com.jingfang.maintenance_cost.module.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jingfang.maintenance_cost.module.entity.MaintenanceCostRecord;
import com.jingfang.maintenance_report.module.vo.MaintenanceReportVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 维护成本记录Mapper接口
 */
@Mapper
public interface MaintenanceCostRecordMapper extends BaseMapper<MaintenanceCostRecord> {
    
    /**
     * 查询成本统计汇总
     */
    MaintenanceReportVo.MaintenanceCostVo selectCostSummary(@Param("startDate") Date startDate,
                                                           @Param("endDate") Date endDate,
                                                           @Param("deptId") String deptId,
                                                           @Param("assetId") String assetId,
                                                           @Param("costTypes") List<Integer> costTypes);
    
    /**
     * 查询成本趋势数据
     */
    List<MaintenanceReportVo.CostTrendVo> selectCostTrends(@Param("startDate") Date startDate,
                                                          @Param("endDate") Date endDate,
                                                          @Param("timeDimension") Integer timeDimension,
                                                          @Param("deptId") String deptId,
                                                          @Param("assetId") String assetId,
                                                          @Param("costTypes") List<Integer> costTypes);
    
    /**
     * 查询部门成本统计
     */
    List<MaintenanceReportVo.DeptCostVo> selectDeptCostStatistics(@Param("startDate") Date startDate,
                                                                 @Param("endDate") Date endDate,
                                                                 @Param("deptIds") List<String> deptIds,
                                                                 @Param("costTypes") List<Integer> costTypes);
    
    /**
     * 查询资产成本统计
     */
    List<MaintenanceReportVo.AssetCostVo> selectAssetCostStatistics(@Param("startDate") Date startDate,
                                                                   @Param("endDate") Date endDate,
                                                                   @Param("deptId") String deptId,
                                                                   @Param("assetIds") List<String> assetIds,
                                                                   @Param("costTypes") List<Integer> costTypes);
    
    /**
     * 查询成本类型分布
     */
    List<MaintenanceReportVo.CostTypeVo> selectCostTypeDistribution(@Param("startDate") Date startDate,
                                                                   @Param("endDate") Date endDate,
                                                                   @Param("deptId") String deptId,
                                                                   @Param("assetId") String assetId);
    
    /**
     * 查询预防性维护成本
     */
    BigDecimal selectPreventiveCost(@Param("startDate") Date startDate,
                                   @Param("endDate") Date endDate,
                                   @Param("deptId") String deptId,
                                   @Param("assetId") String assetId);
    
    /**
     * 查询故障维护成本
     */
    BigDecimal selectCorrectiveCost(@Param("startDate") Date startDate,
                                   @Param("endDate") Date endDate,
                                   @Param("deptId") String deptId,
                                   @Param("assetId") String assetId);
    
    /**
     * 按任务ID查询成本记录
     */
    List<MaintenanceCostRecord> selectByTaskId(@Param("taskId") String taskId);
    
    /**
     * 按故障ID查询成本记录
     */
    List<MaintenanceCostRecord> selectByFaultId(@Param("faultId") String faultId);
    
    /**
     * 按资产ID查询成本记录
     */
    List<MaintenanceCostRecord> selectByAssetId(@Param("assetId") String assetId,
                                               @Param("startDate") Date startDate,
                                               @Param("endDate") Date endDate);
    
    /**
     * 查询成本记录总数
     */
    Integer countCostRecords(@Param("startDate") Date startDate,
                            @Param("endDate") Date endDate,
                            @Param("deptId") String deptId,
                            @Param("assetId") String assetId,
                            @Param("costTypes") List<Integer> costTypes);
    
    /**
     * 查询平均成本
     */
    BigDecimal selectAvgCost(@Param("startDate") Date startDate,
                            @Param("endDate") Date endDate,
                            @Param("deptId") String deptId,
                            @Param("assetId") String assetId,
                            @Param("costType") Integer costType);
    
    /**
     * 查询最大成本记录
     */
    MaintenanceCostRecord selectMaxCostRecord(@Param("startDate") Date startDate,
                                             @Param("endDate") Date endDate,
                                             @Param("deptId") String deptId);
    
    /**
     * 查询最小成本记录
     */
    MaintenanceCostRecord selectMinCostRecord(@Param("startDate") Date startDate,
                                             @Param("endDate") Date endDate,
                                             @Param("deptId") String deptId);
    
    /**
     * 批量插入成本记录
     */
    int batchInsert(@Param("records") List<MaintenanceCostRecord> records);
    
    /**
     * 按条件删除成本记录
     */
    int deleteByCondition(@Param("taskId") String taskId,
                         @Param("faultId") String faultId,
                         @Param("assetId") String assetId);
    
    /**
     * 查询成本记录详情（包含关联信息）
     */
    List<Map<String, Object>> selectCostRecordDetails(@Param("startDate") Date startDate,
                                                     @Param("endDate") Date endDate,
                                                     @Param("deptId") String deptId,
                                                     @Param("assetId") String assetId,
                                                     @Param("costTypes") List<Integer> costTypes,
                                                     @Param("orderBy") String orderBy,
                                                     @Param("orderDirection") String orderDirection);
    
    /**
     * 查询成本统计图表数据
     */
    List<Map<String, Object>> selectCostChartData(@Param("chartType") String chartType,
                                                 @Param("startDate") Date startDate,
                                                 @Param("endDate") Date endDate,
                                                 @Param("timeDimension") Integer timeDimension,
                                                 @Param("deptId") String deptId,
                                                 @Param("assetId") String assetId,
                                                 @Param("costTypes") List<Integer> costTypes);
}
