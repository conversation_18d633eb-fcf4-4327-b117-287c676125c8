# 简化的故障申报功能测试脚本

$BaseUrl = "http://localhost:8080"
$Token = "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiYWY1MjdjY2UtMmIwMy00N2NjLWE1N2ItZDhkZTQ5YzIyY2YwIn0.8orGj_5UWKg6HuKPk9bQSBJsS0c_H25VAdEh_htY9YmxEsUvfrKLmBtt_1YRHulHiry6UH7dVtqaopeVPa1fow"

Write-Host "=== 故障申报功能测试 ===" -ForegroundColor Green

# 设置请求头
$headers = @{
    "Authorization" = "Bearer $Token"
    "Content-Type" = "application/json"
}

Write-Host "1. 测试基础功能检查..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$BaseUrl/maintenance/fault/test/check" -Method GET -Headers $headers
    $result = $response.Content | ConvertFrom-Json
    Write-Host "   结果: $($result.msg)" -ForegroundColor Green
    Write-Host "   服务状态: $($result.data.serviceStatus)" -ForegroundColor Gray
} catch {
    Write-Host "   失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n2. 测试获取故障类型..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$BaseUrl/maintenance/fault/types" -Method GET -Headers $headers
    $result = $response.Content | ConvertFrom-Json
    Write-Host "   结果: $($result.msg)" -ForegroundColor Green
    Write-Host "   故障类型数量: $($result.data.Count)" -ForegroundColor Gray
} catch {
    Write-Host "   失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n3. 测试获取紧急程度..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$BaseUrl/maintenance/fault/urgency-levels" -Method GET -Headers $headers
    $result = $response.Content | ConvertFrom-Json
    Write-Host "   结果: $($result.msg)" -ForegroundColor Green
    Write-Host "   紧急程度数量: $($result.data.Count)" -ForegroundColor Gray
} catch {
    Write-Host "   失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n4. 测试故障申报提交..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$BaseUrl/maintenance/fault/test/submit" -Method POST -Headers $headers
    $result = $response.Content | ConvertFrom-Json
    Write-Host "   结果: $($result.msg)" -ForegroundColor Green
    if ($result.data) {
        Write-Host "   故障ID: $($result.data.faultId)" -ForegroundColor Gray
        Write-Host "   任务ID: $($result.data.taskId)" -ForegroundColor Gray
    }
} catch {
    Write-Host "   失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n5. 测试查询我的故障申报..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$BaseUrl/maintenance/fault/test/my-reports" -Method GET -Headers $headers
    $result = $response.Content | ConvertFrom-Json
    Write-Host "   结果: $($result.msg)" -ForegroundColor Green
    if ($result.data) {
        Write-Host "   申报记录数: $($result.data.Count)" -ForegroundColor Gray
    }
} catch {
    Write-Host "   失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n6. 测试获取统计信息..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$BaseUrl/maintenance/fault/test/statistics" -Method GET -Headers $headers
    $result = $response.Content | ConvertFrom-Json
    Write-Host "   结果: $($result.msg)" -ForegroundColor Green
    if ($result.data) {
        Write-Host "   已提交: $($result.data.submitted)" -ForegroundColor Gray
        Write-Host "   处理中: $($result.data.processing)" -ForegroundColor Gray
        Write-Host "   已完成: $($result.data.completed)" -ForegroundColor Gray
    }
} catch {
    Write-Host "   失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
