package com.jingfang.item_consumption_report.module.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 库存消耗统计数据展示对象
 */
@Data
public class ItemConsumptionStatisticsVo {
    
    /**
     * 总体统计信息
     */
    private OverallStatistics overallStatistics;
    
    /**
     * 消耗类型统计
     */
    private List<ConsumptionTypeStatistics> typeStatistics;
    
    /**
     * 效率等级统计
     */
    private List<EfficiencyLevelStatistics> efficiencyStatistics;
    
    /**
     * 仓库消耗统计
     */
    private List<WarehouseStatistics> warehouseStatistics;
    
    /**
     * 部门消耗统计
     */
    private List<DepartmentStatistics> departmentStatistics;
    
    /**
     * 图表数据
     */
    private Map<String, Object> chartData;
    
    /**
     * 总体统计信息内部类
     */
    @Data
    public static class OverallStatistics {
        /**
         * 总消耗数量
         */
        private BigDecimal totalConsumption;
        
        /**
         * 总消耗金额
         */
        private BigDecimal totalAmount;
        
        /**
         * 平均单价
         */
        private BigDecimal avgUnitPrice;
        
        /**
         * 消耗物品种类数
         */
        private Integer itemCount;
        
        /**
         * 消耗次数
         */
        private Integer consumptionCount;
        
        /**
         * 平均效率评分
         */
        private BigDecimal avgEfficiencyScore;
        
        /**
         * 总节约金额
         */
        private BigDecimal totalSavings;
        
        /**
         * 效率改善率
         */
        private BigDecimal efficiencyImprovement;
    }
    
    /**
     * 消耗类型统计内部类
     */
    @Data
    public static class ConsumptionTypeStatistics {
        /**
         * 消耗类型
         */
        private Integer consumptionType;
        
        /**
         * 消耗类型名称
         */
        private String consumptionTypeName;
        
        /**
         * 消耗数量
         */
        private BigDecimal consumption;
        
        /**
         * 消耗金额
         */
        private BigDecimal amount;
        
        /**
         * 占比百分比
         */
        private BigDecimal percentage;
        
        /**
         * 物品种类数
         */
        private Integer itemCount;
        
        /**
         * 平均效率评分
         */
        private BigDecimal avgEfficiencyScore;
    }
    
    /**
     * 效率等级统计内部类
     */
    @Data
    public static class EfficiencyLevelStatistics {
        /**
         * 效率等级
         */
        private String efficiencyLevel;
        
        /**
         * 效率等级名称
         */
        private String efficiencyLevelName;
        
        /**
         * 物品数量
         */
        private Integer itemCount;
        
        /**
         * 占比百分比
         */
        private BigDecimal percentage;
        
        /**
         * 平均效率评分
         */
        private BigDecimal avgEfficiencyScore;
        
        /**
         * 总消耗数量
         */
        private BigDecimal totalConsumption;
        
        /**
         * 总节约金额
         */
        private BigDecimal totalSavings;
    }
    
    /**
     * 仓库统计内部类
     */
    @Data
    public static class WarehouseStatistics {
        /**
         * 仓库ID
         */
        private Integer warehouseId;
        
        /**
         * 仓库名称
         */
        private String warehouseName;
        
        /**
         * 消耗数量
         */
        private BigDecimal consumption;
        
        /**
         * 消耗金额
         */
        private BigDecimal amount;
        
        /**
         * 占比百分比
         */
        private BigDecimal percentage;
        
        /**
         * 物品种类数
         */
        private Integer itemCount;
        
        /**
         * 平均效率评分
         */
        private BigDecimal avgEfficiencyScore;
    }
    
    /**
     * 部门统计内部类
     */
    @Data
    public static class DepartmentStatistics {
        /**
         * 部门名称
         */
        private String deptName;
        
        /**
         * 消耗数量
         */
        private BigDecimal consumption;
        
        /**
         * 消耗金额
         */
        private BigDecimal amount;
        
        /**
         * 占比百分比
         */
        private BigDecimal percentage;
        
        /**
         * 物品种类数
         */
        private Integer itemCount;
        
        /**
         * 消耗次数
         */
        private Integer consumptionCount;
    }
}
