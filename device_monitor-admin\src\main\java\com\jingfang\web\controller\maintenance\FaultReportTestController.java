package com.jingfang.web.controller.maintenance;

import com.jingfang.common.core.controller.BaseController;
import com.jingfang.common.core.domain.AjaxResult;
import com.jingfang.fault_report.module.dto.FaultReportDto;
import com.jingfang.fault_report.service.FaultReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 资产故障申报测试控制器
 */
@Slf4j
@RestController
@RequestMapping("/maintenance/fault/test")
public class FaultReportTestController extends BaseController {
    
    @Autowired
    private FaultReportService faultReportService;
    
    /**
     * 测试提交故障申报
     */
    @PostMapping("/submit")
    public AjaxResult testSubmitFaultReport() {
        try {
            FaultReportDto faultReportDto = new FaultReportDto();
            faultReportDto.setAssetId("AS20230001");
            faultReportDto.setAssetName("中央空调主机");
            faultReportDto.setFaultTitle("测试故障申报");
            faultReportDto.setFaultDescription("这是一个测试故障申报，用于验证系统功能");
            faultReportDto.setFaultType(2); // 机械故障
            faultReportDto.setUrgencyLevel(3); // 紧急
            faultReportDto.setReportLocation("测试位置");
            faultReportDto.setContactPhone("13800138000");
            faultReportDto.setImages(Arrays.asList("test1.jpg", "test2.jpg"));
            faultReportDto.setRemark("测试备注");
            
            Map<String, String> result = faultReportService.submitFaultReport(faultReportDto);
            return AjaxResult.success("测试故障申报提交成功", result);
        } catch (Exception e) {
            log.error("测试故障申报提交失败", e);
            return AjaxResult.error("测试失败：" + e.getMessage());
        }
    }
    
    /**
     * 测试查询故障类型选项
     */
    @GetMapping("/fault-types")
    public AjaxResult testGetFaultTypes() {
        try {
            List<Map<String, Object>> options = faultReportService.getFaultTypeOptions();
            return AjaxResult.success("获取故障类型选项成功", options);
        } catch (Exception e) {
            log.error("获取故障类型选项失败", e);
            return AjaxResult.error("测试失败：" + e.getMessage());
        }
    }
    
    /**
     * 测试查询紧急程度选项
     */
    @GetMapping("/urgency-levels")
    public AjaxResult testGetUrgencyLevels() {
        try {
            List<Map<String, Object>> options = faultReportService.getUrgencyLevelOptions();
            return AjaxResult.success("获取紧急程度选项成功", options);
        } catch (Exception e) {
            log.error("获取紧急程度选项失败", e);
            return AjaxResult.error("测试失败：" + e.getMessage());
        }
    }
    
    /**
     * 测试查询我的故障申报
     */
    @GetMapping("/my-reports")
    public AjaxResult testGetMyReports() {
        try {
            List<com.jingfang.fault_report.module.vo.FaultReportVo> reports = 
                faultReportService.getMyFaultReports(null);
            return AjaxResult.success("查询我的故障申报成功", reports);
        } catch (Exception e) {
            log.error("查询我的故障申报失败", e);
            return AjaxResult.error("测试失败：" + e.getMessage());
        }
    }
    
    /**
     * 测试查询故障申报统计
     */
    @GetMapping("/statistics")
    public AjaxResult testGetStatistics() {
        try {
            Map<String, Integer> statistics = faultReportService.getFaultReportStatistics();
            return AjaxResult.success("获取故障申报统计成功", statistics);
        } catch (Exception e) {
            log.error("获取故障申报统计失败", e);
            return AjaxResult.error("测试失败：" + e.getMessage());
        }
    }
    
    /**
     * 测试查询紧急故障申报
     */
    @GetMapping("/urgent")
    public AjaxResult testGetUrgentReports() {
        try {
            List<com.jingfang.fault_report.module.vo.FaultReportVo> reports = 
                faultReportService.getUrgentFaultReports();
            return AjaxResult.success("查询紧急故障申报成功", reports);
        } catch (Exception e) {
            log.error("查询紧急故障申报失败", e);
            return AjaxResult.error("测试失败：" + e.getMessage());
        }
    }
    
    /**
     * 检查数据库连接和基础功能
     */
    @GetMapping("/check")
    public AjaxResult checkFaultReportFunction() {
        try {
            // 检查服务是否正常注入
            if (faultReportService == null) {
                return AjaxResult.error("故障申报服务未正确注入");
            }
            
            // 检查基础选项数据
            List<Map<String, Object>> faultTypes = faultReportService.getFaultTypeOptions();
            List<Map<String, Object>> urgencyLevels = faultReportService.getUrgencyLevelOptions();
            
            if (faultTypes.isEmpty() || urgencyLevels.isEmpty()) {
                return AjaxResult.error("基础选项数据异常");
            }
            
            return AjaxResult.success("故障申报功能检查通过", Map.of(
                "faultTypes", faultTypes,
                "urgencyLevels", urgencyLevels,
                "serviceStatus", "正常"
            ));
        } catch (Exception e) {
            log.error("故障申报功能检查失败", e);
            return AjaxResult.error("功能检查失败：" + e.getMessage());
        }
    }
}
