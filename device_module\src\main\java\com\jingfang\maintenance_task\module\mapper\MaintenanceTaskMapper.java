package com.jingfang.maintenance_task.module.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jingfang.maintenance_task.module.entity.MaintenanceTask;
import com.jingfang.maintenance_task.module.request.MaintenanceTaskSearchRequest;
import com.jingfang.maintenance_task.module.vo.MaintenanceTaskVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 维护任务Mapper接口
 */
@Mapper
public interface MaintenanceTaskMapper extends BaseMapper<MaintenanceTask> {
    
    /**
     * 分页查询维护任务
     */
    IPage<MaintenanceTaskVo> selectTaskPage(Page<MaintenanceTaskVo> page, @Param("request") MaintenanceTaskSearchRequest request, @Param("currentUserId") Long currentUserId);
    
    /**
     * 根据ID查询维护任务详情
     */
    MaintenanceTaskVo selectTaskById(@Param("taskId") String taskId);
    
    /**
     * 查询用户的维护任务列表
     */
    List<MaintenanceTaskVo> selectTasksByUser(@Param("userId") Long userId, @Param("statusList") List<Integer> statusList);
    
    /**
     * 查询部门的维护任务列表
     */
    List<MaintenanceTaskVo> selectTasksByDept(@Param("deptId") Long deptId, @Param("statusList") List<Integer> statusList);
    
    /**
     * 查询即将到期的维护任务
     */
    List<MaintenanceTaskVo> selectUpcomingTasks(@Param("days") Integer days);
    
    /**
     * 查询已逾期的维护任务
     */
    List<MaintenanceTaskVo> selectOverdueTasks();
    
    /**
     * 查询待审核的维护任务
     */
    List<MaintenanceTaskVo> selectPendingReviewTasks(@Param("reviewerId") Long reviewerId);
    
    /**
     * 根据维护计划ID查询维护任务列表
     */
    List<MaintenanceTaskVo> selectTasksByPlanId(@Param("planId") String planId);
    
    /**
     * 根据资产ID查询维护任务列表
     */
    List<MaintenanceTaskVo> selectTasksByAssetId(@Param("assetId") String assetId);
    
    /**
     * 统计维护任务数量
     */
    Integer countTasksByStatus(@Param("status") Integer status, @Param("userId") Long userId);
    
    /**
     * 统计用户的维护任务数量
     */
    Integer countUserTasks(@Param("userId") Long userId, @Param("statusList") List<Integer> statusList);
    
    /**
     * 统计部门的维护任务数量
     */
    Integer countDeptTasks(@Param("deptId") Long deptId, @Param("statusList") List<Integer> statusList);
    
    /**
     * 查询需要生成任务的维护计划
     */
    List<MaintenanceTask> selectTasksToGenerate(@Param("currentTime") Date currentTime);
    
    /**
     * 批量插入维护任务
     */
    int batchInsert(@Param("taskList") List<MaintenanceTask> taskList);
    
    /**
     * 更新任务状态
     */
    int updateTaskStatus(@Param("taskId") String taskId, @Param("status") Integer status, @Param("updateBy") String updateBy);
    
    /**
     * 委派任务
     */
    int delegateTask(@Param("taskId") String taskId, @Param("responsibleType") Integer responsibleType,
                     @Param("responsibleId") Long responsibleId, @Param("delegateReason") String delegateReason,
                     @Param("delegateBy") Long delegateBy, @Param("updateBy") String updateBy);

    /**
     * 查询任务统计数据
     */
    Map<String, Object> selectTaskStatistics(@Param("startDate") Date startDate,
                                             @Param("endDate") Date endDate,
                                             @Param("deptId") String deptId,
                                             @Param("assetType") String assetType,
                                             @Param("priorities") List<Integer> priorities);

    /**
     * 查询任务完成率趋势数据
     */
    List<com.jingfang.maintenance_report.module.vo.MaintenanceReportVo.CompletionTrendVo> selectTaskCompletionTrends(
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("timeDimension") Integer timeDimension,
            @Param("deptId") String deptId,
            @Param("priorities") List<Integer> priorities);

    /**
     * 查询部门任务完成率统计
     */
    List<com.jingfang.maintenance_report.module.vo.MaintenanceReportVo.DeptCompletionVo> selectDeptCompletionStatistics(
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("deptIds") List<String> deptIds,
            @Param("priorities") List<Integer> priorities);

    /**
     * 查询优先级任务完成率统计
     */
    List<com.jingfang.maintenance_report.module.vo.MaintenanceReportVo.PriorityCompletionVo> selectPriorityCompletionStatistics(
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("deptId") String deptId);
}