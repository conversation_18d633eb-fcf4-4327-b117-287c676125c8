package com.jingfang.production.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jingfang.production.dto.ProductionTaskQueryDto;
import com.jingfang.production.module.entity.ProductionTask;
import com.jingfang.production.vo.ProductionTaskVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 生产任务Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-16
 */
@Mapper
public interface ProductionTaskMapper extends BaseMapper<ProductionTask> {

    /**
     * 分页查询生产任务列表
     *
     * @param page 分页参数
     * @param queryDto 查询条件
     * @return 生产任务列表
     */
    IPage<ProductionTaskVo> selectProductionTaskList(Page<ProductionTaskVo> page, @Param("query") ProductionTaskQueryDto queryDto);

    /**
     * 根据ID查询生产任务详情
     *
     * @param taskId 任务ID
     * @return 生产任务详情
     */
    ProductionTaskVo selectProductionTaskById(@Param("taskId") String taskId);

    /**
     * 查询任务状态统计
     *
     * @return 状态统计数据
     */
    List<Map<String, Object>> selectTaskStatusStatistics();

    /**
     * 查询任务进度统计
     *
     * @return 进度统计数据
     */
    List<Map<String, Object>> selectTaskProgressStatistics();

    /**
     * 查询即将到期的任务
     *
     * @param days 提前天数
     * @return 即将到期的任务列表
     */
    List<ProductionTaskVo> selectUpcomingTasks(@Param("days") Integer days);

    /**
     * 查询已过期的任务
     *
     * @return 已过期的任务列表
     */
    List<ProductionTaskVo> selectOverdueTasks();

    /**
     * 查询负责人的任务列表
     *
     * @param userId 负责人ID
     * @param status 任务状态（可选）
     * @return 任务列表
     */
    List<ProductionTaskVo> selectTasksByResponsibleUser(@Param("userId") Long userId, @Param("status") Integer status);

    /**
     * 更新任务进度
     *
     * @param taskId 任务ID
     * @param progressRate 进度百分比
     * @return 更新结果
     */
    int updateTaskProgress(@Param("taskId") String taskId, @Param("progressRate") java.math.BigDecimal progressRate);

    /**
     * 更新任务状态
     *
     * @param taskId 任务ID
     * @param status 新状态
     * @param updateBy 更新人
     * @return 更新结果
     */
    int updateTaskStatus(@Param("taskId") String taskId, @Param("status") Integer status, @Param("updateBy") String updateBy);

    /**
     * 查询任务的资源使用情况
     *
     * @param taskId 任务ID
     * @return 资源使用情况
     */
    Map<String, Object> selectTaskResourceUsage(@Param("taskId") String taskId);
}