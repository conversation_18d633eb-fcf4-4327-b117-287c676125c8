# 生产管控模块开发指南

## 📋 项目概述

本文档用于指导AI模型生成生产管控模块的完整代码，包括Controller、Service、Mapper等各层代码。

### 技术栈
- **框架**: 若依(RuoYi)框架
- **ORM**: MyBatis-Plus
- **数据库**: MySQL
- **构建工具**: Maven
- **Java版本**: JDK 8+

### 模块结构
```
# Controller层（在admin模块中）
device_monitor-admin/src/main/java/com/jingfang/web/controller/production/
├── ProductionTaskController.java
├── ProductionTaskNodeController.java
└── ProductionTaskNodeAttachmentController.java

# 业务层（在device_module模块中）
device_module/src/main/java/com/jingfang/production/
├── service/
│   ├── IProductionTaskService.java
│   ├── IProductionTaskNodeService.java
│   ├── IProductionTaskNodeAttachmentService.java
│   └── impl/
│       ├── ProductionTaskServiceImpl.java
│       ├── ProductionTaskNodeServiceImpl.java
│       └── ProductionTaskNodeAttachmentServiceImpl.java
├── mapper/
│   ├── ProductionTaskMapper.java
│   ├── ProductionTaskNodeMapper.java
│   ├── ProductionTaskAssetMapper.java
│   ├── ProductionTaskInventoryMapper.java
│   └── ProductionTaskNodeAttachmentMapper.java
├── entity/          # 实体类（已存在）
│   ├── ProductionTask.java
│   ├── ProductionTaskNode.java
│   ├── ProductionTaskAsset.java
│   ├── ProductionTaskInventory.java
│   └── ProductionTaskNodeAttachment.java
├── vo/              # 视图对象
│   ├── ProductionTaskVo.java
│   ├── ProductionTaskNodeVo.java
│   └── ProductionTaskNodeAttachmentVo.java
└── dto/             # 数据传输对象
    ├── ProductionTaskDto.java
    ├── ProductionTaskQueryDto.java
    ├── ProductionTaskNodeDto.java
    └── ProductionTaskNodeAttachmentDto.java

# XML映射文件
device_module/src/main/resources/mapper/production/
├── ProductionTaskMapper.xml
├── ProductionTaskNodeMapper.xml
├── ProductionTaskAssetMapper.xml
├── ProductionTaskInventoryMapper.xml
└── ProductionTaskNodeAttachmentMapper.xml
```

## 🎯 代码生成规范

### 1. 包命名规范
- **Controller**: `com.jingfang.web.controller.production`
- **Service**: `com.jingfang.production.module.service`
- **ServiceImpl**: `com.jingfang.production.module.service.impl`
- **Mapper**: `com.jingfang.production.module.mapper`
- **Entity**: `com.jingfang.production.module.entity`
- **VO**: `com.jingfang.production.module.vo`
- **DTO**: `com.jingfang.production.module.dto`

### 2. 类命名规范
- **Controller**: `{Entity}Controller`
- **Service接口**: `I{Entity}Service`
- **Service实现**: `{Entity}ServiceImpl`
- **Mapper**: `{Entity}Mapper`
- **VO**: `{Entity}Vo`
- **DTO**: `{Entity}Dto`

### 3. 方法命名规范
- **查询单个**: `select{Entity}By{Field}`
- **查询列表**: `select{Entity}List`
- **新增**: `insert{Entity}`
- **修改**: `update{Entity}`
- **删除**: `delete{Entity}By{Field}`
- **批量删除**: `delete{Entity}ByIds`

## 🏗️ 代码生成要求

### 1. Controller层要求（位于admin模块）

#### 基础注解
```java
@RestController
@RequestMapping("/production/{module}")
@Api(tags = "{模块名称}管理")
public class {Entity}Controller extends BaseController {
    // 控制器代码
}
```

#### 必需依赖注入
```java
@Autowired
private I{Entity}Service {entity}Service;
```

#### 接口方法要求
- 使用 `@ApiOperation` 注解描述接口功能
- 使用 `@PreAuthorize` 进行权限控制
- 使用 `@Log` 记录操作日志
- 返回类型统一使用 `AjaxResult`
- 分页查询使用 `TableDataInfo`

#### 权限标识规范
- 查询: `@PreAuthorize("@ss.hasPermi('production:{module}:query')")`
- 新增: `@PreAuthorize("@ss.hasPermi('production:{module}:add')")`
- 修改: `@PreAuthorize("@ss.hasPermi('production:{module}:edit')")`
- 删除: `@PreAuthorize("@ss.hasPermi('production:{module}:remove')")`
- 导出: `@PreAuthorize("@ss.hasPermi('production:{module}:export')")`

### 2. Service层要求（位于device_module模块）

#### Service接口
```java
public interface I{Entity}Service extends IService<{Entity}> {
    // 业务方法定义
}
```

#### Service实现类
```java
@Service
public class {Entity}ServiceImpl extends ServiceImpl<{Entity}Mapper, {Entity}> implements I{Entity}Service {
    // 业务逻辑实现
}
```

#### 事务管理
- 涉及数据修改的方法使用 `@Transactional`
- 只读操作使用 `@Transactional(readOnly = true)`

### 3. Mapper层要求（位于device_module模块）

#### Mapper接口
```java
@Mapper
public interface {Entity}Mapper extends BaseMapper<{Entity}> {
    // 自定义查询方法
}
```

#### XML映射文件路径
- 位置: `device_module/src/main/resources/mapper/production/{Entity}Mapper.xml`
- namespace: `com.jingfang.production.module.mapper.{Entity}Mapper`

### 4. VO/DTO要求（位于device_module模块）

#### VO类设计
- 用于前端展示的数据传输
- 包含关联表的字段信息
- 使用 `@ApiModel` 和 `@ApiModelProperty` 注解

#### DTO类设计
- 用于接收前端请求参数
- 包含验证注解 `@NotNull`, `@NotBlank` 等
- 继承分页参数类（如需要）

## 📝 具体实现指南

### 1. 生产任务管理 (ProductionTask)

#### 核心功能
- ✅ 基础CRUD操作
- ✅ 状态流转管理
- ✅ 进度跟踪
- ✅ 资源分配（资产+库存）

#### 特殊要求
- 创建/编辑任务时需要同时处理资产和库存分配
- 状态变更需要记录操作日志
- 进度计算基于节点完成情况

#### 关联表处理
- `production_task_asset` - 任务资产关联
- `production_task_inventory` - 任务库存关联
- `production_task_node` - 任务节点

### 2. 任务节点管理 (ProductionTaskNode)

#### 核心功能
- ✅ 节点CRUD操作
- ✅ 节点执行状态管理
- ✅ 节点排序功能

#### 特殊要求
- 节点状态变更影响任务整体进度
- 支持节点树形结构展示
- 节点完成时间影响任务进度计算

### 3. 节点附件管理 (ProductionTaskNodeAttachment)

#### 核心功能
- ✅ 附件关联管理
- ✅ 附件信息查询

#### 特殊要求
- 文件上传使用若依框架的通用接口
- 只管理附件与节点的关联关系

### 4. 统计报表功能

#### 核心功能
- ✅ 任务统计（状态、进度、耗时）
- ✅ 资源统计（资产使用率、库存消耗）

#### 特殊要求
- 统计数据需要实时计算
- 支持时间范围筛选
- 返回图表所需的数据格式

## 🔧 代码生成步骤

### 步骤1: 生成VO/DTO类（device_module模块）
1. 为每个实体创建对应的VO类
2. 创建查询条件DTO类
3. 创建请求参数DTO类

### 步骤2: 生成Mapper层（device_module模块）
1. 创建Mapper接口
2. 创建XML映射文件
3. 实现自定义查询方法

### 步骤3: 生成Service层（device_module模块）
1. 创建Service接口
2. 创建Service实现类
3. 实现业务逻辑方法

### 步骤4: 生成Controller层（admin模块）
1. 创建Controller类
2. 实现REST接口
3. 添加权限控制和日志记录

### 步骤5: 测试验证
1. 单元测试
2. 集成测试
3. API测试

## 📋 接口实现优先级

### 第一阶段（高优先级）
1. **ProductionTaskController** - 生产任务管理
   - 基础CRUD（5个接口）
   - 查询功能（3个接口）
   - 状态管理（5个接口）

2. **ProductionTaskNodeController** - 任务节点管理
   - 基础CRUD（4个接口）
   - 节点执行（4个接口）

### 第二阶段（中优先级）
1. **进度管理**（2个接口）
2. **资源查询**（4个接口）
3. **附件管理**（4个接口）

### 第三阶段（低优先级）
1. **统计报表**（7个接口）
2. **数据导出**（2个接口）
3. **高级功能**（1个接口）

## 🎨 代码模板示例

### Controller模板（admin模块）
```java
package com.jingfang.web.controller.production;

@RestController
@RequestMapping("/production/task")
@Api(tags = "生产任务管理")
public class ProductionTaskController extends BaseController {
    
    @Autowired
    private IProductionTaskService productionTaskService;
    
    @ApiOperation("查询生产任务列表")
    @PreAuthorize("@ss.hasPermi('production:task:query')")
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody ProductionTaskQueryDto queryDto) {
        startPage();
        List<ProductionTaskVo> list = productionTaskService.selectTaskList(queryDto);
        return getDataTable(list);
    }
}
```

### Service模板（device_module模块）
```java
package com.jingfang.production.module.service.impl;

@Service
public class ProductionTaskServiceImpl extends ServiceImpl<ProductionTaskMapper, ProductionTask> 
    implements IProductionTaskService {
    
    @Override
    @Transactional(readOnly = true)
    public List<ProductionTaskVo> selectTaskList(ProductionTaskQueryDto queryDto) {
        // 业务逻辑实现
        return null;
    }
}
```

## 📌 注意事项

1. **跨模块依赖**: Controller在admin模块，Service在device_module模块
2. **包路径正确性**: 确保import语句使用正确的包路径
3. **权限控制**: 所有接口都需要添加权限验证
4. **异常处理**: 使用若依框架的统一异常处理
5. **日志记录**: 重要操作需要记录操作日志
6. **事务管理**: 数据修改操作需要事务控制
7. **参数验证**: 使用JSR-303验证注解
8. **返回格式**: 统一使用AjaxResult和TableDataInfo

## 🚀 开始生成代码

请按照以上规范和优先级，逐步生成生产管控模块的完整代码。建议从第一阶段的ProductionTaskController开始实现。
