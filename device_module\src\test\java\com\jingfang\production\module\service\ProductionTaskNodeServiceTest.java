package com.jingfang.production.module.service;

import com.jingfang.common.utils.uuid.IdUtils;
import com.jingfang.production.module.dto.ProductionTaskNodeDto;
import com.jingfang.production.module.service.impl.ProductionTaskNodeServiceImpl;
import com.jingfang.production.module.vo.ProductionTaskNodeVo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 任务节点Service测试类
 */
@ExtendWith(MockitoExtension.class)
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class ProductionTaskNodeServiceTest {
    
    @Mock
    private IProductionTaskNodeService taskNodeService;
    
    @InjectMocks
    private ProductionTaskNodeServiceImpl taskNodeServiceImpl;
    
    private ProductionTaskNodeDto testNodeDto;
    private ProductionTaskNodeVo testNodeVo;
    private String testTaskId;
    private String testNodeId;
    
    @BeforeEach
    void setUp() {
        testTaskId = IdUtils.fastSimpleUUID();
        testNodeId = IdUtils.fastSimpleUUID();
        
        // 准备测试数据
        testNodeDto = new ProductionTaskNodeDto();
        testNodeDto.setNodeId(testNodeId);
        testNodeDto.setTaskId(testTaskId);
        testNodeDto.setNodeName("测试节点");
        testNodeDto.setNodeType(2);
        testNodeDto.setSequenceNo(1);
        testNodeDto.setIsRequired(1);
        testNodeDto.setEstimatedDuration(60);
        testNodeDto.setStatus(1);
        
        testNodeVo = new ProductionTaskNodeVo();
        testNodeVo.setNodeId(testNodeId);
        testNodeVo.setTaskId(testTaskId);
        testNodeVo.setTaskName("测试任务");
        testNodeVo.setNodeName("测试节点");
        testNodeVo.setNodeType(2);
        testNodeVo.setNodeTypeName("处理");
        testNodeVo.setSequenceNo(1);
        testNodeVo.setIsRequired(1);
        testNodeVo.setIsRequiredName("是");
        testNodeVo.setEstimatedDuration(60);
        testNodeVo.setActualDuration(null);
        testNodeVo.setStartTime(null);
        testNodeVo.setEndTime(null);
        testNodeVo.setStatus(1);
        testNodeVo.setStatusName("待执行");
        testNodeVo.setCreateTime(new Date());
        testNodeVo.setUpdateTime(new Date());
        testNodeVo.setAttachmentCount(0);
        testNodeVo.setCanExecute(true);
        testNodeVo.setProgressDescription("等待执行");
    }
    
    @Test
    void testSelectNodesByTaskId() {
        // 准备模拟数据
        List<ProductionTaskNodeVo> mockNodes = List.of(testNodeVo);
        
        // 模拟返回结果
        when(taskNodeService.selectNodesByTaskId(testTaskId)).thenReturn(mockNodes);
        
        // 执行测试
        List<ProductionTaskNodeVo> result = taskNodeService.selectNodesByTaskId(testTaskId);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testNodeId, result.get(0).getNodeId());
        assertEquals(testTaskId, result.get(0).getTaskId());
        assertEquals("测试节点", result.get(0).getNodeName());
        
        // 验证方法调用
        verify(taskNodeService, times(1)).selectNodesByTaskId(testTaskId);
    }
    
    @Test
    void testSelectNodeById() {
        // 模拟返回结果
        when(taskNodeService.selectNodeById(testNodeId)).thenReturn(testNodeVo);
        
        // 执行测试
        ProductionTaskNodeVo result = taskNodeService.selectNodeById(testNodeId);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(testNodeId, result.getNodeId());
        assertEquals("测试节点", result.getNodeName());
        assertEquals(2, result.getNodeType());
        assertEquals("处理", result.getNodeTypeName());
        assertEquals(1, result.getSequenceNo());
        
        // 验证方法调用
        verify(taskNodeService, times(1)).selectNodeById(testNodeId);
    }
    
    @Test
    void testInsertTaskNode() {
        // 模拟返回结果
        when(taskNodeService.insertTaskNode(any(ProductionTaskNodeDto.class))).thenReturn(true);
        
        // 执行测试
        boolean result = taskNodeService.insertTaskNode(testNodeDto);
        
        // 验证结果
        assertTrue(result);
        
        // 验证方法调用
        verify(taskNodeService, times(1)).insertTaskNode(any(ProductionTaskNodeDto.class));
    }
    
    @Test
    void testUpdateTaskNode() {
        // 模拟返回结果
        when(taskNodeService.updateTaskNode(any(ProductionTaskNodeDto.class))).thenReturn(true);
        
        // 修改测试数据
        testNodeDto.setNodeName("修改后的节点名称");
        testNodeDto.setEstimatedDuration(90);
        
        // 执行测试
        boolean result = taskNodeService.updateTaskNode(testNodeDto);
        
        // 验证结果
        assertTrue(result);
        
        // 验证方法调用
        verify(taskNodeService, times(1)).updateTaskNode(any(ProductionTaskNodeDto.class));
    }
    
    @Test
    void testDeleteTaskNode() {
        // 模拟返回结果
        when(taskNodeService.deleteTaskNode(testNodeId)).thenReturn(true);
        
        // 执行测试
        boolean result = taskNodeService.deleteTaskNode(testNodeId);
        
        // 验证结果
        assertTrue(result);
        
        // 验证方法调用
        verify(taskNodeService, times(1)).deleteTaskNode(testNodeId);
    }
    
    @Test
    void testStartNodeExecution() {
        // 模拟返回结果
        when(taskNodeService.startNodeExecution(testNodeId)).thenReturn(true);
        
        // 执行测试
        boolean result = taskNodeService.startNodeExecution(testNodeId);
        
        // 验证结果
        assertTrue(result);
        
        // 验证方法调用
        verify(taskNodeService, times(1)).startNodeExecution(testNodeId);
    }
    
    @Test
    void testCompleteNodeExecution() {
        // 模拟返回结果
        when(taskNodeService.completeNodeExecution(testNodeId)).thenReturn(true);
        
        // 执行测试
        boolean result = taskNodeService.completeNodeExecution(testNodeId);
        
        // 验证结果
        assertTrue(result);
        
        // 验证方法调用
        verify(taskNodeService, times(1)).completeNodeExecution(testNodeId);
    }
    
    @Test
    void testSkipNode() {
        // 模拟返回结果
        String reason = "测试跳过原因";
        when(taskNodeService.skipNode(testNodeId, reason)).thenReturn(true);
        
        // 执行测试
        boolean result = taskNodeService.skipNode(testNodeId, reason);
        
        // 验证结果
        assertTrue(result);
        
        // 验证方法调用
        verify(taskNodeService, times(1)).skipNode(testNodeId, reason);
    }
    
    @Test
    void testMarkNodeError() {
        // 模拟返回结果
        String errorMessage = "测试异常信息";
        when(taskNodeService.markNodeError(testNodeId, errorMessage)).thenReturn(true);
        
        // 执行测试
        boolean result = taskNodeService.markNodeError(testNodeId, errorMessage);
        
        // 验证结果
        assertTrue(result);
        
        // 验证方法调用
        verify(taskNodeService, times(1)).markNodeError(testNodeId, errorMessage);
    }
    
    @Test
    void testResetNodeStatus() {
        // 模拟返回结果
        when(taskNodeService.resetNodeStatus(testNodeId)).thenReturn(true);
        
        // 执行测试
        boolean result = taskNodeService.resetNodeStatus(testNodeId);
        
        // 验证结果
        assertTrue(result);
        
        // 验证方法调用
        verify(taskNodeService, times(1)).resetNodeStatus(testNodeId);
    }
    
    @Test
    void testSelectNodeStatisticsByTaskId() {
        // 准备模拟数据
        Map<String, Object> mockStatistics = Map.of(
                "total_nodes", 5,
                "pending_nodes", 3,
                "executing_nodes", 1,
                "completed_nodes", 1,
                "skipped_nodes", 0,
                "error_nodes", 0,
                "required_nodes", 4,
                "total_estimated_duration", 300,
                "total_actual_duration", 60
        );
        
        // 模拟返回结果
        when(taskNodeService.selectNodeStatisticsByTaskId(testTaskId)).thenReturn(mockStatistics);
        
        // 执行测试
        Map<String, Object> result = taskNodeService.selectNodeStatisticsByTaskId(testTaskId);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(5, result.get("total_nodes"));
        assertEquals(3, result.get("pending_nodes"));
        assertEquals(1, result.get("executing_nodes"));
        assertEquals(1, result.get("completed_nodes"));
        
        // 验证方法调用
        verify(taskNodeService, times(1)).selectNodeStatisticsByTaskId(testTaskId);
    }
    
    @Test
    void testSelectExecutableNodes() {
        // 准备模拟数据
        List<ProductionTaskNodeVo> mockNodes = List.of(testNodeVo);
        
        // 模拟返回结果
        when(taskNodeService.selectExecutableNodes(testTaskId)).thenReturn(mockNodes);
        
        // 执行测试
        List<ProductionTaskNodeVo> result = taskNodeService.selectExecutableNodes(testTaskId);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testNodeId, result.get(0).getNodeId());
        assertTrue(result.get(0).getCanExecute());
        
        // 验证方法调用
        verify(taskNodeService, times(1)).selectExecutableNodes(testTaskId);
    }
    
    @Test
    void testSelectNextNode() {
        // 模拟返回结果
        when(taskNodeService.selectNextNode(testTaskId, 1)).thenReturn(testNodeVo);
        
        // 执行测试
        ProductionTaskNodeVo result = taskNodeService.selectNextNode(testTaskId, 1);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(testNodeId, result.getNodeId());
        
        // 验证方法调用
        verify(taskNodeService, times(1)).selectNextNode(testTaskId, 1);
    }
    
    @Test
    void testBatchCreateNodes() {
        // 准备测试数据
        List<ProductionTaskNodeDto> nodeDtoList = List.of(testNodeDto);
        
        // 模拟返回结果
        when(taskNodeService.batchCreateNodes(testTaskId, nodeDtoList)).thenReturn(true);
        
        // 执行测试
        boolean result = taskNodeService.batchCreateNodes(testTaskId, nodeDtoList);
        
        // 验证结果
        assertTrue(result);
        
        // 验证方法调用
        verify(taskNodeService, times(1)).batchCreateNodes(testTaskId, nodeDtoList);
    }
    
    @Test
    void testCopyNodesToTask() {
        // 准备测试数据
        String targetTaskId = IdUtils.fastSimpleUUID();
        
        // 模拟返回结果
        when(taskNodeService.copyNodesToTask(testTaskId, targetTaskId)).thenReturn(true);
        
        // 执行测试
        boolean result = taskNodeService.copyNodesToTask(testTaskId, targetTaskId);
        
        // 验证结果
        assertTrue(result);
        
        // 验证方法调用
        verify(taskNodeService, times(1)).copyNodesToTask(testTaskId, targetTaskId);
    }
}
