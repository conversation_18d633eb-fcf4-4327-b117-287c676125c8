package com.jingfang.item_consumption_report.module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 库存消耗效率分析表
 * @TableName item_consumption_efficiency
 */
@TableName(value = "item_consumption_efficiency")
@Data
public class ItemConsumptionEfficiency implements Serializable {
    
    /**
     * 效率分析ID
     */
    @TableId(type = IdType.INPUT)
    private String efficiencyId;
    
    /**
     * 物品ID
     */
    private String itemId;
    
    /**
     * 分析周期(daily/weekly/monthly/quarterly)
     */
    private String analysisPeriod;
    
    /**
     * 周期开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date periodStartDate;
    
    /**
     * 周期结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date periodEndDate;
    
    /**
     * 总消耗量
     */
    private BigDecimal totalConsumption;
    
    /**
     * 总生产产量
     */
    private BigDecimal totalProductionOutput;
    
    /**
     * 单位产量消耗
     */
    private BigDecimal consumptionPerUnit;
    
    /**
     * 效率评分(0-100)
     */
    private BigDecimal efficiencyScore;
    
    /**
     * 效率等级(excellent/good/average/poor)
     */
    private String efficiencyLevel;
    
    /**
     * 基准消耗量
     */
    private BigDecimal benchmarkConsumption;
    
    /**
     * 效率偏差
     */
    private BigDecimal efficiencyVariance;
    
    /**
     * 单位产量成本
     */
    private BigDecimal costPerUnit;
    
    /**
     * 节约金额
     */
    private BigDecimal savingsAmount;
    
    /**
     * 优化建议
     */
    private String optimizationSuggestions;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    
    private static final long serialVersionUID = 1L;
}
