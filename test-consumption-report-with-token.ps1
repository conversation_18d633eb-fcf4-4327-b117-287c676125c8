# 库存消耗报表API测试脚本
# 使用正确的token进行测试

$baseUrl = "http://localhost:8080"
$token = "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiZjM0NDU2YTktYmU2Mi00MTY1LTk4ZjUtN2VhODNlM2VhNjBhIn0.xBoYuAG7vY_hqXeL7Siz_xiIG3ZjEOh_49L5cmvIFUvTHGWul0C33aZWLUbhXj6a4NHWO5aiIggbxC6MXerh_A"

Write-Host "=== 库存消耗报表API测试开始 ===" -ForegroundColor Green
Write-Host "服务地址: $baseUrl" -ForegroundColor Yellow
Write-Host ""

# 测试函数
function Test-API {
    param(
        [string]$Method,
        [string]$Url,
        [string]$Description,
        [string]$Body = $null
    )
    
    Write-Host "测试: $Description" -ForegroundColor Cyan
    Write-Host "请求: $Method $Url" -ForegroundColor Gray
    
    try {
        $headers = @{
            "Authorization" = "Bearer $token"
            "Content-Type" = "application/json"
        }
        
        if ($Body) {
            Write-Host "请求体: $Body" -ForegroundColor Gray
            $response = Invoke-RestMethod -Uri $Url -Method $Method -Headers $headers -Body $Body
        } else {
            $response = Invoke-RestMethod -Uri $Url -Method $Method -Headers $headers
        }
        
        Write-Host "✅ 成功" -ForegroundColor Green
        Write-Host "响应消息: $($response.msg)" -ForegroundColor White
        if ($response.data) {
            Write-Host "响应数据: $($response.data | ConvertTo-Json -Depth 2)" -ForegroundColor White
        }
        Write-Host ""
        return $true
    }
    catch {
        Write-Host "❌ 失败" -ForegroundColor Red
        Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
        if ($_.Exception.Response) {
            $statusCode = $_.Exception.Response.StatusCode
            Write-Host "状态码: $statusCode" -ForegroundColor Red
        }
        Write-Host ""
        return $false
    }
}

# 1. 测试获取优化建议（已移除权限验证）
Write-Host "=== 1. 基础接口测试 ===" -ForegroundColor Yellow
$url1 = "$baseUrl/item/consumption-report/optimization-suggestions?itemId=TEST_ITEM&analysisPeriod=monthly"
Test-API -Method "GET" -Url $url1 -Description "获取效率优化建议"

# 2. 测试生成消耗汇总数据（已移除权限验证）
$url2 = "$baseUrl/item/consumption-report/generate-summary?startDate=2024-01-01&endDate=2024-12-31"
Test-API -Method "POST" -Url $url2 -Description "生成消耗汇总数据"

# 3. 测试查询消耗报表列表（需要权限）
Write-Host "=== 2. 查询接口测试 ===" -ForegroundColor Yellow
$body3 = @{
    pageNum = 1
    pageSize = 10
    startDate = "2024-01-01"
    endDate = "2024-12-31"
} | ConvertTo-Json

$url3 = "$baseUrl/item/consumption-report/list"
Test-API -Method "POST" -Url $url3 -Description "分页查询消耗报表数据" -Body $body3

# 4. 测试查询消耗统计数据（需要权限）
$body4 = @{
    startDate = "2024-01-01"
    endDate = "2024-12-31"
    analysisPeriod = "monthly"
} | ConvertTo-Json

$url4 = "$baseUrl/item/consumption-report/statistics"
Test-API -Method "POST" -Url $url4 -Description "查询消耗统计数据" -Body $body4

# 5. 测试生成图表数据（需要权限）
$body5 = @{
    startDate = "2024-01-01"
    endDate = "2024-12-31"
    timeDimension = 3
} | ConvertTo-Json

$url5 = "$baseUrl/item/consumption-report/chart-data"
Test-API -Method "POST" -Url $url5 -Description "生成图表数据" -Body $body5

# 6. 测试查询消耗趋势数据（需要权限）
Write-Host "=== 3. 趋势分析接口测试 ===" -ForegroundColor Yellow
$url6 = "$baseUrl/item/consumption-report/trend?itemId=TEST_ITEM&startDate=2024-01-01&endDate=2024-12-31&timeDimension=3"
Test-API -Method "GET" -Url $url6 -Description "查询消耗趋势数据"

# 7. 测试查询消耗类型分布（需要权限）
$url7 = "$baseUrl/item/consumption-report/type-distribution?itemId=TEST_ITEM&startDate=2024-01-01&endDate=2024-12-31"
Test-API -Method "GET" -Url $url7 -Description "查询消耗类型分布数据"

# 8. 测试查询效率排行榜（需要权限）
Write-Host "=== 4. 排行榜接口测试 ===" -ForegroundColor Yellow
$body8 = @{
    startDate = "2024-01-01"
    endDate = "2024-12-31"
    analysisPeriod = "monthly"
} | ConvertTo-Json

$url8 = "$baseUrl/item/consumption-report/efficiency-ranking?limit=5"
Test-API -Method "POST" -Url $url8 -Description "查询消耗效率排行榜" -Body $body8

# 9. 测试查询消耗量排行榜（需要权限）
$body9 = @{
    startDate = "2024-01-01"
    endDate = "2024-12-31"
} | ConvertTo-Json

$url9 = "$baseUrl/item/consumption-report/consumption-ranking?limit=5"
Test-API -Method "POST" -Url $url9 -Description "查询消耗量排行榜" -Body $body9

# 10. 测试生成分析报告（需要权限）
Write-Host "=== 5. 分析报告接口测试 ===" -ForegroundColor Yellow
$body10 = @{
    startDate = "2024-01-01"
    endDate = "2024-12-31"
    analysisPeriod = "monthly"
    includeEfficiencyAnalysis = $true
    includeTrendAnalysis = $true
} | ConvertTo-Json

$url10 = "$baseUrl/item/consumption-report/analysis-report"
Test-API -Method "POST" -Url $url10 -Description "生成消耗分析报告" -Body $body10

Write-Host "=== 库存消耗报表API测试完成 ===" -ForegroundColor Green
Write-Host "请检查上述测试结果，确认接口功能是否正常" -ForegroundColor Yellow
