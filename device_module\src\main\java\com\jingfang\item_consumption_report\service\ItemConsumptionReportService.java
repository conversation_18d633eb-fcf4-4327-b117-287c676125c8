package com.jingfang.item_consumption_report.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.item_consumption_report.module.request.ItemConsumptionReportRequest;
import com.jingfang.item_consumption_report.module.vo.ItemConsumptionReportVo;
import com.jingfang.item_consumption_report.module.vo.ItemConsumptionStatisticsVo;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 库存消耗报表服务接口
 */
public interface ItemConsumptionReportService {
    
    /**
     * 分页查询库存消耗报表数据
     * 
     * @param request 查询条件
     * @return 消耗报表数据
     */
    IPage<ItemConsumptionReportVo> selectConsumptionReportList(ItemConsumptionReportRequest request);
    
    /**
     * 查询库存消耗统计数据
     * 
     * @param request 查询条件
     * @return 统计数据
     */
    ItemConsumptionStatisticsVo selectConsumptionStatistics(ItemConsumptionReportRequest request);
    
    /**
     * 查询消耗趋势数据
     * 
     * @param itemId 物品ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param timeDimension 时间维度
     * @return 趋势数据
     */
    List<ItemConsumptionReportVo.ConsumptionTrendVo> selectConsumptionTrend(String itemId, Date startDate, Date endDate, Integer timeDimension);
    
    /**
     * 查询消耗类型分布数据
     * 
     * @param itemId 物品ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 类型分布数据
     */
    List<ItemConsumptionReportVo.ConsumptionTypeVo> selectConsumptionTypeDistribution(String itemId, Date startDate, Date endDate);
    
    /**
     * 查询消耗效率排行榜
     * 
     * @param request 查询条件
     * @param limit 限制数量
     * @return 效率排行数据
     */
    List<ItemConsumptionReportVo> selectEfficiencyRanking(ItemConsumptionReportRequest request, Integer limit);
    
    /**
     * 查询消耗量排行榜
     * 
     * @param request 查询条件
     * @param limit 限制数量
     * @return 消耗量排行数据
     */
    List<ItemConsumptionReportVo> selectConsumptionRanking(ItemConsumptionReportRequest request, Integer limit);
    
    /**
     * 生成消耗汇总数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 生成的记录数
     */
    int generateConsumptionSummary(Date startDate, Date endDate);
    
    /**
     * 更新效率分析数据
     * 
     * @param itemId 物品ID
     * @param analysisPeriod 分析周期
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 更新的记录数
     */
    int updateEfficiencyAnalysis(String itemId, String analysisPeriod, Date startDate, Date endDate);
    
    /**
     * 生成图表数据
     * 
     * @param request 查询条件
     * @return 图表数据
     */
    Map<String, Object> generateChartData(ItemConsumptionReportRequest request);
    
    /**
     * 生成消耗分析报告
     * 
     * @param request 查询条件
     * @return 分析报告数据
     */
    Map<String, Object> generateConsumptionAnalysisReport(ItemConsumptionReportRequest request);
    
    /**
     * 导出消耗报表数据
     * 
     * @param request 查询条件
     * @return 导出数据
     */
    List<ItemConsumptionReportVo> exportConsumptionReport(ItemConsumptionReportRequest request);
    
    /**
     * 批量更新消耗汇总数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 更新结果
     */
    boolean batchUpdateConsumptionSummary(Date startDate, Date endDate);
    
    /**
     * 批量计算效率分析数据
     * 
     * @param analysisPeriod 分析周期
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 计算结果
     */
    boolean batchCalculateEfficiencyAnalysis(String analysisPeriod, Date startDate, Date endDate);
    
    /**
     * 获取消耗预警数据
     * 
     * @param request 查询条件
     * @return 预警数据
     */
    List<ItemConsumptionReportVo> getConsumptionAlerts(ItemConsumptionReportRequest request);
    
    /**
     * 获取效率优化建议
     * 
     * @param itemId 物品ID
     * @param analysisPeriod 分析周期
     * @return 优化建议
     */
    String getEfficiencyOptimizationSuggestions(String itemId, String analysisPeriod);
}
