package com.jingfang.production.module.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jingfang.common.utils.uuid.IdUtils;
import com.jingfang.production.module.dto.ProductionTaskNodeAttachmentDto;
import com.jingfang.production.module.entity.ProductionTaskNodeAttachment;
import com.jingfang.production.module.mapper.ProductionTaskNodeAttachmentMapper;
import com.jingfang.production.module.service.IProductionTaskNodeAttachmentService;
import com.jingfang.production.module.vo.ProductionTaskNodeAttachmentVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 任务节点附件Service业务层处理
 */
@Slf4j
@Service
public class ProductionTaskNodeAttachmentServiceImpl extends ServiceImpl<ProductionTaskNodeAttachmentMapper, ProductionTaskNodeAttachment> implements IProductionTaskNodeAttachmentService {
    
    @Autowired
    private ProductionTaskNodeAttachmentMapper attachmentMapper;
    
    @Override
    @Transactional(readOnly = true)
    public List<ProductionTaskNodeAttachmentVo> selectAttachmentsByNodeId(String nodeId) {
        return attachmentMapper.selectAttachmentsByNodeId(nodeId);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<ProductionTaskNodeAttachmentVo> selectAttachmentsByTaskId(String taskId) {
        return attachmentMapper.selectAttachmentsByTaskId(taskId);
    }
    
    @Override
    @Transactional(readOnly = true)
    public ProductionTaskNodeAttachmentVo selectAttachmentById(String attachmentId) {
        return attachmentMapper.selectAttachmentById(attachmentId);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertNodeAttachment(ProductionTaskNodeAttachmentDto attachmentDto, Long uploadUserId) {
        try {
            ProductionTaskNodeAttachment attachment = new ProductionTaskNodeAttachment();
            BeanUtils.copyProperties(attachmentDto, attachment);
            attachment.setAttachmentId(IdUtils.fastSimpleUUID());
            attachment.setUploadUserId(uploadUserId);
            attachment.setUploadTime(new Date());
            
            boolean result = this.save(attachment);
            log.info("上传节点附件成功，附件ID：{}，节点ID：{}", attachment.getAttachmentId(), attachment.getNodeId());
            return result;
        } catch (Exception e) {
            log.error("上传节点附件失败，节点ID：{}", attachmentDto.getNodeId(), e);
            throw new RuntimeException("上传节点附件失败：" + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateNodeAttachment(ProductionTaskNodeAttachmentDto attachmentDto) {
        try {
            ProductionTaskNodeAttachment attachment = new ProductionTaskNodeAttachment();
            BeanUtils.copyProperties(attachmentDto, attachment);
            
            boolean result = this.updateById(attachment);
            log.info("更新节点附件成功，附件ID：{}", attachment.getAttachmentId());
            return result;
        } catch (Exception e) {
            log.error("更新节点附件失败，附件ID：{}", attachmentDto.getAttachmentId(), e);
            throw new RuntimeException("更新节点附件失败：" + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteNodeAttachment(String attachmentId) {
        try {
            boolean result = this.removeById(attachmentId);
            log.info("删除节点附件成功，附件ID：{}", attachmentId);
            return result;
        } catch (Exception e) {
            log.error("删除节点附件失败，附件ID：{}", attachmentId, e);
            throw new RuntimeException("删除节点附件失败：" + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteNodeAttachments(List<String> attachmentIds) {
        try {
            boolean result = this.removeByIds(attachmentIds);
            log.info("批量删除节点附件成功，附件数量：{}", attachmentIds.size());
            return result;
        } catch (Exception e) {
            log.error("批量删除节点附件失败", e);
            throw new RuntimeException("批量删除节点附件失败：" + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteAttachmentsByNodeId(String nodeId) {
        try {
            int result = attachmentMapper.deleteAttachmentsByNodeId(nodeId);
            log.info("删除节点所有附件成功，节点ID：{}，删除数量：{}", nodeId, result);
            return result >= 0;
        } catch (Exception e) {
            log.error("删除节点所有附件失败，节点ID：{}", nodeId, e);
            throw new RuntimeException("删除节点所有附件失败：" + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteAttachmentsByTaskId(String taskId) {
        try {
            int result = attachmentMapper.deleteAttachmentsByTaskId(taskId);
            log.info("删除任务所有附件成功，任务ID：{}，删除数量：{}", taskId, result);
            return result >= 0;
        } catch (Exception e) {
            log.error("删除任务所有附件失败，任务ID：{}", taskId, e);
            throw new RuntimeException("删除任务所有附件失败：" + e.getMessage());
        }
    }
    
    @Override
    @Transactional(readOnly = true)
    public int countAttachmentsByNodeId(String nodeId) {
        return attachmentMapper.countAttachmentsByNodeId(nodeId);
    }
    
    @Override
    @Transactional(readOnly = true)
    public int countAttachmentsByTaskId(String taskId) {
        return attachmentMapper.countAttachmentsByTaskId(taskId);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUploadAttachments(String nodeId, List<ProductionTaskNodeAttachmentDto> attachmentDtoList, Long uploadUserId) {
        try {
            List<ProductionTaskNodeAttachment> attachmentList = attachmentDtoList.stream()
                .map(attachmentDto -> {
                    ProductionTaskNodeAttachment attachment = new ProductionTaskNodeAttachment();
                    BeanUtils.copyProperties(attachmentDto, attachment);
                    attachment.setAttachmentId(IdUtils.fastSimpleUUID());
                    attachment.setNodeId(nodeId);
                    attachment.setUploadUserId(uploadUserId);
                    attachment.setUploadTime(new Date());
                    return attachment;
                }).toList();
            
            int result = attachmentMapper.batchInsertAttachments(attachmentList);
            log.info("批量上传节点附件成功，节点ID：{}，附件数量：{}", nodeId, attachmentList.size());
            return result > 0;
        } catch (Exception e) {
            log.error("批量上传节点附件失败，节点ID：{}", nodeId, e);
            throw new RuntimeException("批量上传节点附件失败：" + e.getMessage());
        }
    }
    
    @Override
    @Transactional(readOnly = true)
    public String downloadAttachment(String attachmentId) {
        try {
            ProductionTaskNodeAttachmentVo attachment = selectAttachmentById(attachmentId);
            if (attachment == null) {
                throw new RuntimeException("附件不存在");
            }
            
            log.info("获取附件下载路径成功，附件ID：{}，文件路径：{}", attachmentId, attachment.getFilePath());
            return attachment.getFilePath();
        } catch (Exception e) {
            log.error("获取附件下载路径失败，附件ID：{}", attachmentId, e);
            throw new RuntimeException("获取附件下载路径失败：" + e.getMessage());
        }
    }
    
    @Override
    @Transactional(readOnly = true)
    public boolean canDeleteAttachment(String attachmentId, Long userId) {
        try {
            ProductionTaskNodeAttachmentVo attachment = selectAttachmentById(attachmentId);
            if (attachment == null) {
                return false;
            }
            
            // 检查是否是上传者或者有管理权限
            boolean canDelete = attachment.getUploadUserId().equals(userId) || attachment.getCanDelete();
            
            log.info("检查附件删除权限，附件ID：{}，用户ID：{}，可删除：{}", attachmentId, userId, canDelete);
            return canDelete;
        } catch (Exception e) {
            log.error("检查附件删除权限失败，附件ID：{}，用户ID：{}", attachmentId, userId, e);
            return false;
        }
    }
}
