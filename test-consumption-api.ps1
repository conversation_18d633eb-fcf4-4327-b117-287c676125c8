# 测试库存消耗报表API
$token = "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiZjM0NDU2YTktYmU2Mi00MTY1LTk4ZjUtN2VhODNlM2VhNjBhIn0.xBoYuAG7vY_hqXeL7Siz_xiIG3ZjEOh_49L5cmvIFUvTHGWul0C33aZWLUbhXj6a4NHWO5aiIggbxC6MXerh_A"

Write-Host "开始测试库存消耗报表API..." -ForegroundColor Green

# 测试1: 获取优化建议（无权限验证）
Write-Host "1. 测试获取优化建议" -ForegroundColor Yellow
try {
    $headers = @{}
    $headers.Add("Authorization", "Bearer $token")
    
    $response = Invoke-WebRequest -Uri "http://localhost:8080/item/consumption-report/optimization-suggestions?itemId=TEST_ITEM&analysisPeriod=monthly" -Method GET -Headers $headers
    Write-Host "状态码: $($response.StatusCode)" -ForegroundColor Green
    $content = $response.Content | ConvertFrom-Json
    Write-Host "响应: $($content.msg)" -ForegroundColor Cyan
    Write-Host "数据: $($content.data)" -ForegroundColor White
} catch {
    Write-Host "失败: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "状态码: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
        try {
            $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
            $responseBody = $reader.ReadToEnd()
            Write-Host "错误响应: $responseBody" -ForegroundColor Red
        } catch {}
    }
}

# 测试2: 生成消耗汇总数据（无权限验证）
Write-Host "`n2. 测试生成消耗汇总数据" -ForegroundColor Yellow
try {
    $headers = @{}
    $headers.Add("Authorization", "Bearer $token")
    
    $response = Invoke-WebRequest -Uri "http://localhost:8080/item/consumption-report/generate-summary?startDate=2024-01-01&endDate=2024-12-31" -Method POST -Headers $headers
    Write-Host "状态码: $($response.StatusCode)" -ForegroundColor Green
    $content = $response.Content | ConvertFrom-Json
    Write-Host "响应: $($content.msg)" -ForegroundColor Cyan
    Write-Host "数据: $($content.data)" -ForegroundColor White
} catch {
    Write-Host "失败: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "状态码: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
        try {
            $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
            $responseBody = $reader.ReadToEnd()
            Write-Host "错误响应: $responseBody" -ForegroundColor Red
        } catch {}
    }
}

Write-Host "`n测试完成!" -ForegroundColor Green
