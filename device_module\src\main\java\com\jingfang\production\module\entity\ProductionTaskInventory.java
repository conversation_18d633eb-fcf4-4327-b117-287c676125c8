package com.jingfang.production.module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 生产任务库存关联表
 * @TableName production_task_inventory
 */
@TableName(value = "production_task_inventory")
@Data
public class ProductionTaskInventory implements Serializable {
    
    /**
     * 关联ID
     */
    @TableId(type = IdType.INPUT)
    private String relationId;
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 物品ID
     */
    private String itemId;
    
    /**
     * 物品编码
     */
    private String itemCode;
    
    /**
     * 物品名称
     */
    private String itemName;
    
    /**
     * 预计使用数量
     */
    private BigDecimal plannedQuantity;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    
    /**
     * 备注
     */
    private String remark;
    
    private static final long serialVersionUID = 1L;
}