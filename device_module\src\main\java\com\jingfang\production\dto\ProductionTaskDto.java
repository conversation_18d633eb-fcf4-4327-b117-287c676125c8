package com.jingfang.production.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 生产任务DTO
 */
@Data
public class ProductionTaskDto implements Serializable {
    
    /**
     * 生产任务ID
     */
    private String taskId;
    
    /**
     * 任务名称
     */
    @NotBlank(message = "任务名称不能为空")
    private String taskName;
    
    /**
     * 任务编码
     */
    private String taskCode;
    
    /**
     * 任务类型(1-正常生产,2-紧急生产,3-试产)
     */
    @NotNull(message = "任务类型不能为空")
    private Integer taskType;
    
    /**
     * 产品名称
     */
    @NotBlank(message = "产品名称不能为空")
    private String productName;
    
    /**
     * 计划开始时间
     */
    @NotNull(message = "计划开始时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date planStartDate;
    
    /**
     * 计划结束时间
     */
    @NotNull(message = "计划结束时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date planEndDate;
    
    /**
     * 实际开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date actualStartTime;
    
    /**
     * 实际结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date actualEndTime;
    
    /**
     * 优先级(1-高,2-中,3-低)
     */
    @NotNull(message = "优先级不能为空")
    private Integer priorityLevel;
    
    /**
     * 负责人ID
     */
    @NotNull(message = "负责人不能为空")
    private Long responsibleUserId;
    
    /**
     * 预计总工时
     */
    private BigDecimal estimatedHours;
    
    /**
     * 实际总工时
     */
    private BigDecimal actualHours;
    
    /**
     * 状态(1-待执行,2-已分配,3-执行中,4-质检中,5-异常处理,6-已完工,7-已核算)
     */
    private Integer status;
    
    /**
     * 进度百分比
     */
    private BigDecimal progressRate;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 关联资产列表
     */
    private List<ProductionTaskAssetDto> assetList;
    
    /**
     * 关联库存列表
     */
    private List<ProductionTaskInventoryDto> inventoryList;
    
    /**
     * 任务节点列表
     */
    private List<ProductionTaskNodeDto> nodeList;
    
    private static final long serialVersionUID = 1L;
}
