package com.jingfang.production.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jingfang.production.dto.ProductionTaskNodeAttachmentDto;
import com.jingfang.production.module.entity.ProductionTaskNodeAttachment;
import com.jingfang.production.vo.ProductionTaskNodeAttachmentVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 任务节点附件Service接口
 *
 * <AUTHOR>
 * @date 2025-01-16
 */
public interface ProductionTaskNodeAttachmentService extends IService<ProductionTaskNodeAttachment> {

    /**
     * 根据节点ID查询附件列表
     *
     * @param nodeId 节点ID
     * @return 附件列表
     */
    List<ProductionTaskNodeAttachmentVo> selectAttachmentsByNodeId(String nodeId);

    /**
     * 根据任务ID查询所有附件
     *
     * @param taskId 任务ID
     * @return 附件列表
     */
    List<ProductionTaskNodeAttachmentVo> selectAttachmentsByTaskId(String taskId);

    /**
     * 根据附件ID查询附件详情
     *
     * @param attachmentId 附件ID
     * @return 附件详情
     */
    ProductionTaskNodeAttachmentVo selectAttachmentById(String attachmentId);

    /**
     * 上传附件
     *
     * @param nodeId 节点ID
     * @param file 上传文件
     * @param uploadUserId 上传人ID
     * @param remark 备注
     * @return 结果
     */
    boolean uploadAttachment(String nodeId, MultipartFile file, Long uploadUserId, String remark);

    /**
     * 新增附件记录
     *
     * @param attachmentDto 附件信息
     * @return 结果
     */
    boolean insertAttachment(ProductionTaskNodeAttachmentDto attachmentDto);

    /**
     * 修改附件信息
     *
     * @param attachmentDto 附件信息
     * @return 结果
     */
    boolean updateAttachment(ProductionTaskNodeAttachmentDto attachmentDto);

    /**
     * 删除附件
     *
     * @param attachmentId 附件ID
     * @return 结果
     */
    boolean deleteAttachment(String attachmentId);

    /**
     * 批量删除附件
     *
     * @param attachmentIds 附件ID列表
     * @return 结果
     */
    boolean deleteAttachments(List<String> attachmentIds);

    /**
     * 下载附件
     *
     * @param attachmentId 附件ID
     * @return 文件字节数组
     */
    byte[] downloadAttachment(String attachmentId);

    /**
     * 获取附件下载链接
     *
     * @param attachmentId 附件ID
     * @return 下载链接
     */
    String getDownloadUrl(String attachmentId);
}