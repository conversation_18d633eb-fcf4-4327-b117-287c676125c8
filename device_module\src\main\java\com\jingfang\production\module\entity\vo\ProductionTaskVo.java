package com.jingfang.production.module.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 生产任务VO
 */
@Data
public class ProductionTaskVo implements Serializable {
    
    /**
     * 生产任务ID
     */
    private String taskId;
    
    /**
     * 任务名称
     */
    private String taskName;
    
    /**
     * 任务编码
     */
    private String taskCode;
    
    /**
     * 任务类型(1-正常生产,2-紧急生产,3-试产)
     */
    private Integer taskType;
    
    /**
     * 任务类型名称
     */
    private String taskTypeName;
    
    /**
     * 产品名称
     */
    private String productName;
    
    /**
     * 计划开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date planStartDate;
    
    /**
     * 计划结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date planEndDate;
    
    /**
     * 实际开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date actualStartTime;
    
    /**
     * 实际结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date actualEndTime;
    
    /**
     * 优先级(1-高,2-中,3-低)
     */
    private Integer priorityLevel;
    
    /**
     * 优先级名称
     */
    private String priorityLevelName;
    
    /**
     * 负责人ID
     */
    private Long responsibleUserId;
    
    /**
     * 负责人姓名
     */
    private String responsibleUserName;
    
    /**
     * 预计总工时
     */
    private BigDecimal estimatedHours;
    
    /**
     * 实际总工时
     */
    private BigDecimal actualHours;
    
    /**
     * 状态(1-待执行,2-已分配,3-执行中,4-质检中,5-异常处理,6-已完工,7-已核算)
     */
    private Integer status;
    
    /**
     * 状态名称
     */
    private String statusName;
    
    /**
     * 进度百分比
     */
    private BigDecimal progressRate;
    
    /**
     * 创建者
     */
    private String createBy;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    
    /**
     * 更新者
     */
    private String updateBy;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 节点总数
     */
    private Integer totalNodes;
    
    /**
     * 已完成节点数
     */
    private Integer completedNodes;
    
    /**
     * 关联资产数量
     */
    private Integer assetCount;
    
    /**
     * 关联库存数量
     */
    private Integer inventoryCount;
    
    private static final long serialVersionUID = 1L;
}
