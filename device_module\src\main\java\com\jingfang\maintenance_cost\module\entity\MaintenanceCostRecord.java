package com.jingfang.maintenance_cost.module.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 维护成本记录表
 * @TableName maintenance_cost_record
 */
@TableName(value = "maintenance_cost_record")
@Data
public class MaintenanceCostRecord implements Serializable {
    
    /**
     * 成本记录ID
     */
    @TableId(type = IdType.INPUT)
    private String costId;
    
    /**
     * 维护任务ID
     */
    private String taskId;
    
    /**
     * 故障申报ID
     */
    private String faultId;
    
    /**
     * 资产ID
     */
    private String assetId;
    
    /**
     * 成本类型(1-人工成本,2-材料成本,3-外包成本,4-停机成本)
     */
    private Integer costType;
    
    /**
     * 成本金额
     */
    private BigDecimal costAmount;
    
    /**
     * 成本说明
     */
    private String costDescription;
    
    /**
     * 人工工时(小时)
     */
    private BigDecimal laborHours;
    
    /**
     * 小时费率
     */
    private BigDecimal hourlyRate;
    
    /**
     * 材料数量
     */
    private BigDecimal materialQuantity;
    
    /**
     * 材料单价
     */
    private BigDecimal unitPrice;
    
    /**
     * 停机时间(小时)
     */
    private BigDecimal downtimeHours;
    
    /**
     * 停机损失率(元/小时)
     */
    private BigDecimal downtimeLossRate;
    
    /**
     * 外包供应商名称
     */
    private String vendorName;
    
    /**
     * 记录时间
     */
    private Date recordTime;
    
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;
    
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    
    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;
    
    /**
     * 是否删除(0-否, 1-是)
     */
    @TableLogic
    private Integer deleted;
    
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    
    /**
     * 成本类型枚举
     */
    public enum CostType {
        LABOR(1, "人工成本"),
        MATERIAL(2, "材料成本"),
        OUTSOURCE(3, "外包成本"),
        DOWNTIME(4, "停机成本");
        
        private final Integer code;
        private final String description;
        
        CostType(Integer code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public Integer getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
        
        public static CostType fromCode(Integer code) {
            for (CostType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }
}
