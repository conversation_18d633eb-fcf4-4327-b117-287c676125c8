package com.jingfang.production.module.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 生产任务查询DTO
 */
@Data
public class ProductionTaskQueryDto implements Serializable {
    
    /**
     * 当前页码
     */
    private Integer pageNum = 1;
    
    /**
     * 每页数量
     */
    private Integer pageSize = 10;
    
    /**
     * 任务名称（模糊查询）
     */
    private String taskName;
    
    /**
     * 任务编码（模糊查询）
     */
    private String taskCode;
    
    /**
     * 任务类型(1-正常生产,2-紧急生产,3-试产)
     */
    private Integer taskType;
    
    /**
     * 产品名称（模糊查询）
     */
    private String productName;
    
    /**
     * 优先级(1-高,2-中,3-低)
     */
    private Integer priorityLevel;
    
    /**
     * 负责人ID
     */
    private Long responsibleUserId;
    
    /**
     * 状态(1-待执行,2-已分配,3-执行中,4-质检中,5-异常处理,6-已完工,7-已核算)
     */
    private Integer status;
    
    /**
     * 状态列表（多选查询）
     */
    private Integer[] statusList;
    
    /**
     * 计划开始时间-开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date planStartDateBegin;
    
    /**
     * 计划开始时间-结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date planStartDateEnd;
    
    /**
     * 计划结束时间-开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date planEndDateBegin;
    
    /**
     * 计划结束时间-结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date planEndDateEnd;
    
    /**
     * 创建时间-开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createTimeBegin;
    
    /**
     * 创建时间-结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createTimeEnd;
    
    /**
     * 创建者
     */
    private String createBy;
    
    /**
     * 排序字段
     */
    private String orderByColumn;
    
    /**
     * 排序方向（asc/desc）
     */
    private String isAsc;
    
    private static final long serialVersionUID = 1L;
}
