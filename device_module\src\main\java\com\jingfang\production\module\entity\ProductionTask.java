package com.jingfang.production.module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 生产任务表
 * @TableName production_task
 */
@TableName(value = "production_task")
@Data
public class ProductionTask implements Serializable {
    
    /**
     * 生产任务ID
     */
    @TableId(type = IdType.INPUT)
    private String taskId;
    
    /**
     * 任务名称
     */
    private String taskName;
    
    /**
     * 任务编码
     */
    private String taskCode;
    
    /**
     * 任务类型(1-正常生产,2-紧急生产,3-试产)
     */
    private Integer taskType;
    
    /**
     * 产品名称
     */
    private String productName;
    
    /**
     * 计划开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date planStartDate;
    
    /**
     * 计划结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date planEndDate;
    
    /**
     * 实际开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date actualStartTime;
    
    /**
     * 实际结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date actualEndTime;
    
    /**
     * 优先级(1-高,2-中,3-低)
     */
    private Integer priorityLevel;
    
    /**
     * 负责人ID
     */
    private Long responsibleUserId;
    
    /**
     * 预计总工时
     */
    private BigDecimal estimatedHours;
    
    /**
     * 实际总工时
     */
    private BigDecimal actualHours;
    
    /**
     * 状态(1-待执行,2-已分配,3-执行中,4-质检中,5-异常处理,6-已完工,7-已核算)
     */
    private Integer status;
    
    /**
     * 进度百分比
     */
    private BigDecimal progressRate;
    
    /**
     * 创建者
     */
    private String createBy;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    
    /**
     * 更新者
     */
    private String updateBy;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    
    /**
     * 备注
     */
    private String remark;
    
    private static final long serialVersionUID = 1L;
}