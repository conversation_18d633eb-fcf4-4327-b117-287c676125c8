@echo off
echo 测试库存消耗报表API...

set TOKEN=eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiZjM0NDU2YTktYmU2Mi00MTY1LTk4ZjUtN2VhODNlM2VhNjBhIn0.xBoYuAG7vY_hqXeL7Siz_xiIG3ZjEOh_49L5cmvIFUvTHGWul0C33aZWLUbhXj6a4NHWO5aiIggbxC6MXerh_A

echo.
echo 1. 测试获取优化建议...
curl -X GET "http://localhost:8080/item/consumption-report/optimization-suggestions?itemId=TEST_ITEM&analysisPeriod=monthly" -H "Authorization: Bearer %TOKEN%"

echo.
echo.
echo 2. 测试生成消耗汇总数据...
curl -X POST "http://localhost:8080/item/consumption-report/generate-summary?startDate=2024-01-01&endDate=2024-12-31" -H "Authorization: Bearer %TOKEN%"

echo.
echo.
echo 3. 测试查询消耗报表列表...
curl -X POST "http://localhost:8080/item/consumption-report/list" -H "Authorization: Bearer %TOKEN%" -H "Content-Type: application/json" -d "{\"pageNum\":1,\"pageSize\":10,\"startDate\":\"2024-01-01\",\"endDate\":\"2024-12-31\"}"

echo.
echo.
echo 4. 测试查询消耗统计数据...
curl -X POST "http://localhost:8080/item/consumption-report/statistics" -H "Authorization: Bearer %TOKEN%" -H "Content-Type: application/json" -d "{\"startDate\":\"2024-01-01\",\"endDate\":\"2024-12-31\"}"

echo.
echo.
echo 测试完成!
pause
