package com.jingfang.production.module.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.common.utils.uuid.IdUtils;
import com.jingfang.production.module.dto.ProductionTaskDto;
import com.jingfang.production.module.dto.ProductionTaskQueryDto;
import com.jingfang.production.module.entity.ProductionTask;
import com.jingfang.production.module.service.impl.ProductionTaskServiceImpl;
import com.jingfang.production.module.vo.ProductionTaskVo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 生产任务Service测试类
 */
@ExtendWith(MockitoExtension.class)
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class ProductionTaskServiceTest {
    
    @Mock
    private IProductionTaskService productionTaskService;
    
    @InjectMocks
    private ProductionTaskServiceImpl productionTaskServiceImpl;
    
    private ProductionTaskDto testTaskDto;
    private ProductionTaskVo testTaskVo;
    private String testTaskId;
    
    @BeforeEach
    void setUp() {
        testTaskId = IdUtils.fastSimpleUUID();
        
        // 准备测试数据
        testTaskDto = new ProductionTaskDto();
        testTaskDto.setTaskId(testTaskId);
        testTaskDto.setTaskName("测试生产任务");
        testTaskDto.setTaskType(1);
        testTaskDto.setProductName("测试产品");
        testTaskDto.setPlanStartDate(new Date());
        testTaskDto.setPlanEndDate(new Date(System.currentTimeMillis() + 86400000)); // 明天
        testTaskDto.setPriorityLevel(2);
        testTaskDto.setResponsibleUserId(1L);
        testTaskDto.setEstimatedHours(new BigDecimal("8.0"));
        testTaskDto.setRemark("测试备注");
        
        testTaskVo = new ProductionTaskVo();
        testTaskVo.setTaskId(testTaskId);
        testTaskVo.setTaskName("测试生产任务");
        testTaskVo.setTaskCode("SCRW202501160001");
        testTaskVo.setTaskType(1);
        testTaskVo.setTaskTypeName("正常生产");
        testTaskVo.setProductName("测试产品");
        testTaskVo.setPlanStartDate(new Date());
        testTaskVo.setPlanEndDate(new Date(System.currentTimeMillis() + 86400000));
        testTaskVo.setPriorityLevel(2);
        testTaskVo.setPriorityLevelName("中");
        testTaskVo.setResponsibleUserId(1L);
        testTaskVo.setResponsibleUserName("测试用户");
        testTaskVo.setEstimatedHours(new BigDecimal("8.0"));
        testTaskVo.setStatus(1);
        testTaskVo.setStatusName("待执行");
        testTaskVo.setProgressRate(BigDecimal.ZERO);
        testTaskVo.setCreateBy("admin");
        testTaskVo.setCreateTime(new Date());
        testTaskVo.setRemark("测试备注");
        testTaskVo.setTotalNodes(0);
        testTaskVo.setCompletedNodes(0);
        testTaskVo.setAssetCount(0);
        testTaskVo.setInventoryCount(0);
    }
    
    @Test
    void testSelectProductionTaskList() {
        // 准备查询条件
        ProductionTaskQueryDto queryDto = new ProductionTaskQueryDto();
        queryDto.setPageNum(1);
        queryDto.setPageSize(10);
        queryDto.setTaskName("测试");
        
        // 模拟返回数据
        IPage<ProductionTaskVo> mockPage = mock(IPage.class);
        when(mockPage.getRecords()).thenReturn(List.of(testTaskVo));
        when(mockPage.getTotal()).thenReturn(1L);
        when(productionTaskService.selectProductionTaskList(any(ProductionTaskQueryDto.class)))
                .thenReturn(mockPage);
        
        // 执行测试
        IPage<ProductionTaskVo> result = productionTaskService.selectProductionTaskList(queryDto);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getRecords().size());
        assertEquals(1L, result.getTotal());
        assertEquals(testTaskId, result.getRecords().get(0).getTaskId());
        
        // 验证方法调用
        verify(productionTaskService, times(1)).selectProductionTaskList(any(ProductionTaskQueryDto.class));
    }
    
    @Test
    void testSelectProductionTaskById() {
        // 模拟返回数据
        when(productionTaskService.selectProductionTaskById(testTaskId)).thenReturn(testTaskVo);
        
        // 执行测试
        ProductionTaskVo result = productionTaskService.selectProductionTaskById(testTaskId);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(testTaskId, result.getTaskId());
        assertEquals("测试生产任务", result.getTaskName());
        assertEquals("SCRW202501160001", result.getTaskCode());
        assertEquals(1, result.getTaskType());
        assertEquals("正常生产", result.getTaskTypeName());
        
        // 验证方法调用
        verify(productionTaskService, times(1)).selectProductionTaskById(testTaskId);
    }
    
    @Test
    void testInsertProductionTask() {
        // 模拟返回结果
        when(productionTaskService.insertProductionTask(any(ProductionTaskDto.class), anyString()))
                .thenReturn(true);
        
        // 执行测试
        boolean result = productionTaskService.insertProductionTask(testTaskDto, "admin");
        
        // 验证结果
        assertTrue(result);
        
        // 验证方法调用
        verify(productionTaskService, times(1))
                .insertProductionTask(any(ProductionTaskDto.class), eq("admin"));
    }
    
    @Test
    void testUpdateProductionTask() {
        // 模拟返回结果
        when(productionTaskService.updateProductionTask(any(ProductionTaskDto.class), anyString()))
                .thenReturn(true);
        
        // 修改测试数据
        testTaskDto.setTaskName("修改后的任务名称");
        testTaskDto.setRemark("修改后的备注");
        
        // 执行测试
        boolean result = productionTaskService.updateProductionTask(testTaskDto, "admin");
        
        // 验证结果
        assertTrue(result);
        
        // 验证方法调用
        verify(productionTaskService, times(1))
                .updateProductionTask(any(ProductionTaskDto.class), eq("admin"));
    }
    
    @Test
    void testDeleteProductionTask() {
        // 模拟返回结果
        when(productionTaskService.deleteProductionTask(testTaskId)).thenReturn(true);
        
        // 执行测试
        boolean result = productionTaskService.deleteProductionTask(testTaskId);
        
        // 验证结果
        assertTrue(result);
        
        // 验证方法调用
        verify(productionTaskService, times(1)).deleteProductionTask(testTaskId);
    }
    
    @Test
    void testStartTask() {
        // 模拟返回结果
        when(productionTaskService.startTask(testTaskId, "admin")).thenReturn(true);
        
        // 执行测试
        boolean result = productionTaskService.startTask(testTaskId, "admin");
        
        // 验证结果
        assertTrue(result);
        
        // 验证方法调用
        verify(productionTaskService, times(1)).startTask(testTaskId, "admin");
    }
    
    @Test
    void testCompleteTask() {
        // 模拟返回结果
        when(productionTaskService.completeTask(testTaskId, "admin")).thenReturn(true);
        
        // 执行测试
        boolean result = productionTaskService.completeTask(testTaskId, "admin");
        
        // 验证结果
        assertTrue(result);
        
        // 验证方法调用
        verify(productionTaskService, times(1)).completeTask(testTaskId, "admin");
    }
    
    @Test
    void testUpdateTaskProgress() {
        // 模拟返回结果
        BigDecimal progressRate = new BigDecimal("50.0");
        when(productionTaskService.updateTaskProgress(testTaskId, progressRate)).thenReturn(true);
        
        // 执行测试
        boolean result = productionTaskService.updateTaskProgress(testTaskId, progressRate);
        
        // 验证结果
        assertTrue(result);
        
        // 验证方法调用
        verify(productionTaskService, times(1)).updateTaskProgress(testTaskId, progressRate);
    }
    
    @Test
    void testAssignTask() {
        // 模拟返回结果
        Long responsibleUserId = 2L;
        when(productionTaskService.assignTask(testTaskId, responsibleUserId, "admin")).thenReturn(true);
        
        // 执行测试
        boolean result = productionTaskService.assignTask(testTaskId, responsibleUserId, "admin");
        
        // 验证结果
        assertTrue(result);
        
        // 验证方法调用
        verify(productionTaskService, times(1)).assignTask(testTaskId, responsibleUserId, "admin");
    }
    
    @Test
    void testSelectTaskStatusStatistics() {
        // 准备模拟数据
        List<Map<String, Object>> mockStatistics = List.of(
                Map.of("status", 1, "status_name", "待执行", "count", 5),
                Map.of("status", 2, "status_name", "已分配", "count", 3),
                Map.of("status", 3, "status_name", "执行中", "count", 2)
        );
        
        // 模拟返回结果
        when(productionTaskService.selectTaskStatusStatistics()).thenReturn(mockStatistics);
        
        // 执行测试
        List<Map<String, Object>> result = productionTaskService.selectTaskStatusStatistics();
        
        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals(1, result.get(0).get("status"));
        assertEquals("待执行", result.get(0).get("status_name"));
        assertEquals(5, result.get(0).get("count"));
        
        // 验证方法调用
        verify(productionTaskService, times(1)).selectTaskStatusStatistics();
    }
    
    @Test
    void testSelectUpcomingTasks() {
        // 准备模拟数据
        List<ProductionTaskVo> mockTasks = List.of(testTaskVo);
        
        // 模拟返回结果
        when(productionTaskService.selectUpcomingTasks(7)).thenReturn(mockTasks);
        
        // 执行测试
        List<ProductionTaskVo> result = productionTaskService.selectUpcomingTasks(7);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testTaskId, result.get(0).getTaskId());
        
        // 验证方法调用
        verify(productionTaskService, times(1)).selectUpcomingTasks(7);
    }
    
    @Test
    void testCopyTask() {
        // 模拟返回结果
        when(productionTaskService.copyTask(testTaskId, "复制的任务", "admin")).thenReturn(true);
        
        // 执行测试
        boolean result = productionTaskService.copyTask(testTaskId, "复制的任务", "admin");
        
        // 验证结果
        assertTrue(result);
        
        // 验证方法调用
        verify(productionTaskService, times(1)).copyTask(testTaskId, "复制的任务", "admin");
    }
}
