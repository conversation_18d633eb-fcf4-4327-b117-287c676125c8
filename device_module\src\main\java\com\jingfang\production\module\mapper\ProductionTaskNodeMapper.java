package com.jingfang.production.module.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jingfang.production.module.entity.ProductionTaskNode;
import com.jingfang.production.module.vo.ProductionTaskNodeVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 任务节点Mapper接口
 */
@Mapper
public interface ProductionTaskNodeMapper extends BaseMapper<ProductionTaskNode> {
    
    /**
     * 根据任务ID查询节点列表
     * 
     * @param taskId 任务ID
     * @return 节点列表
     */
    List<ProductionTaskNodeVo> selectNodesByTaskId(@Param("taskId") String taskId);
    
    /**
     * 根据节点ID查询节点详情
     * 
     * @param nodeId 节点ID
     * @return 节点详情
     */
    ProductionTaskNodeVo selectNodeById(@Param("nodeId") String nodeId);
    
    /**
     * 查询任务的节点统计信息
     * 
     * @param taskId 任务ID
     * @return 节点统计信息
     */
    Map<String, Object> selectNodeStatisticsByTaskId(@Param("taskId") String taskId);
    
    /**
     * 查询节点执行状态统计
     * 
     * @param taskId 任务ID（可选）
     * @return 状态统计数据
     */
    List<Map<String, Object>> selectNodeStatusStatistics(@Param("taskId") String taskId);
    
    /**
     * 查询可执行的节点（前置节点已完成）
     * 
     * @param taskId 任务ID
     * @return 可执行的节点列表
     */
    List<ProductionTaskNodeVo> selectExecutableNodes(@Param("taskId") String taskId);
    
    /**
     * 查询正在执行的节点
     * 
     * @param taskId 任务ID（可选）
     * @return 正在执行的节点列表
     */
    List<ProductionTaskNodeVo> selectExecutingNodes(@Param("taskId") String taskId);
    
    /**
     * 更新节点状态
     * 
     * @param nodeId 节点ID
     * @param status 新状态
     * @param updateTime 更新时间
     * @return 更新结果
     */
    int updateNodeStatus(@Param("nodeId") String nodeId, @Param("status") Integer status, @Param("updateTime") Date updateTime);
    
    /**
     * 开始执行节点
     * 
     * @param nodeId 节点ID
     * @param startTime 开始时间
     * @return 更新结果
     */
    int startNodeExecution(@Param("nodeId") String nodeId, @Param("startTime") Date startTime);
    
    /**
     * 完成节点执行
     * 
     * @param nodeId 节点ID
     * @param endTime 结束时间
     * @param actualDuration 实际耗时（分钟）
     * @return 更新结果
     */
    int completeNodeExecution(@Param("nodeId") String nodeId, @Param("endTime") Date endTime, @Param("actualDuration") Integer actualDuration);
    
    /**
     * 根据序号查询下一个节点
     * 
     * @param taskId 任务ID
     * @param currentSequenceNo 当前节点序号
     * @return 下一个节点
     */
    ProductionTaskNodeVo selectNextNode(@Param("taskId") String taskId, @Param("currentSequenceNo") Integer currentSequenceNo);
    
    /**
     * 查询节点的前置节点
     * 
     * @param taskId 任务ID
     * @param sequenceNo 节点序号
     * @return 前置节点列表
     */
    List<ProductionTaskNodeVo> selectPreviousNodes(@Param("taskId") String taskId, @Param("sequenceNo") Integer sequenceNo);
    
    /**
     * 批量更新节点序号
     * 
     * @param nodeList 节点列表
     * @return 更新结果
     */
    int batchUpdateNodeSequence(@Param("nodeList") List<ProductionTaskNode> nodeList);
}
