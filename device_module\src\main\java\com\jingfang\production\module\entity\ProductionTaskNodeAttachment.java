package com.jingfang.production.module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 任务节点附件表
 * @TableName production_task_node_attachment
 */
@TableName(value = "production_task_node_attachment")
@Data
public class ProductionTaskNodeAttachment implements Serializable {
    
    /**
     * 附件ID
     */
    @TableId(type = IdType.INPUT)
    private String attachmentId;
    
    /**
     * 节点ID
     */
    private String nodeId;
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 文件类型
     */
    private String fileType;
    
    /**
     * 文件大小(字节)
     */
    private Long fileSize;
    
    /**
     * 文件存储路径
     */
    private String filePath;
    
    /**
     * 上传人ID
     */
    private Long uploadUserId;
    
    /**
     * 上传时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date uploadTime;
    
    /**
     * 备注
     */
    private String remark;
    
    private static final long serialVersionUID = 1L;
}