package com.jingfang.production.module.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jingfang.common.utils.uuid.IdUtils;
import com.jingfang.production.module.dto.ProductionTaskNodeDto;
import com.jingfang.production.module.entity.ProductionTaskNode;
import com.jingfang.production.module.mapper.ProductionTaskNodeMapper;
import com.jingfang.production.module.service.IProductionTaskNodeService;
import com.jingfang.production.module.vo.ProductionTaskNodeVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 任务节点Service业务层处理
 */
@Slf4j
@Service
public class ProductionTaskNodeServiceImpl extends ServiceImpl<ProductionTaskNodeMapper, ProductionTaskNode> implements IProductionTaskNodeService {
    
    @Autowired
    private ProductionTaskNodeMapper productionTaskNodeMapper;
    
    @Override
    @Transactional(readOnly = true)
    public List<ProductionTaskNodeVo> selectNodesByTaskId(String taskId) {
        return productionTaskNodeMapper.selectNodesByTaskId(taskId);
    }
    
    @Override
    @Transactional(readOnly = true)
    public ProductionTaskNodeVo selectNodeById(String nodeId) {
        return productionTaskNodeMapper.selectNodeById(nodeId);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertTaskNode(ProductionTaskNodeDto nodeDto) {
        try {
            ProductionTaskNode node = new ProductionTaskNode();
            BeanUtils.copyProperties(nodeDto, node);
            node.setNodeId(IdUtils.fastSimpleUUID());
            node.setStatus(1); // 待执行
            node.setCreateTime(new Date());
            node.setUpdateTime(new Date());
            
            boolean result = this.save(node);
            log.info("创建任务节点成功，节点ID：{}", node.getNodeId());
            return result;
        } catch (Exception e) {
            log.error("创建任务节点失败", e);
            throw new RuntimeException("创建任务节点失败：" + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTaskNode(ProductionTaskNodeDto nodeDto) {
        try {
            ProductionTaskNode node = new ProductionTaskNode();
            BeanUtils.copyProperties(nodeDto, node);
            node.setUpdateTime(new Date());
            
            boolean result = this.updateById(node);
            log.info("更新任务节点成功，节点ID：{}", node.getNodeId());
            return result;
        } catch (Exception e) {
            log.error("更新任务节点失败，节点ID：{}", nodeDto.getNodeId(), e);
            throw new RuntimeException("更新任务节点失败：" + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTaskNode(String nodeId) {
        try {
            boolean result = this.removeById(nodeId);
            log.info("删除任务节点成功，节点ID：{}", nodeId);
            return result;
        } catch (Exception e) {
            log.error("删除任务节点失败，节点ID：{}", nodeId, e);
            throw new RuntimeException("删除任务节点失败：" + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTaskNodes(List<String> nodeIds) {
        try {
            boolean result = this.removeByIds(nodeIds);
            log.info("批量删除任务节点成功，节点数量：{}", nodeIds.size());
            return result;
        } catch (Exception e) {
            log.error("批量删除任务节点失败", e);
            throw new RuntimeException("批量删除任务节点失败：" + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean startNodeExecution(String nodeId) {
        try {
            int result = productionTaskNodeMapper.startNodeExecution(nodeId, new Date());
            log.info("开始执行节点成功，节点ID：{}", nodeId);
            return result > 0;
        } catch (Exception e) {
            log.error("开始执行节点失败，节点ID：{}", nodeId, e);
            throw new RuntimeException("开始执行节点失败：" + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean completeNodeExecution(String nodeId) {
        try {
            // 获取节点信息计算实际耗时
            ProductionTaskNode node = this.getById(nodeId);
            if (node == null) {
                throw new RuntimeException("节点不存在");
            }
            
            Date endTime = new Date();
            Integer actualDuration = null;
            
            if (node.getStartTime() != null) {
                long durationMillis = endTime.getTime() - node.getStartTime().getTime();
                actualDuration = (int) (durationMillis / 60000); // 转换为分钟
            }
            
            int result = productionTaskNodeMapper.completeNodeExecution(nodeId, endTime, actualDuration);
            log.info("完成节点执行成功，节点ID：{}，实际耗时：{}分钟", nodeId, actualDuration);
            return result > 0;
        } catch (Exception e) {
            log.error("完成节点执行失败，节点ID：{}", nodeId, e);
            throw new RuntimeException("完成节点执行失败：" + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean skipNode(String nodeId, String reason) {
        try {
            int result = productionTaskNodeMapper.updateNodeStatus(nodeId, 4, new Date()); // 跳过
            log.info("跳过节点成功，节点ID：{}，原因：{}", nodeId, reason);
            return result > 0;
        } catch (Exception e) {
            log.error("跳过节点失败，节点ID：{}", nodeId, e);
            throw new RuntimeException("跳过节点失败：" + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean markNodeError(String nodeId, String errorMessage) {
        try {
            int result = productionTaskNodeMapper.updateNodeStatus(nodeId, 5, new Date()); // 异常
            log.info("标记节点异常成功，节点ID：{}，异常信息：{}", nodeId, errorMessage);
            return result > 0;
        } catch (Exception e) {
            log.error("标记节点异常失败，节点ID：{}", nodeId, e);
            throw new RuntimeException("标记节点异常失败：" + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean resetNodeStatus(String nodeId) {
        try {
            ProductionTaskNode node = new ProductionTaskNode();
            node.setNodeId(nodeId);
            node.setStatus(1); // 待执行
            node.setStartTime(null);
            node.setEndTime(null);
            node.setActualDuration(null);
            node.setUpdateTime(new Date());
            
            boolean result = this.updateById(node);
            log.info("重置节点状态成功，节点ID：{}", nodeId);
            return result;
        } catch (Exception e) {
            log.error("重置节点状态失败，节点ID：{}", nodeId, e);
            throw new RuntimeException("重置节点状态失败：" + e.getMessage());
        }
    }
    
    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> selectNodeStatisticsByTaskId(String taskId) {
        return productionTaskNodeMapper.selectNodeStatisticsByTaskId(taskId);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<Map<String, Object>> selectNodeStatusStatistics(String taskId) {
        return productionTaskNodeMapper.selectNodeStatusStatistics(taskId);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<ProductionTaskNodeVo> selectExecutableNodes(String taskId) {
        return productionTaskNodeMapper.selectExecutableNodes(taskId);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<ProductionTaskNodeVo> selectExecutingNodes(String taskId) {
        return productionTaskNodeMapper.selectExecutingNodes(taskId);
    }
    
    @Override
    @Transactional(readOnly = true)
    public ProductionTaskNodeVo selectNextNode(String taskId, Integer currentSequenceNo) {
        return productionTaskNodeMapper.selectNextNode(taskId, currentSequenceNo);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<ProductionTaskNodeVo> selectPreviousNodes(String taskId, Integer sequenceNo) {
        return productionTaskNodeMapper.selectPreviousNodes(taskId, sequenceNo);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean adjustNodeSequence(String taskId, List<Map<String, Object>> nodeSequenceList) {
        try {
            List<ProductionTaskNode> nodeList = nodeSequenceList.stream()
                .map(item -> {
                    ProductionTaskNode node = new ProductionTaskNode();
                    node.setNodeId((String) item.get("nodeId"));
                    node.setSequenceNo((Integer) item.get("sequenceNo"));
                    return node;
                }).toList();
            
            int result = productionTaskNodeMapper.batchUpdateNodeSequence(nodeList);
            log.info("调整节点顺序成功，任务ID：{}，节点数量：{}", taskId, nodeList.size());
            return result > 0;
        } catch (Exception e) {
            log.error("调整节点顺序失败，任务ID：{}", taskId, e);
            throw new RuntimeException("调整节点顺序失败：" + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchCreateNodes(String taskId, List<ProductionTaskNodeDto> nodeDtoList) {
        try {
            List<ProductionTaskNode> nodeList = nodeDtoList.stream()
                .map(nodeDto -> {
                    ProductionTaskNode node = new ProductionTaskNode();
                    BeanUtils.copyProperties(nodeDto, node);
                    node.setNodeId(IdUtils.fastSimpleUUID());
                    node.setTaskId(taskId);
                    node.setStatus(1); // 待执行
                    node.setCreateTime(new Date());
                    node.setUpdateTime(new Date());
                    return node;
                }).toList();
            
            boolean result = this.saveBatch(nodeList);
            log.info("批量创建节点成功，任务ID：{}，节点数量：{}", taskId, nodeList.size());
            return result;
        } catch (Exception e) {
            log.error("批量创建节点失败，任务ID：{}", taskId, e);
            throw new RuntimeException("批量创建节点失败：" + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean copyNodesToTask(String sourceTaskId, String targetTaskId) {
        try {
            List<ProductionTaskNodeVo> sourceNodes = selectNodesByTaskId(sourceTaskId);
            if (sourceNodes.isEmpty()) {
                return true;
            }
            
            List<ProductionTaskNode> targetNodes = sourceNodes.stream()
                .map(sourceNode -> {
                    ProductionTaskNode targetNode = new ProductionTaskNode();
                    targetNode.setNodeId(IdUtils.fastSimpleUUID());
                    targetNode.setTaskId(targetTaskId);
                    targetNode.setNodeName(sourceNode.getNodeName());
                    targetNode.setNodeType(sourceNode.getNodeType());
                    targetNode.setSequenceNo(sourceNode.getSequenceNo());
                    targetNode.setIsRequired(sourceNode.getIsRequired());
                    targetNode.setEstimatedDuration(sourceNode.getEstimatedDuration());
                    targetNode.setStatus(1); // 待执行
                    targetNode.setCreateTime(new Date());
                    targetNode.setUpdateTime(new Date());
                    return targetNode;
                }).toList();
            
            boolean result = this.saveBatch(targetNodes);
            log.info("复制节点到任务成功，源任务ID：{}，目标任务ID：{}，节点数量：{}", 
                    sourceTaskId, targetTaskId, targetNodes.size());
            return result;
        } catch (Exception e) {
            log.error("复制节点到任务失败，源任务ID：{}，目标任务ID：{}", sourceTaskId, targetTaskId, e);
            throw new RuntimeException("复制节点到任务失败：" + e.getMessage());
        }
    }
}
