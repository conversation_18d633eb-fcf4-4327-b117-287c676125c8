package com.jingfang.common.handler;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * JSON字符串与List<String>类型转换处理器
 * 用于处理数据库中JSON格式字符串与Java List<String>对象之间的转换
 * 
 * <AUTHOR>
 */
@MappedTypes(List.class)
@MappedJdbcTypes(JdbcType.VARCHAR)
public class JsonStringListTypeHandler extends BaseTypeHandler<List<String>> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<String> parameter, JdbcType jdbcType) throws SQLException {
        // 将List<String>转换为JSON字符串存储到数据库
        if (parameter == null || parameter.isEmpty()) {
            ps.setString(i, "[]");
        } else {
            ps.setString(i, JSON.toJSONString(parameter));
        }
    }

    @Override
    public List<String> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        // 从数据库读取JSON字符串并转换为List<String>
        String jsonString = rs.getString(columnName);
        return parseJsonToList(jsonString);
    }

    @Override
    public List<String> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        // 从数据库读取JSON字符串并转换为List<String>
        String jsonString = rs.getString(columnIndex);
        return parseJsonToList(jsonString);
    }

    @Override
    public List<String> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        // 从数据库读取JSON字符串并转换为List<String>
        String jsonString = cs.getString(columnIndex);
        return parseJsonToList(jsonString);
    }

    /**
     * 解析JSON字符串为List<String>
     * 
     * @param jsonString JSON字符串
     * @return List<String>对象
     */
    private List<String> parseJsonToList(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        try {
            // 使用FastJSON2解析JSON字符串为List<String>
            return JSON.parseObject(jsonString, new TypeReference<List<String>>() {});
        } catch (Exception e) {
            // 如果解析失败，返回空列表
            return new ArrayList<>();
        }
    }
}
