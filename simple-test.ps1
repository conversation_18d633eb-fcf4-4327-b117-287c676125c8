# 简单的API测试脚本
$baseUrl = "http://localhost:8080"
$token = "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiZjM0NDU2YTktYmU2Mi00MTY1LTk4ZjUtN2VhODNlM2VhNjBhIn0.xBoYuAG7vY_hqXeL7Siz_xiIG3ZjEOh_49L5cmvIFUvTHGWul0C33aZWLUbhXj6a4NHWO5aiIggbxC6MXerh_A"

Write-Host "开始测试库存消耗报表API..." -ForegroundColor Green

# 测试1: 生成消耗汇总数据
Write-Host "1. 测试生成消耗汇总数据" -ForegroundColor Yellow
try {
    $url = "$baseUrl/item/consumption-report/generate-summary?startDate=2024-01-01&endDate=2024-12-31"
    $response = Invoke-RestMethod -Uri $url -Method POST -Headers @{"Authorization"="Bearer $token"}
    Write-Host "成功: $($response.msg)" -ForegroundColor Green
} catch {
    Write-Host "失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试2: 查询消耗报表列表
Write-Host "2. 测试查询消耗报表列表" -ForegroundColor Yellow
try {
    $url = "$baseUrl/item/consumption-report/list"
    $body = '{"pageNum":1,"pageSize":10,"startDate":"2024-01-01","endDate":"2024-12-31"}'
    $response = Invoke-RestMethod -Uri $url -Method POST -Headers @{"Authorization"="Bearer $token";"Content-Type"="application/json"} -Body $body
    Write-Host "成功: 查询到数据" -ForegroundColor Green
} catch {
    Write-Host "失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试3: 查询消耗统计数据
Write-Host "3. 测试查询消耗统计数据" -ForegroundColor Yellow
try {
    $url = "$baseUrl/item/consumption-report/statistics"
    $body = '{"startDate":"2024-01-01","endDate":"2024-12-31"}'
    $response = Invoke-RestMethod -Uri $url -Method POST -Headers @{"Authorization"="Bearer $token";"Content-Type"="application/json"} -Body $body
    Write-Host "成功: 获取统计数据" -ForegroundColor Green
} catch {
    Write-Host "失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试4: 生成图表数据
Write-Host "4. 测试生成图表数据" -ForegroundColor Yellow
try {
    $url = "$baseUrl/item/consumption-report/chart-data"
    $body = '{"startDate":"2024-01-01","endDate":"2024-12-31"}'
    $response = Invoke-RestMethod -Uri $url -Method POST -Headers @{"Authorization"="Bearer $token";"Content-Type"="application/json"} -Body $body
    Write-Host "成功: 生成图表数据" -ForegroundColor Green
} catch {
    Write-Host "失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试5: 获取优化建议
Write-Host "5. 测试获取优化建议" -ForegroundColor Yellow
try {
    $url = "$baseUrl/item/consumption-report/optimization-suggestions?itemId=TEST_ITEM&analysisPeriod=monthly"
    $response = Invoke-RestMethod -Uri $url -Method GET -Headers @{"Authorization"="Bearer $token"}
    Write-Host "成功: 获取优化建议" -ForegroundColor Green
} catch {
    Write-Host "失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "测试完成!" -ForegroundColor Green
