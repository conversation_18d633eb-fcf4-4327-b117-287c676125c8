package com.jingfang.production.module.integration;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.common.utils.uuid.IdUtils;
import com.jingfang.production.module.dto.ProductionTaskDto;
import com.jingfang.production.module.dto.ProductionTaskNodeDto;
import com.jingfang.production.module.dto.ProductionTaskQueryDto;
import com.jingfang.production.module.service.IProductionTaskNodeService;
import com.jingfang.production.module.service.IProductionTaskService;
import com.jingfang.production.module.vo.ProductionTaskNodeVo;
import com.jingfang.production.module.vo.ProductionTaskVo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 生产管控模块集成测试类
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class ProductionTaskIntegrationTest {
    
    @Autowired
    private IProductionTaskService productionTaskService;
    
    @Autowired
    private IProductionTaskNodeService taskNodeService;
    
    private ProductionTaskDto testTaskDto;
    private String testTaskId;
    
    @BeforeEach
    void setUp() {
        testTaskId = IdUtils.fastSimpleUUID();
        
        // 准备测试数据
        testTaskDto = new ProductionTaskDto();
        testTaskDto.setTaskId(testTaskId);
        testTaskDto.setTaskName("集成测试生产任务");
        testTaskDto.setTaskType(1);
        testTaskDto.setProductName("集成测试产品");
        testTaskDto.setPlanStartDate(new Date());
        testTaskDto.setPlanEndDate(new Date(System.currentTimeMillis() + 86400000));
        testTaskDto.setPriorityLevel(1);
        testTaskDto.setResponsibleUserId(1L);
        testTaskDto.setEstimatedHours(new BigDecimal("16.0"));
        testTaskDto.setRemark("集成测试备注");
    }
    
    @Test
    void testCompleteProductionTaskWorkflow() {
        // 1. 创建生产任务
        boolean createResult = productionTaskService.insertProductionTask(testTaskDto, "admin");
        assertTrue(createResult, "创建生产任务应该成功");
        
        // 2. 查询创建的任务
        ProductionTaskVo createdTask = productionTaskService.selectProductionTaskById(testTaskId);
        assertNotNull(createdTask, "应该能查询到创建的任务");
        assertEquals("集成测试生产任务", createdTask.getTaskName());
        assertEquals(1, createdTask.getStatus()); // 待执行
        assertEquals(BigDecimal.ZERO, createdTask.getProgressRate());
        
        // 3. 为任务创建节点
        List<ProductionTaskNodeDto> nodeList = createTestNodes(testTaskId);
        boolean batchCreateResult = taskNodeService.batchCreateNodes(testTaskId, nodeList);
        assertTrue(batchCreateResult, "批量创建节点应该成功");
        
        // 4. 查询任务节点
        List<ProductionTaskNodeVo> nodes = taskNodeService.selectNodesByTaskId(testTaskId);
        assertEquals(3, nodes.size(), "应该有3个节点");
        
        // 5. 分配任务
        boolean assignResult = productionTaskService.assignTask(testTaskId, 2L, "admin");
        assertTrue(assignResult, "分配任务应该成功");
        
        // 6. 开始执行任务
        boolean startResult = productionTaskService.startTask(testTaskId, "admin");
        assertTrue(startResult, "开始执行任务应该成功");
        
        // 7. 执行第一个节点
        ProductionTaskNodeVo firstNode = nodes.get(0);
        boolean startNodeResult = taskNodeService.startNodeExecution(firstNode.getNodeId());
        assertTrue(startNodeResult, "开始执行第一个节点应该成功");
        
        boolean completeNodeResult = taskNodeService.completeNodeExecution(firstNode.getNodeId());
        assertTrue(completeNodeResult, "完成第一个节点应该成功");
        
        // 8. 更新任务进度
        boolean updateProgressResult = productionTaskService.updateTaskProgress(testTaskId, new BigDecimal("33.33"));
        assertTrue(updateProgressResult, "更新任务进度应该成功");
        
        // 9. 执行第二个节点
        ProductionTaskNodeVo secondNode = nodes.get(1);
        taskNodeService.startNodeExecution(secondNode.getNodeId());
        taskNodeService.completeNodeExecution(secondNode.getNodeId());
        
        // 10. 跳过第三个节点
        ProductionTaskNodeVo thirdNode = nodes.get(2);
        boolean skipResult = taskNodeService.skipNode(thirdNode.getNodeId(), "测试跳过");
        assertTrue(skipResult, "跳过节点应该成功");
        
        // 11. 完成任务
        boolean completeTaskResult = productionTaskService.completeTask(testTaskId, "admin");
        assertTrue(completeTaskResult, "完成任务应该成功");
        
        // 12. 验证最终状态
        ProductionTaskVo finalTask = productionTaskService.selectProductionTaskById(testTaskId);
        assertEquals(6, finalTask.getStatus()); // 已完工
        assertEquals(new BigDecimal("100"), finalTask.getProgressRate());
        assertNotNull(finalTask.getActualEndTime());
        
        // 13. 查询节点统计
        Map<String, Object> nodeStats = taskNodeService.selectNodeStatisticsByTaskId(testTaskId);
        assertEquals(3, nodeStats.get("total_nodes"));
        assertEquals(2, nodeStats.get("completed_nodes"));
        assertEquals(1, nodeStats.get("skipped_nodes"));
    }
    
    @Test
    void testTaskCopyWorkflow() {
        // 1. 创建原始任务
        productionTaskService.insertProductionTask(testTaskDto, "admin");
        
        // 2. 为原始任务创建节点
        List<ProductionTaskNodeDto> nodeList = createTestNodes(testTaskId);
        taskNodeService.batchCreateNodes(testTaskId, nodeList);
        
        // 3. 复制任务
        boolean copyResult = productionTaskService.copyTask(testTaskId, "复制的任务", "admin");
        assertTrue(copyResult, "复制任务应该成功");
        
        // 4. 查询任务列表验证复制结果
        ProductionTaskQueryDto queryDto = new ProductionTaskQueryDto();
        queryDto.setTaskName("复制的任务");
        queryDto.setPageNum(1);
        queryDto.setPageSize(10);
        
        IPage<ProductionTaskVo> taskPage = productionTaskService.selectProductionTaskList(queryDto);
        assertEquals(1, taskPage.getRecords().size());
        
        ProductionTaskVo copiedTask = taskPage.getRecords().get(0);
        assertEquals("复制的任务", copiedTask.getTaskName());
        assertEquals(1, copiedTask.getStatus()); // 待执行
        assertEquals(BigDecimal.ZERO, copiedTask.getProgressRate());
        
        // 5. 验证复制的任务也有节点
        List<ProductionTaskNodeVo> copiedNodes = taskNodeService.selectNodesByTaskId(copiedTask.getTaskId());
        assertEquals(3, copiedNodes.size(), "复制的任务应该有相同数量的节点");
    }
    
    @Test
    void testTaskErrorHandlingWorkflow() {
        // 1. 创建任务
        productionTaskService.insertProductionTask(testTaskDto, "admin");
        
        // 2. 创建节点
        List<ProductionTaskNodeDto> nodeList = createTestNodes(testTaskId);
        taskNodeService.batchCreateNodes(testTaskId, nodeList);
        
        // 3. 开始执行任务
        productionTaskService.startTask(testTaskId, "admin");
        
        // 4. 获取第一个节点并开始执行
        List<ProductionTaskNodeVo> nodes = taskNodeService.selectNodesByTaskId(testTaskId);
        ProductionTaskNodeVo firstNode = nodes.get(0);
        taskNodeService.startNodeExecution(firstNode.getNodeId());
        
        // 5. 标记节点异常
        boolean errorResult = taskNodeService.markNodeError(firstNode.getNodeId(), "测试异常情况");
        assertTrue(errorResult, "标记节点异常应该成功");
        
        // 6. 重置节点状态
        boolean resetResult = taskNodeService.resetNodeStatus(firstNode.getNodeId());
        assertTrue(resetResult, "重置节点状态应该成功");
        
        // 7. 重新执行节点
        taskNodeService.startNodeExecution(firstNode.getNodeId());
        taskNodeService.completeNodeExecution(firstNode.getNodeId());
        
        // 8. 验证节点状态
        ProductionTaskNodeVo updatedNode = taskNodeService.selectNodeById(firstNode.getNodeId());
        assertEquals(3, updatedNode.getStatus()); // 已完成
    }
    
    @Test
    void testTaskStatisticsWorkflow() {
        // 1. 创建多个不同状态的任务
        for (int i = 0; i < 5; i++) {
            ProductionTaskDto taskDto = new ProductionTaskDto();
            taskDto.setTaskId(IdUtils.fastSimpleUUID());
            taskDto.setTaskName("统计测试任务" + i);
            taskDto.setTaskType(1);
            taskDto.setProductName("测试产品" + i);
            taskDto.setPlanStartDate(new Date());
            taskDto.setPlanEndDate(new Date(System.currentTimeMillis() + 86400000));
            taskDto.setPriorityLevel(i % 3 + 1);
            taskDto.setResponsibleUserId(1L);
            taskDto.setEstimatedHours(new BigDecimal("8.0"));
            
            productionTaskService.insertProductionTask(taskDto, "admin");
            
            // 设置不同的任务状态
            if (i % 2 == 0) {
                productionTaskService.startTask(taskDto.getTaskId(), "admin");
            }
            if (i == 4) {
                productionTaskService.completeTask(taskDto.getTaskId(), "admin");
            }
        }
        
        // 2. 查询状态统计
        List<Map<String, Object>> statusStats = productionTaskService.selectTaskStatusStatistics();
        assertNotNull(statusStats);
        assertFalse(statusStats.isEmpty());
        
        // 3. 查询进度统计
        List<Map<String, Object>> progressStats = productionTaskService.selectTaskProgressStatistics();
        assertNotNull(progressStats);
        assertFalse(progressStats.isEmpty());
        
        // 4. 查询即将到期的任务
        List<ProductionTaskVo> upcomingTasks = productionTaskService.selectUpcomingTasks(30);
        assertNotNull(upcomingTasks);
        assertTrue(upcomingTasks.size() >= 5);
    }
    
    @Test
    void testDataConsistencyWorkflow() {
        // 1. 创建任务
        productionTaskService.insertProductionTask(testTaskDto, "admin");
        
        // 2. 创建节点
        List<ProductionTaskNodeDto> nodeList = createTestNodes(testTaskId);
        taskNodeService.batchCreateNodes(testTaskId, nodeList);
        
        // 3. 查询资源使用情况
        Map<String, Object> resourceUsage = productionTaskService.selectTaskResourceUsage(testTaskId);
        assertNotNull(resourceUsage);
        assertEquals(3, resourceUsage.get("node_count"));
        assertEquals(0, resourceUsage.get("completed_node_count"));
        
        // 4. 执行节点并验证统计数据一致性
        List<ProductionTaskNodeVo> nodes = taskNodeService.selectNodesByTaskId(testTaskId);
        taskNodeService.startNodeExecution(nodes.get(0).getNodeId());
        taskNodeService.completeNodeExecution(nodes.get(0).getNodeId());
        
        // 5. 重新查询资源使用情况
        Map<String, Object> updatedResourceUsage = productionTaskService.selectTaskResourceUsage(testTaskId);
        assertEquals(1, updatedResourceUsage.get("completed_node_count"));
        
        // 6. 查询节点统计信息
        Map<String, Object> nodeStats = taskNodeService.selectNodeStatisticsByTaskId(testTaskId);
        assertEquals(3, nodeStats.get("total_nodes"));
        assertEquals(1, nodeStats.get("completed_nodes"));
        assertEquals(2, nodeStats.get("pending_nodes"));
    }
    
    /**
     * 创建测试节点
     */
    private List<ProductionTaskNodeDto> createTestNodes(String taskId) {
        ProductionTaskNodeDto node1 = new ProductionTaskNodeDto();
        node1.setTaskId(taskId);
        node1.setNodeName("准备阶段");
        node1.setNodeType(1); // 开始
        node1.setSequenceNo(1);
        node1.setIsRequired(1);
        node1.setEstimatedDuration(60);
        
        ProductionTaskNodeDto node2 = new ProductionTaskNodeDto();
        node2.setTaskId(taskId);
        node2.setNodeName("生产阶段");
        node2.setNodeType(2); // 处理
        node2.setSequenceNo(2);
        node2.setIsRequired(1);
        node2.setEstimatedDuration(480);
        
        ProductionTaskNodeDto node3 = new ProductionTaskNodeDto();
        node3.setTaskId(taskId);
        node3.setNodeName("检验阶段");
        node3.setNodeType(3); // 检验
        node3.setSequenceNo(3);
        node3.setIsRequired(0);
        node3.setEstimatedDuration(120);
        
        return List.of(node1, node2, node3);
    }
}
