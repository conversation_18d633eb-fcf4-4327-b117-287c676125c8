package com.jingfang.production.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jingfang.production.module.entity.ProductionTaskNode;
import com.jingfang.production.vo.ProductionTaskNodeVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 任务节点Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-16
 */
@Mapper
public interface ProductionTaskNodeMapper extends BaseMapper<ProductionTaskNode> {

    /**
     * 根据任务ID查询节点列表
     *
     * @param taskId 任务ID
     * @return 节点列表
     */
    List<ProductionTaskNodeVo> selectNodesByTaskId(@Param("taskId") String taskId);

    /**
     * 根据节点ID查询节点详情
     *
     * @param nodeId 节点ID
     * @return 节点详情
     */
    ProductionTaskNodeVo selectNodeById(@Param("nodeId") String nodeId);

    /**
     * 查询任务的节点统计信息
     *
     * @param taskId 任务ID
     * @return 节点统计信息
     */
    Map<String, Object> selectNodeStatisticsByTaskId(@Param("taskId") String taskId);

    /**
     * 开始执行节点
     *
     * @param nodeId 节点ID
     * @param startTime 开始时间
     * @return 更新结果
     */
    int startNodeExecution(@Param("nodeId") String nodeId, @Param("startTime") Date startTime);

    /**
     * 完成节点执行
     *
     * @param nodeId 节点ID
     * @param endTime 结束时间
     * @param actualDuration 实际耗时（分钟）
     * @return 更新结果
     */
    int completeNodeExecution(@Param("nodeId") String nodeId, @Param("endTime") Date endTime, @Param("actualDuration") Integer actualDuration);

    /**
     * 更新节点状态
     *
     * @param nodeId 节点ID
     * @param status 新状态
     * @param updateTime 更新时间
     * @return 更新结果
     */
    int updateNodeStatus(@Param("nodeId") String nodeId, @Param("status") Integer status, @Param("updateTime") Date updateTime);
}