<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.item_consumption_report.mapper.ItemConsumptionReportMapper">

    <!-- 库存消耗报表结果映射 -->
    <resultMap id="ItemConsumptionReportVoResult" type="com.jingfang.item_consumption_report.module.vo.ItemConsumptionReportVo">
        <result property="itemId" column="item_id" jdbcType="VARCHAR"/>
        <result property="itemName" column="item_name" jdbcType="VARCHAR"/>
        <result property="itemCode" column="item_code" jdbcType="VARCHAR"/>
        <result property="itemType" column="item_type" jdbcType="INTEGER"/>
        <result property="itemTypeName" column="item_type_name" jdbcType="VARCHAR"/>
        <result property="specModel" column="spec_model" jdbcType="VARCHAR"/>
        <result property="unit" column="unit" jdbcType="VARCHAR"/>
        <result property="warehouseId" column="warehouse_id" jdbcType="INTEGER"/>
        <result property="warehouseName" column="warehouse_name" jdbcType="VARCHAR"/>
        <result property="periodStartDate" column="period_start_date" jdbcType="DATE"/>
        <result property="periodEndDate" column="period_end_date" jdbcType="DATE"/>
        <result property="totalConsumption" column="total_consumption" jdbcType="DECIMAL"/>
        <result property="totalAmount" column="total_amount" jdbcType="DECIMAL"/>
        <result property="avgUnitPrice" column="avg_unit_price" jdbcType="DECIMAL"/>
        <result property="consumptionCount" column="consumption_count" jdbcType="INTEGER"/>
        <result property="productionConsumption" column="production_consumption" jdbcType="DECIMAL"/>
        <result property="maintenanceConsumption" column="maintenance_consumption" jdbcType="DECIMAL"/>
        <result property="requisitionConsumption" column="requisition_consumption" jdbcType="DECIMAL"/>
        <result property="otherConsumption" column="other_consumption" jdbcType="DECIMAL"/>
        <result property="relatedProductionOutput" column="related_production_output" jdbcType="DECIMAL"/>
        <result property="consumptionPerUnit" column="consumption_per_unit" jdbcType="DECIMAL"/>
        <result property="efficiencyScore" column="efficiency_score" jdbcType="DECIMAL"/>
        <result property="efficiencyLevel" column="efficiency_level" jdbcType="VARCHAR"/>
        <result property="efficiencyLevelName" column="efficiency_level_name" jdbcType="VARCHAR"/>
        <result property="benchmarkConsumption" column="benchmark_consumption" jdbcType="DECIMAL"/>
        <result property="efficiencyVariance" column="efficiency_variance" jdbcType="DECIMAL"/>
        <result property="efficiencyVariancePercent" column="efficiency_variance_percent" jdbcType="DECIMAL"/>
        <result property="costPerUnit" column="cost_per_unit" jdbcType="DECIMAL"/>
        <result property="savingsAmount" column="savings_amount" jdbcType="DECIMAL"/>
        <result property="optimizationSuggestions" column="optimization_suggestions" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 分页查询库存消耗报表数据 -->
    <select id="selectConsumptionReportList" resultMap="ItemConsumptionReportVoResult">
        SELECT 
            i.item_id,
            i.item_name,
            i.item_code,
            i.item_type,
            CASE i.item_type 
                WHEN 1 THEN '消耗品' 
                WHEN 2 THEN '备品备件' 
                ELSE '未知' 
            END as item_type_name,
            i.spec_model,
            i.unit,
            cs.warehouse_id,
            CASE cs.warehouse_id 
                WHEN 1 THEN '主仓库'
                WHEN 2 THEN '备件仓库'
                WHEN 3 THEN '临时仓库'
                ELSE CONCAT('仓库', cs.warehouse_id)
            END as warehouse_name,
            #{request.startDate} as period_start_date,
            #{request.endDate} as period_end_date,
            COALESCE(SUM(cs.total_quantity), 0) as total_consumption,
            COALESCE(SUM(cs.total_amount), 0) as total_amount,
            CASE WHEN SUM(cs.total_quantity) > 0 
                 THEN SUM(cs.total_amount) / SUM(cs.total_quantity) 
                 ELSE 0 
            END as avg_unit_price,
            COALESCE(SUM(cs.consumption_count), 0) as consumption_count,
            COALESCE(SUM(CASE WHEN cs.consumption_type = 1 THEN cs.total_quantity ELSE 0 END), 0) as production_consumption,
            COALESCE(SUM(CASE WHEN cs.consumption_type = 2 THEN cs.total_quantity ELSE 0 END), 0) as maintenance_consumption,
            COALESCE(SUM(CASE WHEN cs.consumption_type = 3 THEN cs.total_quantity ELSE 0 END), 0) as requisition_consumption,
            COALESCE(SUM(CASE WHEN cs.consumption_type = 4 THEN cs.total_quantity ELSE 0 END), 0) as other_consumption,
            COALESCE(AVG(cs.related_production_output), 0) as related_production_output,
            COALESCE(AVG(cs.consumption_efficiency), 0) as consumption_per_unit,
            COALESCE(e.efficiency_score, 0) as efficiency_score,
            COALESCE(e.efficiency_level, 'unknown') as efficiency_level,
            CASE COALESCE(e.efficiency_level, 'unknown')
                WHEN 'excellent' THEN '优秀'
                WHEN 'good' THEN '良好'
                WHEN 'average' THEN '一般'
                WHEN 'poor' THEN '较差'
                ELSE '未知'
            END as efficiency_level_name,
            COALESCE(b.consumption_per_unit, 0) as benchmark_consumption,
            COALESCE(e.efficiency_variance, 0) as efficiency_variance,
            CASE WHEN b.consumption_per_unit > 0 
                 THEN (e.efficiency_variance / b.consumption_per_unit) * 100 
                 ELSE 0 
            END as efficiency_variance_percent,
            COALESCE(e.cost_per_unit, 0) as cost_per_unit,
            COALESCE(e.savings_amount, 0) as savings_amount,
            e.optimization_suggestions
        FROM item_base_info i
        LEFT JOIN item_consumption_summary cs ON i.item_id = cs.item_id
        LEFT JOIN item_consumption_efficiency e ON i.item_id = e.item_id 
            AND e.analysis_period = #{request.analysisPeriod}
            AND e.period_start_date = #{request.startDate}
        LEFT JOIN item_consumption_benchmark b ON i.item_id = b.item_id 
            AND b.is_active = 1
            AND b.valid_start_date &lt;= #{request.startDate}
            AND (b.valid_end_date IS NULL OR b.valid_end_date &gt;= #{request.endDate})
        <where>
            i.deleted = 0
            <if test="request.itemIds != null and request.itemIds.size() > 0">
                AND i.item_id IN
                <foreach collection="request.itemIds" item="itemId" open="(" separator="," close=")">
                    #{itemId}
                </foreach>
            </if>
            <if test="request.itemName != null and request.itemName != ''">
                AND i.item_name LIKE CONCAT('%', #{request.itemName}, '%')
            </if>
            <if test="request.itemCode != null and request.itemCode != ''">
                AND i.item_code LIKE CONCAT('%', #{request.itemCode}, '%')
            </if>
            <if test="request.itemType != null">
                AND i.item_type = #{request.itemType}
            </if>
            <if test="request.warehouseIds != null and request.warehouseIds.size() > 0">
                AND cs.warehouse_id IN
                <foreach collection="request.warehouseIds" item="warehouseId" open="(" separator="," close=")">
                    #{warehouseId}
                </foreach>
            </if>
            <if test="request.consumptionTypes != null and request.consumptionTypes.size() > 0">
                AND cs.consumption_type IN
                <foreach collection="request.consumptionTypes" item="consumptionType" open="(" separator="," close=")">
                    #{consumptionType}
                </foreach>
            </if>
            <if test="request.startDate != null and request.endDate != null">
                AND cs.consumption_date BETWEEN #{request.startDate} AND #{request.endDate}
            </if>
            <if test="request.efficiencyLevels != null and request.efficiencyLevels.size() > 0">
                AND e.efficiency_level IN
                <foreach collection="request.efficiencyLevels" item="level" open="(" separator="," close=")">
                    #{level}
                </foreach>
            </if>
            <if test="request.minEfficiencyScore != null">
                AND e.efficiency_score &gt;= #{request.minEfficiencyScore}
            </if>
            <if test="request.maxEfficiencyScore != null">
                AND e.efficiency_score &lt;= #{request.maxEfficiencyScore}
            </if>
        </where>
        GROUP BY i.item_id, cs.warehouse_id, e.efficiency_id, b.benchmark_id
        HAVING 1=1
        <if test="request.minConsumption != null">
            AND total_consumption &gt;= #{request.minConsumption}
        </if>
        <if test="request.maxConsumption != null">
            AND total_consumption &lt;= #{request.maxConsumption}
        </if>
        <choose>
            <when test="request.orderBy != null and request.orderBy != ''">
                ORDER BY ${request.orderBy} ${request.orderDirection}
            </when>
            <otherwise>
                ORDER BY total_consumption DESC
            </otherwise>
        </choose>
    </select>

    <!-- 查询总体统计数据 -->
    <select id="selectOverallStatistics" resultType="com.jingfang.item_consumption_report.module.vo.ItemConsumptionStatisticsVo$OverallStatistics">
        SELECT
            COALESCE(SUM(cs.total_quantity), 0) as totalConsumption,
            COALESCE(SUM(cs.total_amount), 0) as totalAmount,
            CASE WHEN SUM(cs.total_quantity) > 0
                 THEN SUM(cs.total_amount) / SUM(cs.total_quantity)
                 ELSE 0
            END as avgUnitPrice,
            COUNT(DISTINCT cs.item_id) as itemCount,
            COALESCE(SUM(cs.consumption_count), 0) as consumptionCount,
            COALESCE(AVG(e.efficiency_score), 0) as avgEfficiencyScore,
            COALESCE(SUM(e.savings_amount), 0) as totalSavings,
            CASE WHEN COUNT(e.efficiency_id) > 0
                 THEN (COUNT(CASE WHEN e.efficiency_score >= 80 THEN 1 END) * 100.0 / COUNT(e.efficiency_id))
                 ELSE 0
            END as efficiencyImprovement
        FROM item_consumption_summary cs
        LEFT JOIN item_consumption_efficiency e ON cs.item_id = e.item_id
        <where>
            <if test="request.startDate != null and request.endDate != null">
                cs.consumption_date BETWEEN #{request.startDate} AND #{request.endDate}
            </if>
            <if test="request.itemIds != null and request.itemIds.size() > 0">
                AND cs.item_id IN
                <foreach collection="request.itemIds" item="itemId" open="(" separator="," close=")">
                    #{itemId}
                </foreach>
            </if>
            <if test="request.warehouseIds != null and request.warehouseIds.size() > 0">
                AND cs.warehouse_id IN
                <foreach collection="request.warehouseIds" item="warehouseId" open="(" separator="," close=")">
                    #{warehouseId}
                </foreach>
            </if>
            <if test="request.consumptionTypes != null and request.consumptionTypes.size() > 0">
                AND cs.consumption_type IN
                <foreach collection="request.consumptionTypes" item="consumptionType" open="(" separator="," close=")">
                    #{consumptionType}
                </foreach>
            </if>
        </where>
    </select>

    <!-- 查询消耗类型统计数据 -->
    <select id="selectConsumptionTypeStatistics" resultType="com.jingfang.item_consumption_report.module.vo.ItemConsumptionStatisticsVo$ConsumptionTypeStatistics">
        SELECT
            cs.consumption_type as consumptionType,
            CASE cs.consumption_type
                WHEN 1 THEN '生产消耗'
                WHEN 2 THEN '维护消耗'
                WHEN 3 THEN '领用消耗'
                WHEN 4 THEN '其他消耗'
                ELSE '未知'
            END as consumptionTypeName,
            COALESCE(SUM(cs.total_quantity), 0) as consumption,
            COALESCE(SUM(cs.total_amount), 0) as amount,
            CASE WHEN total_stats.total_amount > 0
                 THEN (SUM(cs.total_amount) * 100.0 / total_stats.total_amount)
                 ELSE 0
            END as percentage,
            COUNT(DISTINCT cs.item_id) as itemCount,
            COALESCE(AVG(e.efficiency_score), 0) as avgEfficiencyScore
        FROM item_consumption_summary cs
        LEFT JOIN item_consumption_efficiency e ON cs.item_id = e.item_id
        CROSS JOIN (
            SELECT COALESCE(SUM(total_amount), 1) as total_amount
            FROM item_consumption_summary
            <where>
                <if test="request.startDate != null and request.endDate != null">
                    consumption_date BETWEEN #{request.startDate} AND #{request.endDate}
                </if>
            </where>
        ) total_stats
        <where>
            <if test="request.startDate != null and request.endDate != null">
                cs.consumption_date BETWEEN #{request.startDate} AND #{request.endDate}
            </if>
            <if test="request.itemIds != null and request.itemIds.size() > 0">
                AND cs.item_id IN
                <foreach collection="request.itemIds" item="itemId" open="(" separator="," close=")">
                    #{itemId}
                </foreach>
            </if>
            <if test="request.warehouseIds != null and request.warehouseIds.size() > 0">
                AND cs.warehouse_id IN
                <foreach collection="request.warehouseIds" item="warehouseId" open="(" separator="," close=")">
                    #{warehouseId}
                </foreach>
            </if>
        </where>
        GROUP BY cs.consumption_type, total_stats.total_amount
        ORDER BY consumption DESC
    </select>

    <!-- 查询效率等级统计数据 -->
    <select id="selectEfficiencyLevelStatistics" resultType="com.jingfang.item_consumption_report.module.vo.ItemConsumptionStatisticsVo$EfficiencyLevelStatistics">
        SELECT
            e.efficiency_level as efficiencyLevel,
            CASE e.efficiency_level
                WHEN 'excellent' THEN '优秀'
                WHEN 'good' THEN '良好'
                WHEN 'average' THEN '一般'
                WHEN 'poor' THEN '较差'
                ELSE '未知'
            END as efficiencyLevelName,
            COUNT(DISTINCT e.item_id) as itemCount,
            CASE WHEN total_stats.total_count > 0
                 THEN (COUNT(DISTINCT e.item_id) * 100.0 / total_stats.total_count)
                 ELSE 0
            END as percentage,
            COALESCE(AVG(e.efficiency_score), 0) as avgEfficiencyScore,
            COALESCE(SUM(cs.total_quantity), 0) as totalConsumption,
            COALESCE(SUM(e.savings_amount), 0) as totalSavings
        FROM item_consumption_efficiency e
        LEFT JOIN item_consumption_summary cs ON e.item_id = cs.item_id
        CROSS JOIN (
            SELECT COUNT(DISTINCT item_id) as total_count
            FROM item_consumption_efficiency
            <where>
                <if test="request.startDate != null and request.endDate != null">
                    period_start_date &gt;= #{request.startDate} AND period_end_date &lt;= #{request.endDate}
                </if>
            </where>
        ) total_stats
        <where>
            <if test="request.startDate != null and request.endDate != null">
                e.period_start_date &gt;= #{request.startDate} AND e.period_end_date &lt;= #{request.endDate}
            </if>
            <if test="request.analysisPeriod != null and request.analysisPeriod != ''">
                AND e.analysis_period = #{request.analysisPeriod}
            </if>
        </where>
        GROUP BY e.efficiency_level, total_stats.total_count
        ORDER BY avgEfficiencyScore DESC
    </select>

    <!-- 查询仓库消耗统计数据 -->
    <select id="selectWarehouseStatistics" resultType="com.jingfang.item_consumption_report.module.vo.ItemConsumptionStatisticsVo$WarehouseStatistics">
        SELECT
            cs.warehouse_id as warehouseId,
            CASE cs.warehouse_id
                WHEN 1 THEN '主仓库'
                WHEN 2 THEN '备件仓库'
                WHEN 3 THEN '临时仓库'
                ELSE CONCAT('仓库', cs.warehouse_id)
            END as warehouseName,
            COALESCE(SUM(cs.total_quantity), 0) as consumption,
            COALESCE(SUM(cs.total_amount), 0) as amount,
            CASE WHEN total_stats.total_amount > 0
                 THEN (SUM(cs.total_amount) * 100.0 / total_stats.total_amount)
                 ELSE 0
            END as percentage,
            COUNT(DISTINCT cs.item_id) as itemCount,
            COALESCE(AVG(e.efficiency_score), 0) as avgEfficiencyScore
        FROM item_consumption_summary cs
        LEFT JOIN item_consumption_efficiency e ON cs.item_id = e.item_id
        CROSS JOIN (
            SELECT COALESCE(SUM(total_amount), 1) as total_amount
            FROM item_consumption_summary
            <where>
                <if test="request.startDate != null and request.endDate != null">
                    consumption_date BETWEEN #{request.startDate} AND #{request.endDate}
                </if>
            </where>
        ) total_stats
        <where>
            <if test="request.startDate != null and request.endDate != null">
                cs.consumption_date BETWEEN #{request.startDate} AND #{request.endDate}
            </if>
            <if test="request.warehouseIds != null and request.warehouseIds.size() > 0">
                AND cs.warehouse_id IN
                <foreach collection="request.warehouseIds" item="warehouseId" open="(" separator="," close=")">
                    #{warehouseId}
                </foreach>
            </if>
        </where>
        GROUP BY cs.warehouse_id, total_stats.total_amount
        ORDER BY consumption DESC
    </select>

    <!-- 查询部门消耗统计数据 -->
    <select id="selectDepartmentStatistics" resultType="com.jingfang.item_consumption_report.module.vo.ItemConsumptionStatisticsVo$DepartmentStatistics">
        SELECT
            vcd.recipient_dept as deptName,
            COALESCE(SUM(vcd.consumption_quantity), 0) as consumption,
            COALESCE(SUM(vcd.consumption_amount), 0) as amount,
            CASE WHEN total_stats.total_amount > 0
                 THEN (SUM(vcd.consumption_amount) * 100.0 / total_stats.total_amount)
                 ELSE 0
            END as percentage,
            COUNT(DISTINCT vcd.item_id) as itemCount,
            COUNT(*) as consumptionCount
        FROM v_item_consumption_detail vcd
        CROSS JOIN (
            SELECT COALESCE(SUM(consumption_amount), 1) as total_amount
            FROM v_item_consumption_detail
            <where>
                <if test="request.startDate != null and request.endDate != null">
                    consumption_date BETWEEN #{request.startDate} AND #{request.endDate}
                </if>
            </where>
        ) total_stats
        <where>
            vcd.recipient_dept IS NOT NULL AND vcd.recipient_dept != ''
            <if test="request.startDate != null and request.endDate != null">
                AND vcd.consumption_date BETWEEN #{request.startDate} AND #{request.endDate}
            </if>
            <if test="request.deptName != null and request.deptName != ''">
                AND vcd.recipient_dept LIKE CONCAT('%', #{request.deptName}, '%')
            </if>
        </where>
        GROUP BY vcd.recipient_dept, total_stats.total_amount
        ORDER BY consumption DESC
    </select>

    <!-- 查询消耗趋势数据 -->
    <select id="selectConsumptionTrend" resultType="com.jingfang.item_consumption_report.module.vo.ItemConsumptionReportVo$ConsumptionTrendVo">
        <choose>
            <when test="timeDimension == 1">
                SELECT
                    cs.consumption_date as date,
                    DATE_FORMAT(cs.consumption_date, '%Y-%m-%d') as dateLabel,
                    COALESCE(SUM(cs.total_quantity), 0) as consumption,
                    COALESCE(SUM(cs.total_amount), 0) as amount,
                    COALESCE(AVG(cs.consumption_efficiency), 0) as consumptionPerUnit,
                    COALESCE(AVG(e.efficiency_score), 0) as efficiencyScore
                FROM item_consumption_summary cs
                LEFT JOIN item_consumption_efficiency e ON cs.item_id = e.item_id
                    AND cs.consumption_date BETWEEN e.period_start_date AND e.period_end_date
                WHERE cs.item_id = #{itemId}
                <if test="startDate != null and endDate != null">
                    AND cs.consumption_date BETWEEN #{startDate} AND #{endDate}
                </if>
                GROUP BY cs.consumption_date
                ORDER BY cs.consumption_date
            </when>
            <when test="timeDimension == 2">
                SELECT
                    MIN(cs.consumption_date) as date,
                    CONCAT(YEAR(MIN(cs.consumption_date)), '-W', WEEK(MIN(cs.consumption_date))) as dateLabel,
                    COALESCE(SUM(cs.total_quantity), 0) as consumption,
                    COALESCE(SUM(cs.total_amount), 0) as amount,
                    COALESCE(AVG(cs.consumption_efficiency), 0) as consumptionPerUnit,
                    COALESCE(AVG(e.efficiency_score), 0) as efficiencyScore
                FROM item_consumption_summary cs
                LEFT JOIN item_consumption_efficiency e ON cs.item_id = e.item_id
                    AND cs.consumption_date BETWEEN e.period_start_date AND e.period_end_date
                WHERE cs.item_id = #{itemId}
                <if test="startDate != null and endDate != null">
                    AND cs.consumption_date BETWEEN #{startDate} AND #{endDate}
                </if>
                GROUP BY YEAR(cs.consumption_date), WEEK(cs.consumption_date)
                ORDER BY YEAR(cs.consumption_date), WEEK(cs.consumption_date)
            </when>
            <when test="timeDimension == 3">
                SELECT
                    MIN(cs.consumption_date) as date,
                    DATE_FORMAT(MIN(cs.consumption_date), '%Y-%m') as dateLabel,
                    COALESCE(SUM(cs.total_quantity), 0) as consumption,
                    COALESCE(SUM(cs.total_amount), 0) as amount,
                    COALESCE(AVG(cs.consumption_efficiency), 0) as consumptionPerUnit,
                    COALESCE(AVG(e.efficiency_score), 0) as efficiencyScore
                FROM item_consumption_summary cs
                LEFT JOIN item_consumption_efficiency e ON cs.item_id = e.item_id
                    AND cs.consumption_date BETWEEN e.period_start_date AND e.period_end_date
                WHERE cs.item_id = #{itemId}
                <if test="startDate != null and endDate != null">
                    AND cs.consumption_date BETWEEN #{startDate} AND #{endDate}
                </if>
                GROUP BY YEAR(cs.consumption_date), MONTH(cs.consumption_date)
                ORDER BY YEAR(cs.consumption_date), MONTH(cs.consumption_date)
            </when>
            <when test="timeDimension == 4">
                SELECT
                    MIN(cs.consumption_date) as date,
                    CONCAT(YEAR(MIN(cs.consumption_date)), '-Q', QUARTER(MIN(cs.consumption_date))) as dateLabel,
                    COALESCE(SUM(cs.total_quantity), 0) as consumption,
                    COALESCE(SUM(cs.total_amount), 0) as amount,
                    COALESCE(AVG(cs.consumption_efficiency), 0) as consumptionPerUnit,
                    COALESCE(AVG(e.efficiency_score), 0) as efficiencyScore
                FROM item_consumption_summary cs
                LEFT JOIN item_consumption_efficiency e ON cs.item_id = e.item_id
                    AND cs.consumption_date BETWEEN e.period_start_date AND e.period_end_date
                WHERE cs.item_id = #{itemId}
                <if test="startDate != null and endDate != null">
                    AND cs.consumption_date BETWEEN #{startDate} AND #{endDate}
                </if>
                GROUP BY YEAR(cs.consumption_date), QUARTER(cs.consumption_date)
                ORDER BY YEAR(cs.consumption_date), QUARTER(cs.consumption_date)
            </when>
            <otherwise>
                SELECT
                    MIN(cs.consumption_date) as date,
                    DATE_FORMAT(MIN(cs.consumption_date), '%Y') as dateLabel,
                    COALESCE(SUM(cs.total_quantity), 0) as consumption,
                    COALESCE(SUM(cs.total_amount), 0) as amount,
                    COALESCE(AVG(cs.consumption_efficiency), 0) as consumptionPerUnit,
                    COALESCE(AVG(e.efficiency_score), 0) as efficiencyScore
                FROM item_consumption_summary cs
                LEFT JOIN item_consumption_efficiency e ON cs.item_id = e.item_id
                    AND cs.consumption_date BETWEEN e.period_start_date AND e.period_end_date
                WHERE cs.item_id = #{itemId}
                <if test="startDate != null and endDate != null">
                    AND cs.consumption_date BETWEEN #{startDate} AND #{endDate}
                </if>
                GROUP BY YEAR(cs.consumption_date)
                ORDER BY YEAR(cs.consumption_date)
            </otherwise>
        </choose>
    </select>

    <!-- 查询消耗类型分布数据 -->
    <select id="selectConsumptionTypeDistribution" resultType="com.jingfang.item_consumption_report.module.vo.ItemConsumptionReportVo$ConsumptionTypeVo">
        SELECT
            cs.consumption_type as consumptionType,
            CASE cs.consumption_type
                WHEN 1 THEN '生产消耗'
                WHEN 2 THEN '维护消耗'
                WHEN 3 THEN '领用消耗'
                WHEN 4 THEN '其他消耗'
                ELSE '未知'
            END as consumptionTypeName,
            COALESCE(SUM(cs.total_quantity), 0) as consumption,
            COALESCE(SUM(cs.total_amount), 0) as amount,
            CASE WHEN total_stats.total_amount > 0
                 THEN (SUM(cs.total_amount) * 100.0 / total_stats.total_amount)
                 ELSE 0
            END as percentage
        FROM item_consumption_summary cs
        CROSS JOIN (
            SELECT COALESCE(SUM(total_amount), 1) as total_amount
            FROM item_consumption_summary
            WHERE item_id = #{itemId}
            <if test="startDate != null and endDate != null">
                AND consumption_date BETWEEN #{startDate} AND #{endDate}
            </if>
        ) total_stats
        <where>
            cs.item_id = #{itemId}
            <if test="startDate != null and endDate != null">
                AND cs.consumption_date BETWEEN #{startDate} AND #{endDate}
            </if>
        </where>
        GROUP BY cs.consumption_type, total_stats.total_amount
        ORDER BY consumption DESC
    </select>

    <!-- 查询消耗效率排行榜 -->
    <select id="selectEfficiencyRanking" resultMap="ItemConsumptionReportVoResult">
        SELECT
            i.item_id,
            i.item_name,
            i.item_code,
            i.item_type,
            CASE i.item_type
                WHEN 1 THEN '消耗品'
                WHEN 2 THEN '备品备件'
                ELSE '未知'
            END as item_type_name,
            i.spec_model,
            i.unit,
            COALESCE(e.efficiency_score, 0) as efficiency_score,
            COALESCE(e.efficiency_level, 'unknown') as efficiency_level,
            CASE COALESCE(e.efficiency_level, 'unknown')
                WHEN 'excellent' THEN '优秀'
                WHEN 'good' THEN '良好'
                WHEN 'average' THEN '一般'
                WHEN 'poor' THEN '较差'
                ELSE '未知'
            END as efficiency_level_name,
            COALESCE(e.consumption_per_unit, 0) as consumption_per_unit,
            COALESCE(e.savings_amount, 0) as savings_amount,
            COALESCE(SUM(cs.total_quantity), 0) as total_consumption,
            COALESCE(SUM(cs.total_amount), 0) as total_amount
        FROM item_base_info i
        LEFT JOIN item_consumption_efficiency e ON i.item_id = e.item_id
        LEFT JOIN item_consumption_summary cs ON i.item_id = cs.item_id
        WHERE i.deleted = 0
            AND e.efficiency_score IS NOT NULL
            <if test="request.startDate != null and request.endDate != null">
                AND e.period_start_date &gt;= #{request.startDate}
                AND e.period_end_date &lt;= #{request.endDate}
                AND cs.consumption_date BETWEEN #{request.startDate} AND #{request.endDate}
            </if>
            <if test="request.analysisPeriod != null and request.analysisPeriod != ''">
                AND e.analysis_period = #{request.analysisPeriod}
            </if>
        GROUP BY i.item_id, e.efficiency_id
        ORDER BY e.efficiency_score DESC
        LIMIT #{limit}
    </select>

    <!-- 查询消耗量排行榜 -->
    <select id="selectConsumptionRanking" resultMap="ItemConsumptionReportVoResult">
        SELECT
            i.item_id,
            i.item_name,
            i.item_code,
            i.item_type,
            CASE i.item_type
                WHEN 1 THEN '消耗品'
                WHEN 2 THEN '备品备件'
                ELSE '未知'
            END as item_type_name,
            i.spec_model,
            i.unit,
            COALESCE(SUM(cs.total_quantity), 0) as total_consumption,
            COALESCE(SUM(cs.total_amount), 0) as total_amount,
            CASE WHEN SUM(cs.total_quantity) > 0
                 THEN SUM(cs.total_amount) / SUM(cs.total_quantity)
                 ELSE 0
            END as avg_unit_price,
            COALESCE(SUM(cs.consumption_count), 0) as consumption_count
        FROM item_base_info i
        INNER JOIN item_consumption_summary cs ON i.item_id = cs.item_id
        WHERE i.deleted = 0
            <if test="request.startDate != null and request.endDate != null">
                AND cs.consumption_date BETWEEN #{request.startDate} AND #{request.endDate}
            </if>
            <if test="request.consumptionTypes != null and request.consumptionTypes.size() > 0">
                AND cs.consumption_type IN
                <foreach collection="request.consumptionTypes" item="consumptionType" open="(" separator="," close=")">
                    #{consumptionType}
                </foreach>
            </if>
        GROUP BY i.item_id
        ORDER BY total_consumption DESC
        LIMIT #{limit}
    </select>

    <!-- 生成消耗汇总数据 -->
    <insert id="generateConsumptionSummary">
        INSERT INTO item_consumption_summary (
            summary_id, item_id, warehouse_id, consumption_date, consumption_type,
            total_quantity, total_amount, avg_unit_price, consumption_count,
            related_production_output, consumption_efficiency, create_time, update_time
        )
        SELECT
            CONCAT('SUM_', DATE_FORMAT(vcd.consumption_date, '%Y%m%d'), '_', vcd.item_id, '_', vcd.warehouse_id, '_', vcd.consumption_type) as summary_id,
            vcd.item_id,
            vcd.warehouse_id,
            vcd.consumption_date,
            vcd.consumption_type,
            SUM(vcd.consumption_quantity) as total_quantity,
            SUM(vcd.consumption_amount) as total_amount,
            CASE WHEN SUM(vcd.consumption_quantity) > 0
                 THEN SUM(vcd.consumption_amount) / SUM(vcd.consumption_quantity)
                 ELSE 0
            END as avg_unit_price,
            COUNT(*) as consumption_count,
            NULL as related_production_output,
            NULL as consumption_efficiency,
            NOW() as create_time,
            NOW() as update_time
        FROM v_item_consumption_detail vcd
        <where>
            <if test="startDate != null and endDate != null">
                vcd.consumption_date BETWEEN #{startDate} AND #{endDate}
            </if>
        </where>
        GROUP BY vcd.item_id, vcd.warehouse_id, vcd.consumption_date, vcd.consumption_type
        ON DUPLICATE KEY UPDATE
            total_quantity = VALUES(total_quantity),
            total_amount = VALUES(total_amount),
            avg_unit_price = VALUES(avg_unit_price),
            consumption_count = VALUES(consumption_count),
            update_time = NOW()
    </insert>

    <!-- 更新效率分析数据 -->
    <insert id="updateEfficiencyAnalysis">
        INSERT INTO item_consumption_efficiency (
            efficiency_id, item_id, analysis_period, period_start_date, period_end_date,
            total_consumption, total_production_output, consumption_per_unit,
            efficiency_score, efficiency_level, benchmark_consumption, efficiency_variance,
            cost_per_unit, savings_amount, optimization_suggestions, create_time, update_time
        )
        SELECT
            CONCAT('EFF_', #{analysisPeriod}, '_', #{itemId}, '_', DATE_FORMAT(#{startDate}, '%Y%m%d')) as efficiency_id,
            #{itemId} as item_id,
            #{analysisPeriod} as analysis_period,
            #{startDate} as period_start_date,
            #{endDate} as period_end_date,
            COALESCE(SUM(cs.total_quantity), 0) as total_consumption,
            1000 as total_production_output, -- 假设生产产量，实际应该从生产系统获取
            CASE WHEN 1000 > 0
                 THEN SUM(cs.total_quantity) / 1000
                 ELSE 0
            END as consumption_per_unit,
            CASE
                WHEN (SUM(cs.total_quantity) / 1000) &lt;= 0.8 THEN 95
                WHEN (SUM(cs.total_quantity) / 1000) &lt;= 1.0 THEN 85
                WHEN (SUM(cs.total_quantity) / 1000) &lt;= 1.2 THEN 75
                ELSE 60
            END as efficiency_score,
            CASE
                WHEN (SUM(cs.total_quantity) / 1000) &lt;= 0.8 THEN 'excellent'
                WHEN (SUM(cs.total_quantity) / 1000) &lt;= 1.0 THEN 'good'
                WHEN (SUM(cs.total_quantity) / 1000) &lt;= 1.2 THEN 'average'
                ELSE 'poor'
            END as efficiency_level,
            COALESCE(b.consumption_per_unit, 1.0) as benchmark_consumption,
            (SUM(cs.total_quantity) / 1000) - COALESCE(b.consumption_per_unit, 1.0) as efficiency_variance,
            CASE WHEN SUM(cs.total_quantity) > 0
                 THEN SUM(cs.total_amount) / SUM(cs.total_quantity)
                 ELSE 0
            END as cost_per_unit,
            CASE WHEN (SUM(cs.total_quantity) / 1000) &lt; COALESCE(b.consumption_per_unit, 1.0)
                 THEN (COALESCE(b.consumption_per_unit, 1.0) - (SUM(cs.total_quantity) / 1000)) * 1000 *
                      (SUM(cs.total_amount) / SUM(cs.total_quantity))
                 ELSE 0
            END as savings_amount,
            '建议优化消耗品使用流程，提高使用效率' as optimization_suggestions,
            NOW() as create_time,
            NOW() as update_time
        FROM item_consumption_summary cs
        LEFT JOIN item_consumption_benchmark b ON cs.item_id = b.item_id
            AND b.is_active = 1
            AND b.valid_start_date &lt;= #{startDate}
            AND (b.valid_end_date IS NULL OR b.valid_end_date &gt;= #{endDate})
        WHERE cs.item_id = #{itemId}
            AND cs.consumption_date BETWEEN #{startDate} AND #{endDate}
        GROUP BY cs.item_id
        ON DUPLICATE KEY UPDATE
            total_consumption = VALUES(total_consumption),
            consumption_per_unit = VALUES(consumption_per_unit),
            efficiency_score = VALUES(efficiency_score),
            efficiency_level = VALUES(efficiency_level),
            efficiency_variance = VALUES(efficiency_variance),
            cost_per_unit = VALUES(cost_per_unit),
            savings_amount = VALUES(savings_amount),
            update_time = NOW()
    </insert>

    <!-- 查询图表数据 -->
    <select id="selectChartData" resultType="java.util.Map">
        <choose>
            <when test="chartType == 'trend'">
                SELECT
                    DATE_FORMAT(cs.consumption_date, '%Y-%m') as dateLabel,
                    SUM(cs.total_quantity) as consumption,
                    SUM(cs.total_amount) as amount,
                    AVG(e.efficiency_score) as efficiencyScore
                FROM item_consumption_summary cs
                LEFT JOIN item_consumption_efficiency e ON cs.item_id = e.item_id
                <where>
                    <if test="request.startDate != null and request.endDate != null">
                        cs.consumption_date BETWEEN #{request.startDate} AND #{request.endDate}
                    </if>
                </where>
                GROUP BY DATE_FORMAT(cs.consumption_date, '%Y-%m')
                ORDER BY DATE_FORMAT(cs.consumption_date, '%Y-%m')
            </when>
            <when test="chartType == 'type'">
                SELECT
                    CASE cs.consumption_type
                        WHEN 1 THEN '生产消耗'
                        WHEN 2 THEN '维护消耗'
                        WHEN 3 THEN '领用消耗'
                        WHEN 4 THEN '其他消耗'
                        ELSE '未知'
                    END as consumptionTypeName,
                    SUM(cs.total_amount) as amount
                FROM item_consumption_summary cs
                <where>
                    <if test="request.startDate != null and request.endDate != null">
                        cs.consumption_date BETWEEN #{request.startDate} AND #{request.endDate}
                    </if>
                </where>
                GROUP BY cs.consumption_type
                ORDER BY amount DESC
            </when>
            <when test="chartType == 'efficiency'">
                SELECT
                    CASE e.efficiency_level
                        WHEN 'excellent' THEN '优秀'
                        WHEN 'good' THEN '良好'
                        WHEN 'average' THEN '一般'
                        WHEN 'poor' THEN '较差'
                        ELSE '未知'
                    END as efficiencyLevelName,
                    COUNT(DISTINCT e.item_id) as itemCount
                FROM item_consumption_efficiency e
                <where>
                    <if test="request.startDate != null and request.endDate != null">
                        e.period_start_date &gt;= #{request.startDate}
                        AND e.period_end_date &lt;= #{request.endDate}
                    </if>
                </where>
                GROUP BY e.efficiency_level
                ORDER BY itemCount DESC
            </when>
            <when test="chartType == 'warehouse'">
                SELECT
                    CASE cs.warehouse_id
                        WHEN 1 THEN '主仓库'
                        WHEN 2 THEN '备件仓库'
                        WHEN 3 THEN '临时仓库'
                        ELSE CONCAT('仓库', cs.warehouse_id)
                    END as warehouseName,
                    SUM(cs.total_quantity) as consumption
                FROM item_consumption_summary cs
                <where>
                    <if test="request.startDate != null and request.endDate != null">
                        cs.consumption_date BETWEEN #{request.startDate} AND #{request.endDate}
                    </if>
                </where>
                GROUP BY cs.warehouse_id
                ORDER BY consumption DESC
            </when>
            <when test="chartType == 'department'">
                SELECT
                    vcd.recipient_dept as deptName,
                    SUM(vcd.consumption_quantity) as consumption
                FROM v_item_consumption_detail vcd
                <where>
                    vcd.recipient_dept IS NOT NULL AND vcd.recipient_dept != ''
                    <if test="request.startDate != null and request.endDate != null">
                        AND vcd.consumption_date BETWEEN #{request.startDate} AND #{request.endDate}
                    </if>
                </where>
                GROUP BY vcd.recipient_dept
                ORDER BY consumption DESC
                LIMIT 10
            </when>
            <otherwise>
                SELECT 'No Data' as message
            </otherwise>
        </choose>
    </select>

</mapper>
