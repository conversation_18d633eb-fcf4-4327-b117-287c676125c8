<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.maintenance_cost.module.mapper.MaintenanceCostRecordMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.jingfang.maintenance_cost.module.entity.MaintenanceCostRecord">
        <id property="costId" column="cost_id" jdbcType="VARCHAR"/>
        <result property="taskId" column="task_id" jdbcType="VARCHAR"/>
        <result property="faultId" column="fault_id" jdbcType="VARCHAR"/>
        <result property="assetId" column="asset_id" jdbcType="VARCHAR"/>
        <result property="costType" column="cost_type" jdbcType="TINYINT"/>
        <result property="costAmount" column="cost_amount" jdbcType="DECIMAL"/>
        <result property="costDescription" column="cost_description" jdbcType="LONGVARCHAR"/>
        <result property="laborHours" column="labor_hours" jdbcType="DECIMAL"/>
        <result property="hourlyRate" column="hourly_rate" jdbcType="DECIMAL"/>
        <result property="materialQuantity" column="material_quantity" jdbcType="DECIMAL"/>
        <result property="unitPrice" column="unit_price" jdbcType="DECIMAL"/>
        <result property="downtimeHours" column="downtime_hours" jdbcType="DECIMAL"/>
        <result property="downtimeLossRate" column="downtime_loss_rate" jdbcType="DECIMAL"/>
        <result property="vendorName" column="vendor_name" jdbcType="VARCHAR"/>
        <result property="recordTime" column="record_time" jdbcType="TIMESTAMP"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="deleted" column="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        cost_id, task_id, fault_id, asset_id, cost_type, cost_amount, cost_description,
        labor_hours, hourly_rate, material_quantity, unit_price, downtime_hours,
        downtime_loss_rate, vendor_name, record_time, create_time, create_by,
        update_time, update_by, deleted
    </sql>

    <!-- 查询成本统计汇总 -->
    <select id="selectCostSummary" resultType="com.jingfang.maintenance_report.module.vo.MaintenanceReportVo$MaintenanceCostVo">
        SELECT
            COALESCE(SUM(cost_amount), 0) as totalCost,
            COALESCE(SUM(CASE WHEN cost_type = 1 THEN cost_amount ELSE 0 END), 0) as laborCost,
            COALESCE(SUM(CASE WHEN cost_type = 2 THEN cost_amount ELSE 0 END), 0) as materialCost,
            COALESCE(SUM(CASE WHEN cost_type = 3 THEN cost_amount ELSE 0 END), 0) as outsourceCost,
            COALESCE(SUM(CASE WHEN cost_type = 4 THEN cost_amount ELSE 0 END), 0) as downtimeCost,
            COALESCE(AVG(cost_amount), 0) as avgMaintenanceCost,
            #{startDate} as startDate,
            #{endDate} as endDate
        FROM maintenance_cost_record mcr
        LEFT JOIN asset_ledger a ON mcr.asset_id = a.asset_id
        WHERE mcr.deleted = 0
        AND mcr.record_time BETWEEN #{startDate} AND #{endDate}
        <if test="deptId != null and deptId != ''">
            AND a.dept_id = #{deptId}
        </if>
        <if test="assetId != null and assetId != ''">
            AND mcr.asset_id = #{assetId}
        </if>
        <if test="costTypes != null and costTypes.size() > 0">
            AND mcr.cost_type IN
            <foreach collection="costTypes" item="costType" open="(" separator="," close=")">
                #{costType}
            </foreach>
        </if>
    </select>

    <!-- 查询成本趋势数据 -->
    <select id="selectCostTrends" resultType="com.jingfang.maintenance_report.module.vo.MaintenanceReportVo$CostTrendVo">
        SELECT
            <choose>
                <when test="timeDimension == 1">
                    DATE_FORMAT(mcr.record_time, '%Y-%m-%d') as period,
                    DATE_FORMAT(mcr.record_time, '%m月%d日') as periodName
                </when>
                <when test="timeDimension == 2">
                    DATE_FORMAT(mcr.record_time, '%Y-%u') as period,
                    CONCAT(YEAR(mcr.record_time), '年第', WEEK(mcr.record_time), '周') as periodName
                </when>
                <when test="timeDimension == 3">
                    DATE_FORMAT(mcr.record_time, '%Y-%m') as period,
                    DATE_FORMAT(mcr.record_time, '%Y年%m月') as periodName
                </when>
                <when test="timeDimension == 4">
                    CONCAT(YEAR(mcr.record_time), '-Q', QUARTER(mcr.record_time)) as period,
                    CONCAT(YEAR(mcr.record_time), '年第', QUARTER(mcr.record_time), '季度') as periodName
                </when>
                <otherwise>
                    YEAR(mcr.record_time) as period,
                    CONCAT(YEAR(mcr.record_time), '年') as periodName
                </otherwise>
            </choose>,
            SUM(mcr.cost_amount) as totalCost,
            SUM(CASE WHEN mcr.cost_type = 1 THEN mcr.cost_amount ELSE 0 END) as laborCost,
            SUM(CASE WHEN mcr.cost_type = 2 THEN mcr.cost_amount ELSE 0 END) as materialCost,
            SUM(CASE WHEN mcr.cost_type = 3 THEN mcr.cost_amount ELSE 0 END) as outsourceCost,
            SUM(CASE WHEN mcr.cost_type = 4 THEN mcr.cost_amount ELSE 0 END) as downtimeCost,
            COUNT(*) as recordCount
        FROM maintenance_cost_record mcr
        LEFT JOIN asset_ledger a ON mcr.asset_id = a.asset_id
        WHERE mcr.deleted = 0
        AND mcr.record_time BETWEEN #{startDate} AND #{endDate}
        <if test="deptId != null and deptId != ''">
            AND a.dept_id = #{deptId}
        </if>
        <if test="assetId != null and assetId != ''">
            AND mcr.asset_id = #{assetId}
        </if>
        <if test="costTypes != null and costTypes.size() > 0">
            AND mcr.cost_type IN
            <foreach collection="costTypes" item="costType" open="(" separator="," close=")">
                #{costType}
            </foreach>
        </if>
        GROUP BY period, periodName
        ORDER BY period
    </select>

    <!-- 查询部门成本统计 -->
    <select id="selectDeptCostStatistics" resultType="com.jingfang.maintenance_report.module.vo.MaintenanceReportVo$DeptCostVo">
        SELECT
            a.dept_id as deptId,
            d.dept_name as deptName,
            SUM(mcr.cost_amount) as totalCost,
            AVG(mcr.cost_amount) as avgCost,
            COUNT(*) as recordCount,
            ROUND(SUM(mcr.cost_amount) * 100.0 / (
                SELECT SUM(cost_amount) 
                FROM maintenance_cost_record mcr2 
                LEFT JOIN asset a2 ON mcr2.asset_id = a2.asset_id
                WHERE mcr2.deleted = 0 
                AND mcr2.record_time BETWEEN #{startDate} AND #{endDate}
                <if test="costTypes != null and costTypes.size() > 0">
                    AND mcr2.cost_type IN
                    <foreach collection="costTypes" item="costType" open="(" separator="," close=")">
                        #{costType}
                    </foreach>
                </if>
            ), 2) as costPercentage
        FROM maintenance_cost_record mcr
        LEFT JOIN asset_ledger a ON mcr.asset_id = a.asset_id
        LEFT JOIN sys_dept d ON a.dept_id = d.dept_id
        WHERE mcr.deleted = 0
        AND mcr.record_time BETWEEN #{startDate} AND #{endDate}
        <if test="deptIds != null and deptIds.size() > 0">
            AND a.dept_id IN
            <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
        </if>
        <if test="costTypes != null and costTypes.size() > 0">
            AND mcr.cost_type IN
            <foreach collection="costTypes" item="costType" open="(" separator="," close=")">
                #{costType}
            </foreach>
        </if>
        GROUP BY a.dept_id, d.dept_name
        ORDER BY totalCost DESC
    </select>

    <!-- 查询资产成本统计 -->
    <select id="selectAssetCostStatistics" resultType="com.jingfang.maintenance_report.module.vo.MaintenanceReportVo$AssetCostVo">
        SELECT
            mcr.asset_id as assetId,
            a.asset_name as assetName,
            a.asset_id as assetCode,
            SUM(mcr.cost_amount) as totalCost,
            AVG(mcr.cost_amount) as avgCost,
            COUNT(*) as recordCount,
            MAX(mcr.record_time) as lastMaintenanceDate
        FROM maintenance_cost_record mcr
        LEFT JOIN asset_ledger a ON mcr.asset_id = a.asset_id
        WHERE mcr.deleted = 0
        AND mcr.record_time BETWEEN #{startDate} AND #{endDate}
        <if test="deptId != null and deptId != ''">
            AND a.dept_id = #{deptId}
        </if>
        <if test="assetIds != null and assetIds.size() > 0">
            AND mcr.asset_id IN
            <foreach collection="assetIds" item="assetId" open="(" separator="," close=")">
                #{assetId}
            </foreach>
        </if>
        <if test="costTypes != null and costTypes.size() > 0">
            AND mcr.cost_type IN
            <foreach collection="costTypes" item="costType" open="(" separator="," close=")">
                #{costType}
            </foreach>
        </if>
        GROUP BY mcr.asset_id, a.asset_name, a.asset_id
        ORDER BY totalCost DESC
    </select>

    <!-- 查询成本类型分布 -->
    <select id="selectCostTypeDistribution" resultType="com.jingfang.maintenance_report.module.vo.MaintenanceReportVo$CostTypeVo">
        SELECT
            mcr.cost_type as costType,
            CASE mcr.cost_type 
                WHEN 1 THEN '人工成本'
                WHEN 2 THEN '材料成本'
                WHEN 3 THEN '外包成本'
                WHEN 4 THEN '停机成本'
                ELSE '其他成本'
            END as costTypeName,
            SUM(mcr.cost_amount) as amount,
            COUNT(*) as recordCount,
            ROUND(SUM(mcr.cost_amount) * 100.0 / (
                SELECT SUM(cost_amount) 
                FROM maintenance_cost_record mcr2 
                LEFT JOIN asset_ledger a2 ON mcr2.asset_id = a2.asset_id
                WHERE mcr2.deleted = 0 
                AND mcr2.record_time BETWEEN #{startDate} AND #{endDate}
                <if test="deptId != null and deptId != ''">
                    AND a2.dept_id = #{deptId}
                </if>
                <if test="assetId != null and assetId != ''">
                    AND mcr2.asset_id = #{assetId}
                </if>
            ), 2) as percentage
        FROM maintenance_cost_record mcr
        LEFT JOIN asset_ledger a ON mcr.asset_id = a.asset_id
        WHERE mcr.deleted = 0
        AND mcr.record_time BETWEEN #{startDate} AND #{endDate}
        <if test="deptId != null and deptId != ''">
            AND a.dept_id = #{deptId}
        </if>
        <if test="assetId != null and assetId != ''">
            AND mcr.asset_id = #{assetId}
        </if>
        GROUP BY mcr.cost_type
        ORDER BY amount DESC
    </select>

    <!-- 查询预防性维护成本 -->
    <select id="selectPreventiveCost" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(mcr.cost_amount), 0)
        FROM maintenance_cost_record mcr
        LEFT JOIN maintenance_task mt ON mcr.task_id = mt.task_id
        LEFT JOIN asset_ledger a ON mcr.asset_id = a.asset_id
        WHERE mcr.deleted = 0
        AND mcr.task_id IS NOT NULL
        AND mcr.record_time BETWEEN #{startDate} AND #{endDate}
        <if test="deptId != null and deptId != ''">
            AND a.dept_id = #{deptId}
        </if>
        <if test="assetId != null and assetId != ''">
            AND mcr.asset_id = #{assetId}
        </if>
    </select>

    <!-- 查询故障维护成本 -->
    <select id="selectCorrectiveCost" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(mcr.cost_amount), 0)
        FROM maintenance_cost_record mcr
        LEFT JOIN asset_fault_report afr ON mcr.fault_id = afr.fault_id
        LEFT JOIN asset_ledger a ON mcr.asset_id = a.asset_id
        WHERE mcr.deleted = 0
        AND mcr.fault_id IS NOT NULL
        AND mcr.record_time BETWEEN #{startDate} AND #{endDate}
        <if test="deptId != null and deptId != ''">
            AND a.dept_id = #{deptId}
        </if>
        <if test="assetId != null and assetId != ''">
            AND mcr.asset_id = #{assetId}
        </if>
    </select>

    <!-- 按任务ID查询成本记录 -->
    <select id="selectByTaskId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM maintenance_cost_record
        WHERE deleted = 0 AND task_id = #{taskId}
        ORDER BY record_time DESC
    </select>

    <!-- 按故障ID查询成本记录 -->
    <select id="selectByFaultId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM maintenance_cost_record
        WHERE deleted = 0 AND fault_id = #{faultId}
        ORDER BY record_time DESC
    </select>

    <!-- 按资产ID查询成本记录 -->
    <select id="selectByAssetId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM maintenance_cost_record
        WHERE deleted = 0 AND asset_id = #{assetId}
        <if test="startDate != null">
            AND record_time >= #{startDate}
        </if>
        <if test="endDate != null">
            AND record_time &lt;= #{endDate}
        </if>
        ORDER BY record_time DESC
    </select>

    <!-- 查询成本记录总数 -->
    <select id="countCostRecords" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM maintenance_cost_record mcr
        LEFT JOIN asset_ledger a ON mcr.asset_id = a.asset_id
        WHERE mcr.deleted = 0
        AND mcr.record_time BETWEEN #{startDate} AND #{endDate}
        <if test="deptId != null and deptId != ''">
            AND a.dept_id = #{deptId}
        </if>
        <if test="assetId != null and assetId != ''">
            AND mcr.asset_id = #{assetId}
        </if>
        <if test="costTypes != null and costTypes.size() > 0">
            AND mcr.cost_type IN
            <foreach collection="costTypes" item="costType" open="(" separator="," close=")">
                #{costType}
            </foreach>
        </if>
    </select>

    <!-- 查询平均成本 -->
    <select id="selectAvgCost" resultType="java.math.BigDecimal">
        SELECT COALESCE(AVG(mcr.cost_amount), 0)
        FROM maintenance_cost_record mcr
        LEFT JOIN asset_ledger a ON mcr.asset_id = a.asset_id
        WHERE mcr.deleted = 0
        AND mcr.record_time BETWEEN #{startDate} AND #{endDate}
        <if test="deptId != null and deptId != ''">
            AND a.dept_id = #{deptId}
        </if>
        <if test="assetId != null and assetId != ''">
            AND mcr.asset_id = #{assetId}
        </if>
        <if test="costType != null">
            AND mcr.cost_type = #{costType}
        </if>
    </select>

    <!-- 批量插入成本记录 -->
    <insert id="batchInsert">
        INSERT INTO maintenance_cost_record (
            cost_id, task_id, fault_id, asset_id, cost_type, cost_amount, cost_description,
            labor_hours, hourly_rate, material_quantity, unit_price, downtime_hours,
            downtime_loss_rate, vendor_name, record_time, create_time, create_by, deleted
        ) VALUES
        <foreach collection="records" item="record" separator=",">
            (
                #{record.costId}, #{record.taskId}, #{record.faultId}, #{record.assetId},
                #{record.costType}, #{record.costAmount}, #{record.costDescription},
                #{record.laborHours}, #{record.hourlyRate}, #{record.materialQuantity},
                #{record.unitPrice}, #{record.downtimeHours}, #{record.downtimeLossRate},
                #{record.vendorName}, #{record.recordTime}, #{record.createTime},
                #{record.createBy}, 0
            )
        </foreach>
    </insert>

</mapper>
