<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.production.mapper.ProductionTaskNodeAttachmentMapper">
    
    <resultMap type="com.jingfang.production.module.entity.vo.ProductionTaskNodeAttachmentVo" id="ProductionTaskNodeAttachmentVoResult">
        <result property="attachmentId" column="attachment_id" />
        <result property="nodeId" column="node_id" />
        <result property="nodeName" column="node_name" />
        <result property="taskId" column="task_id" />
        <result property="taskName" column="task_name" />
        <result property="fileName" column="file_name" />
        <result property="fileType" column="file_type" />
        <result property="fileSize" column="file_size" />
        <result property="fileSizeDesc" column="file_size_desc" />
        <result property="filePath" column="file_path" />
        <result property="uploadUserId" column="upload_user_id" />
        <result property="uploadUserName" column="upload_user_name" />
        <result property="uploadTime" column="upload_time" />
        <result property="remark" column="remark" />
        <result property="downloadUrl" column="download_url" />
        <result property="canDelete" column="can_delete" />
    </resultMap>
    
    <sql id="selectProductionTaskNodeAttachmentVo">
        SELECT 
            ptna.attachment_id,
            ptna.node_id,
            ptn.node_name,
            ptn.task_id,
            pt.task_name,
            ptna.file_name,
            ptna.file_type,
            ptna.file_size,
            CASE 
                WHEN ptna.file_size &lt; 1024 THEN CONCAT(ptna.file_size, 'B')
                WHEN ptna.file_size &lt; 1048576 THEN CONCAT(ROUND(ptna.file_size/1024, 2), 'KB')
                WHEN ptna.file_size &lt; 1073741824 THEN CONCAT(ROUND(ptna.file_size/1048576, 2), 'MB')
                ELSE CONCAT(ROUND(ptna.file_size/1073741824, 2), 'GB')
            END as file_size_desc,
            ptna.file_path,
            ptna.upload_user_id,
            su.nick_name as upload_user_name,
            ptna.upload_time,
            ptna.remark,
            CONCAT('/common/download?fileName=', ptna.file_name, '&amp;filePath=', ptna.file_path) as download_url,
            CASE 
                WHEN ptn.status IN (1, 2) THEN 1
                ELSE 0
            END as can_delete
        FROM production_task_node_attachment ptna
        LEFT JOIN production_task_node ptn ON ptna.node_id = ptn.node_id
        LEFT JOIN production_task pt ON ptn.task_id = pt.task_id
        LEFT JOIN sys_user su ON ptna.upload_user_id = su.user_id
    </sql>
    
    <select id="selectAttachmentsByNodeId" resultMap="ProductionTaskNodeAttachmentVoResult">
        <include refid="selectProductionTaskNodeAttachmentVo"/>
        WHERE ptna.node_id = #{nodeId}
        ORDER BY ptna.upload_time DESC
    </select>
    
    <select id="selectAttachmentsByTaskId" resultMap="ProductionTaskNodeAttachmentVoResult">
        <include refid="selectProductionTaskNodeAttachmentVo"/>
        WHERE ptn.task_id = #{taskId}
        ORDER BY ptn.sequence_no ASC, ptna.upload_time DESC
    </select>
    
    <select id="selectAttachmentById" resultMap="ProductionTaskNodeAttachmentVoResult">
        <include refid="selectProductionTaskNodeAttachmentVo"/>
        WHERE ptna.attachment_id = #{attachmentId}
    </select>
    
    <select id="countAttachmentsByNodeId" resultType="int">
        SELECT COUNT(*) 
        FROM production_task_node_attachment 
        WHERE node_id = #{nodeId}
    </select>
    
    <select id="countAttachmentsByTaskId" resultType="int">
        SELECT COUNT(*) 
        FROM production_task_node_attachment ptna
        LEFT JOIN production_task_node ptn ON ptna.node_id = ptn.node_id
        WHERE ptn.task_id = #{taskId}
    </select>
    
    <delete id="deleteAttachmentsByNodeId">
        DELETE FROM production_task_node_attachment WHERE node_id = #{nodeId}
    </delete>
    
    <delete id="deleteAttachmentsByTaskId">
        DELETE ptna FROM production_task_node_attachment ptna
        LEFT JOIN production_task_node ptn ON ptna.node_id = ptn.node_id
        WHERE ptn.task_id = #{taskId}
    </delete>
    
    <insert id="batchInsertAttachments">
        INSERT INTO production_task_node_attachment (
            attachment_id, node_id, file_name, file_type, file_size, file_path, upload_user_id, upload_time, remark
        ) VALUES
        <foreach collection="attachmentList" item="attachment" separator=",">
            (
                #{attachment.attachmentId}, #{attachment.nodeId}, #{attachment.fileName}, 
                #{attachment.fileType}, #{attachment.fileSize}, #{attachment.filePath}, 
                #{attachment.uploadUserId}, #{attachment.uploadTime}, #{attachment.remark}
            )
        </foreach>
    </insert>

</mapper>
