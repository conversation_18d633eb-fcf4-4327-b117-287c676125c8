package com.jingfang.production.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jingfang.production.dto.ProductionTaskNodeDto;
import com.jingfang.production.module.entity.ProductionTaskNode;
import com.jingfang.production.vo.ProductionTaskNodeVo;

import java.util.List;
import java.util.Map;

/**
 * 任务节点Service接口
 *
 * <AUTHOR>
 * @date 2025-01-16
 */
public interface ProductionTaskNodeService extends IService<ProductionTaskNode> {

    /**
     * 根据任务ID查询节点列表
     *
     * @param taskId 任务ID
     * @return 节点列表
     */
    List<ProductionTaskNodeVo> selectNodesByTaskId(String taskId);

    /**
     * 根据节点ID查询节点详情
     *
     * @param nodeId 节点ID
     * @return 节点详情
     */
    ProductionTaskNodeVo selectNodeById(String nodeId);

    /**
     * 新增任务节点
     *
     * @param nodeDto 节点信息
     * @return 结果
     */
    boolean insertTaskNode(ProductionTaskNodeDto nodeDto);

    /**
     * 修改任务节点
     *
     * @param nodeDto 节点信息
     * @return 结果
     */
    boolean updateTaskNode(ProductionTaskNodeDto nodeDto);

    /**
     * 删除任务节点
     *
     * @param nodeId 节点ID
     * @return 结果
     */
    boolean deleteTaskNode(String nodeId);

    /**
     * 批量删除任务节点
     *
     * @param nodeIds 节点ID列表
     * @return 结果
     */
    boolean deleteTaskNodes(List<String> nodeIds);

    /**
     * 开始执行节点
     *
     * @param nodeId 节点ID
     * @return 结果
     */
    boolean startNodeExecution(String nodeId);

    /**
     * 完成节点执行
     *
     * @param nodeId 节点ID
     * @return 结果
     */
    boolean completeNodeExecution(String nodeId);

    /**
     * 跳过节点
     *
     * @param nodeId 节点ID
     * @param reason 跳过原因
     * @return 结果
     */
    boolean skipNode(String nodeId, String reason);

    /**
     * 标记节点异常
     *
     * @param nodeId 节点ID
     * @param errorMessage 异常信息
     * @return 结果
     */
    boolean markNodeError(String nodeId, String errorMessage);

    /**
     * 重置节点状态
     *
     * @param nodeId 节点ID
     * @return 结果
     */
    boolean resetNodeStatus(String nodeId);

    /**
     * 查询任务的节点统计信息
     *
     * @param taskId 任务ID
     * @return 节点统计信息
     */
    Map<String, Object> selectNodeStatisticsByTaskId(String taskId);

    /**
     * 查询节点执行状态统计
     *
     * @param taskId 任务ID（可选）
     * @return 状态统计数据
     */
    List<Map<String, Object>> selectNodeStatusStatistics(String taskId);

    /**
     * 查询可执行的节点
     *
     * @param taskId 任务ID
     * @return 可执行的节点列表
     */
    List<ProductionTaskNodeVo> selectExecutableNodes(String taskId);

    /**
     * 查询正在执行的节点
     *
     * @param taskId 任务ID（可选）
     * @return 正在执行的节点列表
     */
    List<ProductionTaskNodeVo> selectExecutingNodes(String taskId);

    /**
     * 查询下一个节点
     *
     * @param taskId 任务ID
     * @param currentSequenceNo 当前节点序号
     * @return 下一个节点
     */
    ProductionTaskNodeVo selectNextNode(String taskId, Integer currentSequenceNo);

    /**
     * 查询前置节点
     *
     * @param taskId 任务ID
     * @param sequenceNo 节点序号
     * @return 前置节点列表
     */
    List<ProductionTaskNodeVo> selectPreviousNodes(String taskId, Integer sequenceNo);

    /**
     * 调整节点顺序
     *
     * @param taskId 任务ID
     * @param nodeSequenceList 节点序号列表
     * @return 结果
     */
    boolean adjustNodeSequence(String taskId, List<Map<String, Object>> nodeSequenceList);

    /**
     * 批量创建节点
     *
     * @param taskId 任务ID
     * @param nodeDtoList 节点信息列表
     * @return 结果
     */
    boolean batchCreateNodes(String taskId, List<ProductionTaskNodeDto> nodeDtoList);

    /**
     * 复制节点到其他任务
     *
     * @param sourceTaskId 源任务ID
     * @param targetTaskId 目标任务ID
     * @return 结果
     */
    boolean copyNodesToTask(String sourceTaskId, String targetTaskId);
}