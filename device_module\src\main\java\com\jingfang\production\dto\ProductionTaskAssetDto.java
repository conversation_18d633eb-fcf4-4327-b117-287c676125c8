package com.jingfang.production.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 生产任务资产关联DTO
 */
@Data
public class ProductionTaskAssetDto implements Serializable {
    
    /**
     * 关联ID
     */
    private String relationId;
    
    /**
     * 任务ID
     */
    @NotBlank(message = "任务ID不能为空")
    private String taskId;
    
    /**
     * 资产ID
     */
    @NotBlank(message = "资产ID不能为空")
    private String assetId;
    
    /**
     * 资产编码
     */
    private String assetCode;
    
    /**
     * 资产名称
     */
    private String assetName;
    
    /**
     * 使用类型(1-主要设备,2-辅助设备,3-工具,4-模具)
     */
    @NotNull(message = "使用类型不能为空")
    private Integer usageType;
    
    /**
     * 备注
     */
    private String remark;
    
    private static final long serialVersionUID = 1L;
}
