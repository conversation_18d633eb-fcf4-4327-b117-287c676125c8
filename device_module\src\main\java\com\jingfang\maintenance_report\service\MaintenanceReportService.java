package com.jingfang.maintenance_report.service;

import com.jingfang.maintenance_report.module.dto.MaintenanceReportRequest;
import com.jingfang.maintenance_report.module.vo.MaintenanceReportVo;
import jakarta.servlet.http.HttpServletResponse;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 维护报表服务接口
 */
public interface MaintenanceReportService {
    
    /**
     * 获取维护任务完成率统计
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param deptId 部门ID
     * @param assetType 资产类型
     * @param priorities 优先级列表
     * @return 任务完成率统计数据
     */
    MaintenanceReportVo.TaskCompletionVo getTaskCompletionStatistics(
        Date startDate, Date endDate, String deptId, String assetType, List<Integer> priorities);
    
    /**
     * 获取故障响应时间统计
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param faultTypes 故障类型列表
     * @param urgencyLevels 紧急程度列表
     * @param handlerIds 处理人员ID列表
     * @return 故障响应时间统计数据
     */
    MaintenanceReportVo.FaultResponseVo getFaultResponseStatistics(
        Date startDate, Date endDate, List<Integer> faultTypes, 
        List<Integer> urgencyLevels, List<Long> handlerIds);
    
    /**
     * 获取维护成本统计
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param deptId 部门ID
     * @param assetId 资产ID
     * @param costTypes 成本类型列表
     * @return 维护成本统计数据
     */
    MaintenanceReportVo.MaintenanceCostVo getMaintenanceCostStatistics(
        Date startDate, Date endDate, String deptId, String assetId, List<Integer> costTypes);
    
    /**
     * 获取综合报表数据
     * 
     * @param request 报表请求参数
     * @return 综合报表数据
     */
    MaintenanceReportVo.ComprehensiveReportVo getComprehensiveReport(MaintenanceReportRequest request);
    
    /**
     * 生成维护报表图表数据
     * 
     * @param reportType 报表类型
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param timeDimension 时间维度
     * @param filters 筛选条件
     * @return 图表数据
     */
    Map<String, Object> generateMaintenanceChartData(
        Integer reportType, Date startDate, Date endDate, 
        Integer timeDimension, Map<String, Object> filters);
    
    /**
     * 获取任务完成率趋势数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param timeDimension 时间维度
     * @param deptId 部门ID
     * @param priorities 优先级列表
     * @return 完成率趋势数据
     */
    List<MaintenanceReportVo.CompletionTrendVo> getTaskCompletionTrends(
        Date startDate, Date endDate, Integer timeDimension, String deptId, List<Integer> priorities);
    
    /**
     * 获取部门任务完成率统计
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param deptIds 部门ID列表
     * @param priorities 优先级列表
     * @return 部门完成率统计
     */
    List<MaintenanceReportVo.DeptCompletionVo> getDeptCompletionStatistics(
        Date startDate, Date endDate, List<String> deptIds, List<Integer> priorities);
    
    /**
     * 获取优先级任务完成率统计
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param deptId 部门ID
     * @return 优先级完成率统计
     */
    List<MaintenanceReportVo.PriorityCompletionVo> getPriorityCompletionStatistics(
        Date startDate, Date endDate, String deptId);
    
    /**
     * 获取故障类型响应时间统计
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param deptId 部门ID
     * @return 故障类型响应统计
     */
    List<MaintenanceReportVo.FaultTypeResponseVo> getFaultTypeResponseStatistics(
        Date startDate, Date endDate, String deptId);
    
    /**
     * 获取紧急程度响应时间统计
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param deptId 部门ID
     * @return 紧急程度响应统计
     */
    List<MaintenanceReportVo.UrgencyResponseVo> getUrgencyResponseStatistics(
        Date startDate, Date endDate, String deptId);
    
    /**
     * 获取响应时间趋势数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param timeDimension 时间维度
     * @param faultTypes 故障类型列表
     * @return 响应时间趋势数据
     */
    List<MaintenanceReportVo.ResponseTrendVo> getResponseTimeTrends(
        Date startDate, Date endDate, Integer timeDimension, List<Integer> faultTypes);
    
    /**
     * 获取成本趋势数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param timeDimension 时间维度
     * @param deptId 部门ID
     * @param assetId 资产ID
     * @param costTypes 成本类型列表
     * @return 成本趋势数据
     */
    List<MaintenanceReportVo.CostTrendVo> getCostTrends(
        Date startDate, Date endDate, Integer timeDimension, 
        String deptId, String assetId, List<Integer> costTypes);
    
    /**
     * 获取部门成本统计
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param deptIds 部门ID列表
     * @param costTypes 成本类型列表
     * @return 部门成本统计
     */
    List<MaintenanceReportVo.DeptCostVo> getDeptCostStatistics(
        Date startDate, Date endDate, List<String> deptIds, List<Integer> costTypes);
    
    /**
     * 获取资产成本统计
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param deptId 部门ID
     * @param assetIds 资产ID列表
     * @param costTypes 成本类型列表
     * @return 资产成本统计
     */
    List<MaintenanceReportVo.AssetCostVo> getAssetCostStatistics(
        Date startDate, Date endDate, String deptId, 
        List<String> assetIds, List<Integer> costTypes);
    
    /**
     * 获取成本类型分布
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param deptId 部门ID
     * @param assetId 资产ID
     * @return 成本类型分布
     */
    List<MaintenanceReportVo.CostTypeVo> getCostTypeDistribution(
        Date startDate, Date endDate, String deptId, String assetId);
    
    /**
     * 导出维护报表
     * 
     * @param response HTTP响应
     * @param request 报表请求参数
     */
    void exportMaintenanceReport(HttpServletResponse response, MaintenanceReportRequest request);
    
    /**
     * 导出任务完成率报表
     * 
     * @param response HTTP响应
     * @param request 报表请求参数
     */
    void exportTaskCompletionReport(HttpServletResponse response, MaintenanceReportRequest request);
    
    /**
     * 导出故障响应时间报表
     * 
     * @param response HTTP响应
     * @param request 报表请求参数
     */
    void exportFaultResponseReport(HttpServletResponse response, MaintenanceReportRequest request);
    
    /**
     * 导出维护成本报表
     * 
     * @param response HTTP响应
     * @param request 报表请求参数
     */
    void exportMaintenanceCostReport(HttpServletResponse response, MaintenanceReportRequest request);
    
    /**
     * 获取报表配置选项
     * 
     * @return 报表配置选项
     */
    Map<String, Object> getReportConfigOptions();
    
    /**
     * 保存报表配置
     * 
     * @param configName 配置名称
     * @param reportType 报表类型
     * @param config 配置内容
     * @return 保存结果
     */
    boolean saveReportConfig(String configName, Integer reportType, Map<String, Object> config);
    
    /**
     * 获取用户报表配置列表
     * 
     * @param reportType 报表类型
     * @return 配置列表
     */
    List<Map<String, Object>> getUserReportConfigs(Integer reportType);
}
