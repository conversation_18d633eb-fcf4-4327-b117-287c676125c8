package com.jingfang.web.controller.asset.stocktaking;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.asset_stocktaking.module.dto.StocktakingPlanDto;
import com.jingfang.asset_stocktaking.module.entity.AssetStocktakingPlan;
import com.jingfang.asset_stocktaking.module.request.PlanSearchRequest;
import com.jingfang.asset_stocktaking.module.vo.StocktakingPlanVo;
import com.jingfang.asset_stocktaking.service.StocktakingPlanService;
import com.jingfang.common.annotation.Log;
import com.jingfang.common.core.controller.BaseController;
import com.jingfang.common.core.domain.AjaxResult;
import com.jingfang.common.core.page.TableDataInfo;
import com.jingfang.common.enums.BusinessType;
import com.jingfang.common.utils.poi.ExcelUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


import java.util.Arrays;
import java.util.List;

/**
 * 盘点计划控制器
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@RestController
@RequestMapping("/asset/stocktaking/plan")
public class StocktakingPlanController extends BaseController {

    @Resource
    private StocktakingPlanService planService;

    /**
     * 查询盘点计划列表
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:plan:view')")
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody PlanSearchRequest request) {
        // 使用MyBatis-Plus分页，不调用PageHelper的startPage()
        IPage<StocktakingPlanVo> page = planService.selectPlanList(request);
        TableDataInfo dataTable = new TableDataInfo();
        dataTable.setCode(200);
        dataTable.setMsg("查询成功");
        dataTable.setRows(page.getRecords());
        dataTable.setTotal(page.getTotal());
        return dataTable;
    }

    /**
     * 导出盘点计划列表
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:plan:export')")
    @Log(title = "盘点计划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody PlanSearchRequest request) {
        request.setPageNum(1);
        request.setPageSize(10000);
        IPage<StocktakingPlanVo> page = planService.selectPlanList(request);
        ExcelUtil<StocktakingPlanVo> util = new ExcelUtil<>(StocktakingPlanVo.class);
        util.exportExcel(response, page.getRecords(), "盘点计划数据");
    }

    /**
     * 获取盘点计划详细信息
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:plan:view')")
    @GetMapping("/{planId}")
    public AjaxResult getInfo(@PathVariable("planId") String planId) {
        StocktakingPlanVo planVo = planService.selectPlanById(planId);
        return success(planVo);
    }

    /**
     * 新增盘点计划
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:plan:add')")
    @Log(title = "盘点计划", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody StocktakingPlanDto planDto) {
        if (planService.createPlan(planDto)) {
            return success("创建盘点计划成功");
        }
        return error("创建盘点计划失败");
    }

    /**
     * 修改盘点计划
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:plan:edit')")
    @Log(title = "盘点计划", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody StocktakingPlanDto planDto) {
        if (planService.editPlan(planDto)) {
            return success("修改盘点计划成功");
        }
        return error("修改盘点计划失败");
    }

    /**
     * 删除盘点计划
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:plan:remove')")
    @Log(title = "盘点计划", businessType = BusinessType.DELETE)
    @DeleteMapping("/{planIds}")
    public AjaxResult remove(@PathVariable String[] planIds) {
        if (planService.batchDeletePlans(Arrays.asList(planIds))) {
            return success("删除盘点计划成功");
        }
        return error("删除盘点计划失败");
    }

    /**
     * 提交审批
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:plan:edit')")
    @Log(title = "盘点计划", businessType = BusinessType.UPDATE)
    @PostMapping("/{planId}/submit")
    public AjaxResult submitForApproval(@PathVariable("planId") String planId) {
        if (planService.submitForApproval(planId)) {
            return success("提交审批成功");
        }
        return error("提交审批失败");
    }

    /**
     * 审批通过
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:plan:approve')")
    @Log(title = "盘点计划", businessType = BusinessType.UPDATE)
    @PostMapping("/{planId}/approve")
    public AjaxResult approve(@PathVariable("planId") String planId, 
                             @RequestParam(required = false) String approvalComment) {
        if (planService.approvePlan(planId, approvalComment)) {
            return success("审批通过成功");
        }
        return error("审批通过失败");
    }

    /**
     * 审批拒绝
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:plan:approve')")
    @Log(title = "盘点计划", businessType = BusinessType.UPDATE)
    @PostMapping("/{planId}/reject")
    public AjaxResult reject(@PathVariable("planId") String planId, 
                            @RequestParam String rejectReason) {
        if (planService.rejectPlan(planId, rejectReason)) {
            return success("审批拒绝成功");
        }
        return error("审批拒绝失败");
    }

    /**
     * 启动盘点计划
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:plan:start')")
    @Log(title = "盘点计划", businessType = BusinessType.UPDATE)
    @PostMapping("/{planId}/start")
    public AjaxResult start(@PathVariable("planId") String planId) {
        if (planService.startPlan(planId)) {
            return success("启动盘点计划成功");
        }
        return error("启动盘点计划失败");
    }

    /**
     * 完成盘点计划
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:plan:complete')")
    @Log(title = "盘点计划", businessType = BusinessType.UPDATE)
    @PostMapping("/{planId}/complete")
    public AjaxResult complete(@PathVariable("planId") String planId) {
        if (planService.completePlan(planId)) {
            return success("完成盘点计划成功");
        }
        return error("完成盘点计划失败");
    }

    /**
     * 取消盘点计划
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:plan:cancel')")
    @Log(title = "盘点计划", businessType = BusinessType.UPDATE)
    @PostMapping("/{planId}/cancel")
    public AjaxResult cancel(@PathVariable("planId") String planId, 
                            @RequestParam String cancelReason) {
        if (planService.cancelPlan(planId, cancelReason)) {
            return success("取消盘点计划成功");
        }
        return error("取消盘点计划失败");
    }

    /**
     * 复制盘点计划
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:plan:add')")
    @Log(title = "盘点计划", businessType = BusinessType.INSERT)
    @PostMapping("/{planId}/copy")
    public AjaxResult copy(@PathVariable("planId") String planId, 
                          @RequestParam String newPlanName) {
        String newPlanId = planService.copyPlan(planId, newPlanName);
        if (newPlanId != null) {
            return AjaxResult.success("复制盘点计划成功", newPlanId);
        }
        return error("复制盘点计划失败");
    }

    /**
     * 检查计划名称是否重复
     */
    @GetMapping("/checkName")
    public AjaxResult checkPlanName(@RequestParam String planName, 
                                   @RequestParam(required = false) String excludePlanId) {
        boolean exists = planService.checkPlanNameExists(planName, excludePlanId);
        return success(!exists);
    }

    /**
     * 查询用户有权限的盘点计划
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:plan:view')")
    @GetMapping("/user/{userId}")
    public AjaxResult getPlansByUser(@PathVariable("userId") Long userId) {
        List<AssetStocktakingPlan> plans = planService.selectPlanByUserPermission(userId);
        return success(plans);
    }

    /**
     * 查询即将到期的盘点计划
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:plan:view')")
    @GetMapping("/expiring")
    public AjaxResult getExpiringPlans(@RequestParam(defaultValue = "7") Integer days) {
        List<AssetStocktakingPlan> plans = planService.selectExpiringPlans(days);
        return success(plans);
    }

    /**
     * 查询逾期的盘点计划
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:plan:view')")
    @GetMapping("/overdue")
    public AjaxResult getOverduePlans() {
        List<AssetStocktakingPlan> plans = planService.selectOverduePlans();
        return success(plans);
    }

    /**
     * 统计各状态的盘点计划数量
     */
    @PreAuthorize("@ss.hasPermi('stocktaking:plan:view')")
    @GetMapping("/statistics/status")
    public AjaxResult getStatusStatistics() {
        List<java.util.Map<String, Object>> statistics = planService.countPlanByStatus();
        return success(statistics);
    }
}
