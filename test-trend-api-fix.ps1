# 测试修复后的消耗趋势数据接口
$token = "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiZjM0NDU2YTktYmU2Mi00MTY1LTk4ZjUtN2VhODNlM2VhNjBhIn0.xBoYuAG7vY_hqXeL7Siz_xiIG3ZjEOh_49L5cmvIFUvTHGWul0C33aZWLUbhXj6a4NHWO5aiIggbxC6MXerh_A"

Write-Host "测试修复后的消耗趋势数据接口..." -ForegroundColor Green

# 测试函数
function Test-TrendAPI {
    param(
        [string]$TimeDimension,
        [string]$Description
    )
    
    Write-Host "`n测试: $Description (timeDimension=$TimeDimension)" -ForegroundColor Cyan
    
    try {
        $headers = @{
            "Authorization" = "Bearer $token"
        }
        
        $url = "http://localhost:8080/item/consumption-report/trend?itemId=TEST_ITEM&startDate=2024-01-01&endDate=2024-12-31&timeDimension=$TimeDimension"
        Write-Host "请求URL: $url" -ForegroundColor Gray
        
        $response = Invoke-RestMethod -Uri $url -Method GET -Headers $headers
        
        Write-Host "✅ 成功" -ForegroundColor Green
        Write-Host "响应消息: $($response.msg)" -ForegroundColor White
        if ($response.data) {
            Write-Host "数据条数: $($response.data.Count)" -ForegroundColor White
            if ($response.data.Count -gt 0) {
                Write-Host "示例数据: $($response.data[0] | ConvertTo-Json)" -ForegroundColor White
            }
        }
        return $true
    }
    catch {
        Write-Host "❌ 失败" -ForegroundColor Red
        Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
        if ($_.Exception.Response) {
            $statusCode = $_.Exception.Response.StatusCode
            Write-Host "状态码: $statusCode" -ForegroundColor Red
        }
        return $false
    }
}

# 测试不同的时间维度
Test-TrendAPI -TimeDimension "1" -Description "日度趋势"
Test-TrendAPI -TimeDimension "2" -Description "周度趋势"  
Test-TrendAPI -TimeDimension "3" -Description "月度趋势"
Test-TrendAPI -TimeDimension "4" -Description "季度趋势"
Test-TrendAPI -TimeDimension "5" -Description "年度趋势"

Write-Host "`n测试完成!" -ForegroundColor Green
