package com.jingfang.production.module.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 任务节点DTO
 */
@Data
public class ProductionTaskNodeDto implements Serializable {
    
    /**
     * 节点ID
     */
    private String nodeId;
    
    /**
     * 任务ID
     */
    @NotBlank(message = "任务ID不能为空")
    private String taskId;
    
    /**
     * 节点名称
     */
    @NotBlank(message = "节点名称不能为空")
    private String nodeName;
    
    /**
     * 节点类型(1-开始,2-处理,3-检验,4-决策,5-结束)
     */
    @NotNull(message = "节点类型不能为空")
    private Integer nodeType;
    
    /**
     * 节点序号
     */
    @NotNull(message = "节点序号不能为空")
    private Integer sequenceNo;
    
    /**
     * 是否必需(1-是,0-否)
     */
    @NotNull(message = "是否必需不能为空")
    private Integer isRequired;
    
    /**
     * 预计耗时(分钟)
     */
    private Integer estimatedDuration;
    
    /**
     * 实际耗时(分钟)
     */
    private Integer actualDuration;
    
    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;
    
    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;
    
    /**
     * 状态(1-待执行,2-执行中,3-已完成,4-跳过,5-异常)
     */
    private Integer status;
    
    private static final long serialVersionUID = 1L;
}
