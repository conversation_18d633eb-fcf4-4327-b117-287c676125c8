package com.jingfang.item_consumption_report.module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 库存消耗记录汇总表
 * @TableName item_consumption_summary
 */
@TableName(value = "item_consumption_summary")
@Data
public class ItemConsumptionSummary implements Serializable {
    
    /**
     * 汇总记录ID
     */
    @TableId(type = IdType.INPUT)
    private String summaryId;
    
    /**
     * 物品ID
     */
    private String itemId;
    
    /**
     * 仓库ID
     */
    private Integer warehouseId;
    
    /**
     * 消耗日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date consumptionDate;
    
    /**
     * 消耗类型(1-生产消耗,2-维护消耗,3-领用消耗,4-其他消耗)
     */
    private Integer consumptionType;
    
    /**
     * 总消耗数量
     */
    private BigDecimal totalQuantity;
    
    /**
     * 总消耗金额
     */
    private BigDecimal totalAmount;
    
    /**
     * 平均单价
     */
    private BigDecimal avgUnitPrice;
    
    /**
     * 消耗次数
     */
    private Integer consumptionCount;
    
    /**
     * 关联生产产量
     */
    private BigDecimal relatedProductionOutput;
    
    /**
     * 消耗效率(单位产量消耗)
     */
    private BigDecimal consumptionEfficiency;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    
    private static final long serialVersionUID = 1L;
}
