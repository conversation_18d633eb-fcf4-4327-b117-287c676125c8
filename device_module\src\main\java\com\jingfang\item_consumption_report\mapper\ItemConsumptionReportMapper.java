package com.jingfang.item_consumption_report.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.item_consumption_report.module.entity.ItemConsumptionSummary;
import com.jingfang.item_consumption_report.module.request.ItemConsumptionReportRequest;
import com.jingfang.item_consumption_report.module.vo.ItemConsumptionReportVo;
import com.jingfang.item_consumption_report.module.vo.ItemConsumptionStatisticsVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 库存消耗报表Mapper接口
 */
@Mapper
public interface ItemConsumptionReportMapper extends BaseMapper<ItemConsumptionSummary> {
    
    /**
     * 分页查询库存消耗报表数据
     * 
     * @param page 分页对象
     * @param request 查询条件
     * @return 消耗报表数据
     */
    IPage<ItemConsumptionReportVo> selectConsumptionReportList(IPage<ItemConsumptionReportVo> page, 
                                                               @Param("request") ItemConsumptionReportRequest request);
    
    /**
     * 查询库存消耗统计数据
     * 
     * @param request 查询条件
     * @return 统计数据
     */
    ItemConsumptionStatisticsVo.OverallStatistics selectOverallStatistics(@Param("request") ItemConsumptionReportRequest request);
    
    /**
     * 查询消耗类型统计数据
     * 
     * @param request 查询条件
     * @return 消耗类型统计列表
     */
    List<ItemConsumptionStatisticsVo.ConsumptionTypeStatistics> selectConsumptionTypeStatistics(@Param("request") ItemConsumptionReportRequest request);
    
    /**
     * 查询效率等级统计数据
     * 
     * @param request 查询条件
     * @return 效率等级统计列表
     */
    List<ItemConsumptionStatisticsVo.EfficiencyLevelStatistics> selectEfficiencyLevelStatistics(@Param("request") ItemConsumptionReportRequest request);
    
    /**
     * 查询仓库消耗统计数据
     * 
     * @param request 查询条件
     * @return 仓库统计列表
     */
    List<ItemConsumptionStatisticsVo.WarehouseStatistics> selectWarehouseStatistics(@Param("request") ItemConsumptionReportRequest request);
    
    /**
     * 查询部门消耗统计数据
     * 
     * @param request 查询条件
     * @return 部门统计列表
     */
    List<ItemConsumptionStatisticsVo.DepartmentStatistics> selectDepartmentStatistics(@Param("request") ItemConsumptionReportRequest request);
    
    /**
     * 查询消耗趋势数据
     * 
     * @param itemId 物品ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param timeDimension 时间维度
     * @return 趋势数据
     */
    List<ItemConsumptionReportVo.ConsumptionTrendVo> selectConsumptionTrend(@Param("itemId") String itemId,
                                                                           @Param("startDate") Date startDate,
                                                                           @Param("endDate") Date endDate,
                                                                           @Param("timeDimension") Integer timeDimension);
    
    /**
     * 查询消耗类型分布数据
     * 
     * @param itemId 物品ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 类型分布数据
     */
    List<ItemConsumptionReportVo.ConsumptionTypeVo> selectConsumptionTypeDistribution(@Param("itemId") String itemId,
                                                                                     @Param("startDate") Date startDate,
                                                                                     @Param("endDate") Date endDate);
    
    /**
     * 查询消耗效率排行榜
     * 
     * @param request 查询条件
     * @param limit 限制数量
     * @return 效率排行数据
     */
    List<ItemConsumptionReportVo> selectEfficiencyRanking(@Param("request") ItemConsumptionReportRequest request,
                                                          @Param("limit") Integer limit);
    
    /**
     * 查询消耗量排行榜
     * 
     * @param request 查询条件
     * @param limit 限制数量
     * @return 消耗量排行数据
     */
    List<ItemConsumptionReportVo> selectConsumptionRanking(@Param("request") ItemConsumptionReportRequest request,
                                                           @Param("limit") Integer limit);
    
    /**
     * 生成消耗汇总数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 生成的记录数
     */
    int generateConsumptionSummary(@Param("startDate") Date startDate, @Param("endDate") Date endDate);
    
    /**
     * 更新效率分析数据
     * 
     * @param itemId 物品ID
     * @param analysisPeriod 分析周期
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 更新的记录数
     */
    int updateEfficiencyAnalysis(@Param("itemId") String itemId,
                                @Param("analysisPeriod") String analysisPeriod,
                                @Param("startDate") Date startDate,
                                @Param("endDate") Date endDate);
    
    /**
     * 查询图表数据
     * 
     * @param chartType 图表类型
     * @param request 查询条件
     * @return 图表数据
     */
    List<Map<String, Object>> selectChartData(@Param("chartType") String chartType,
                                              @Param("request") ItemConsumptionReportRequest request);
}
