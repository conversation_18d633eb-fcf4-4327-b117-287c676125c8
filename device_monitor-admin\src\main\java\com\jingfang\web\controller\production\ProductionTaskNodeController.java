package com.jingfang.web.controller.production;

import com.jingfang.common.annotation.Log;
import com.jingfang.common.core.controller.BaseController;
import com.jingfang.common.core.domain.AjaxResult;
import com.jingfang.common.enums.BusinessType;
import com.jingfang.production.dto.ProductionTaskNodeDto;
import com.jingfang.production.service.ProductionTaskNodeService;
import com.jingfang.production.module.entity.vo.ProductionTaskNodeVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 任务节点Controller
 */
@Slf4j
@RestController
@RequestMapping("/production/task/node")
public class ProductionTaskNodeController extends BaseController {

    @Autowired
    private ProductionTaskNodeService taskNodeService;

    /**
     * 根据任务ID查询节点列表
     */
    @PreAuthorize("@ss.hasPermi('production:task:query')")
    @GetMapping("/list/{taskId}")
    public AjaxResult list(@PathVariable String taskId) {
        try {
            List<ProductionTaskNodeVo> list = taskNodeService.selectNodesByTaskId(taskId);
            return success(list);
        } catch (Exception e) {
            log.error("查询任务节点列表失败，任务ID：{}", taskId, e);
            return error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取任务节点详细信息
     */
    @PreAuthorize("@ss.hasPermi('production:task:query')")
    @GetMapping("/{nodeId}")
    public AjaxResult getInfo(@PathVariable String nodeId) {
        try {
            ProductionTaskNodeVo nodeVo = taskNodeService.selectNodeById(nodeId);
            return success(nodeVo);
        } catch (Exception e) {
            log.error("查询任务节点详情失败，节点ID：{}", nodeId, e);
            return error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 新增任务节点
     */
    @PreAuthorize("@ss.hasPermi('production:task:edit')")
    @Log(title = "任务节点", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody ProductionTaskNodeDto nodeDto) {
        try {
            boolean result = taskNodeService.insertTaskNode(nodeDto);
            return toAjax(result);
        } catch (Exception e) {
            log.error("新增任务节点失败", e);
            return error("新增失败：" + e.getMessage());
        }
    }

    /**
     * 修改任务节点
     */
    @PreAuthorize("@ss.hasPermi('production:task:edit')")
    @Log(title = "任务节点", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody ProductionTaskNodeDto nodeDto) {
        try {
            boolean result = taskNodeService.updateTaskNode(nodeDto);
            return toAjax(result);
        } catch (Exception e) {
            log.error("修改任务节点失败，节点ID：{}", nodeDto.getNodeId(), e);
            return error("修改失败：" + e.getMessage());
        }
    }

    /**
     * 删除任务节点
     */
    @PreAuthorize("@ss.hasPermi('production:task:edit')")
    @Log(title = "任务节点", businessType = BusinessType.DELETE)
    @DeleteMapping("/{nodeIds}")
    public AjaxResult remove(@PathVariable String[] nodeIds) {
        try {
            boolean result = taskNodeService.deleteTaskNodes(List.of(nodeIds));
            return toAjax(result);
        } catch (Exception e) {
            log.error("删除任务节点失败", e);
            return error("删除失败：" + e.getMessage());
        }
    }

    /**
     * 开始执行节点
     */
    @PreAuthorize("@ss.hasPermi('production:task:edit')")
    @Log(title = "任务节点", businessType = BusinessType.UPDATE)
    @PostMapping("/start/{nodeId}")
    public AjaxResult startExecution(@PathVariable String nodeId) {
        try {
            boolean result = taskNodeService.startNodeExecution(nodeId);
            return toAjax(result);
        } catch (Exception e) {
            log.error("开始执行节点失败，节点ID：{}", nodeId, e);
            return error("开始执行失败：" + e.getMessage());
        }
    }

    /**
     * 完成节点执行
     */
    @PreAuthorize("@ss.hasPermi('production:task:edit')")
    @Log(title = "任务节点", businessType = BusinessType.UPDATE)
    @PostMapping("/complete/{nodeId}")
    public AjaxResult completeExecution(@PathVariable String nodeId) {
        try {
            boolean result = taskNodeService.completeNodeExecution(nodeId);
            return toAjax(result);
        } catch (Exception e) {
            log.error("完成节点执行失败，节点ID：{}", nodeId, e);
            return error("完成执行失败：" + e.getMessage());
        }
    }

    /**
     * 跳过节点
     */
    @PreAuthorize("@ss.hasPermi('production:task:edit')")
    @Log(title = "任务节点", businessType = BusinessType.UPDATE)
    @PostMapping("/skip/{nodeId}")
    public AjaxResult skipNode(@PathVariable String nodeId, @RequestParam(required = false) String reason) {
        try {
            boolean result = taskNodeService.skipNode(nodeId, reason);
            return toAjax(result);
        } catch (Exception e) {
            log.error("跳过节点失败，节点ID：{}", nodeId, e);
            return error("跳过失败：" + e.getMessage());
        }
    }

    /**
     * 标记节点异常
     */
    @PreAuthorize("@ss.hasPermi('production:task:edit')")
    @Log(title = "任务节点", businessType = BusinessType.UPDATE)
    @PostMapping("/error/{nodeId}")
    public AjaxResult markError(@PathVariable String nodeId, @RequestParam String errorMessage) {
        try {
            boolean result = taskNodeService.markNodeError(nodeId, errorMessage);
            return toAjax(result);
        } catch (Exception e) {
            log.error("标记节点异常失败，节点ID：{}", nodeId, e);
            return error("标记异常失败：" + e.getMessage());
        }
    }

    /**
     * 重置节点状态
     */
    @PreAuthorize("@ss.hasPermi('production:task:edit')")
    @Log(title = "任务节点", businessType = BusinessType.UPDATE)
    @PostMapping("/reset/{nodeId}")
    public AjaxResult resetStatus(@PathVariable String nodeId) {
        try {
            boolean result = taskNodeService.resetNodeStatus(nodeId);
            return toAjax(result);
        } catch (Exception e) {
            log.error("重置节点状态失败，节点ID：{}", nodeId, e);
            return error("重置失败：" + e.getMessage());
        }
    }

    /**
     * 查询任务的节点统计信息
     */
    @PreAuthorize("@ss.hasPermi('production:task:query')")
    @GetMapping("/statistics/{taskId}")
    public AjaxResult getNodeStatistics(@PathVariable String taskId) {
        try {
            Map<String, Object> statistics = taskNodeService.selectNodeStatisticsByTaskId(taskId);
            return success(statistics);
        } catch (Exception e) {
            log.error("查询节点统计信息失败，任务ID：{}", taskId, e);
            return error("查询统计失败：" + e.getMessage());
        }
    }

    /**
     * 查询节点执行状态统计
     */
    @PreAuthorize("@ss.hasPermi('production:task:query')")
    @GetMapping("/statistics/status")
    public AjaxResult getStatusStatistics(@RequestParam(required = false) String taskId) {
        try {
            List<Map<String, Object>> statistics = taskNodeService.selectNodeStatusStatistics(taskId);
            return success(statistics);
        } catch (Exception e) {
            log.error("查询节点状态统计失败", e);
            return error("查询统计失败：" + e.getMessage());
        }
    }

    /**
     * 查询可执行的节点
     */
    @PreAuthorize("@ss.hasPermi('production:task:query')")
    @GetMapping("/executable/{taskId}")
    public AjaxResult getExecutableNodes(@PathVariable String taskId) {
        try {
            List<ProductionTaskNodeVo> nodes = taskNodeService.selectExecutableNodes(taskId);
            return success(nodes);
        } catch (Exception e) {
            log.error("查询可执行节点失败，任务ID：{}", taskId, e);
            return error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 查询正在执行的节点
     */
    @PreAuthorize("@ss.hasPermi('production:task:query')")
    @GetMapping("/executing")
    public AjaxResult getExecutingNodes(@RequestParam(required = false) String taskId) {
        try {
            List<ProductionTaskNodeVo> nodes = taskNodeService.selectExecutingNodes(taskId);
            return success(nodes);
        } catch (Exception e) {
            log.error("查询正在执行节点失败", e);
            return error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 查询下一个节点
     */
    @PreAuthorize("@ss.hasPermi('production:task:query')")
    @GetMapping("/next/{taskId}")
    public AjaxResult getNextNode(@PathVariable String taskId, @RequestParam Integer currentSequenceNo) {
        try {
            ProductionTaskNodeVo nextNode = taskNodeService.selectNextNode(taskId, currentSequenceNo);
            return success(nextNode);
        } catch (Exception e) {
            log.error("查询下一个节点失败，任务ID：{}，当前序号：{}", taskId, currentSequenceNo, e);
            return error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 查询前置节点
     */
    @PreAuthorize("@ss.hasPermi('production:task:query')")
    @GetMapping("/previous/{taskId}")
    public AjaxResult getPreviousNodes(@PathVariable String taskId, @RequestParam Integer sequenceNo) {
        try {
            List<ProductionTaskNodeVo> nodes = taskNodeService.selectPreviousNodes(taskId, sequenceNo);
            return success(nodes);
        } catch (Exception e) {
            log.error("查询前置节点失败，任务ID：{}，序号：{}", taskId, sequenceNo, e);
            return error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 调整节点顺序
     */
    @PreAuthorize("@ss.hasPermi('production:task:edit')")
    @Log(title = "任务节点", businessType = BusinessType.UPDATE)
    @PostMapping("/sequence/{taskId}")
    public AjaxResult adjustSequence(@PathVariable String taskId, @RequestBody List<Map<String, Object>> nodeSequenceList) {
        try {
            boolean result = taskNodeService.adjustNodeSequence(taskId, nodeSequenceList);
            return toAjax(result);
        } catch (Exception e) {
            log.error("调整节点顺序失败，任务ID：{}", taskId, e);
            return error("调整顺序失败：" + e.getMessage());
        }
    }

    /**
     * 批量创建节点
     */
    @PreAuthorize("@ss.hasPermi('production:task:edit')")
    @Log(title = "任务节点", businessType = BusinessType.INSERT)
    @PostMapping("/batch/{taskId}")
    public AjaxResult batchCreate(@PathVariable String taskId, @RequestBody List<ProductionTaskNodeDto> nodeDtoList) {
        try {
            boolean result = taskNodeService.batchCreateNodes(taskId, nodeDtoList);
            return toAjax(result);
        } catch (Exception e) {
            log.error("批量创建节点失败，任务ID：{}", taskId, e);
            return error("批量创建失败：" + e.getMessage());
        }
    }

    /**
     * 复制节点到其他任务
     */
    @PreAuthorize("@ss.hasPermi('production:task:edit')")
    @Log(title = "任务节点", businessType = BusinessType.INSERT)
    @PostMapping("/copy/{sourceTaskId}/{targetTaskId}")
    public AjaxResult copyNodes(@PathVariable String sourceTaskId, @PathVariable String targetTaskId) {
        try {
            boolean result = taskNodeService.copyNodesToTask(sourceTaskId, targetTaskId);
            return toAjax(result);
        } catch (Exception e) {
            log.error("复制节点失败，源任务ID：{}，目标任务ID：{}", sourceTaskId, targetTaskId, e);
            return error("复制失败：" + e.getMessage());
        }
    }
}