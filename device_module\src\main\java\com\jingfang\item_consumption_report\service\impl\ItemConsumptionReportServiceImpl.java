package com.jingfang.item_consumption_report.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jingfang.item_consumption_report.mapper.ItemConsumptionReportMapper;
import com.jingfang.item_consumption_report.module.request.ItemConsumptionReportRequest;
import com.jingfang.item_consumption_report.module.vo.ItemConsumptionReportVo;
import com.jingfang.item_consumption_report.module.vo.ItemConsumptionStatisticsVo;
import com.jingfang.item_consumption_report.service.ItemConsumptionReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 库存消耗报表服务实现类
 */
@Slf4j
@Service
public class ItemConsumptionReportServiceImpl implements ItemConsumptionReportService {

    @Autowired
    private ItemConsumptionReportMapper consumptionReportMapper;

    @Override
    public IPage<ItemConsumptionReportVo> selectConsumptionReportList(ItemConsumptionReportRequest request) {
        try {
            log.info("查询库存消耗报表数据，请求参数：{}", request);

            // 创建分页对象
            Page<ItemConsumptionReportVo> page = new Page<>(request.getPageNum(), request.getPageSize());

            // 查询数据
            IPage<ItemConsumptionReportVo> result = consumptionReportMapper.selectConsumptionReportList(page, request);

            // 如果需要包含趋势分析，为每个物品添加趋势数据
            if (request.getIncludeTrendAnalysis() != null && request.getIncludeTrendAnalysis()) {
                for (ItemConsumptionReportVo item : result.getRecords()) {
                    List<ItemConsumptionReportVo.ConsumptionTrendVo> trendData =
                            selectConsumptionTrend(item.getItemId(), request.getStartDate(),
                                    request.getEndDate(), request.getTimeDimension());
                    item.setTrendData(trendData);
                }
            }

            // 如果需要包含类型分布，为每个物品添加类型分布数据
            if (request.getIncludeComparisonAnalysis() != null && request.getIncludeComparisonAnalysis()) {
                for (ItemConsumptionReportVo item : result.getRecords()) {
                    List<ItemConsumptionReportVo.ConsumptionTypeVo> typeDistribution =
                            selectConsumptionTypeDistribution(item.getItemId(), request.getStartDate(), request.getEndDate());
                    item.setTypeDistribution(typeDistribution);
                }
            }

            log.info("查询库存消耗报表数据成功，返回{}条记录", result.getRecords().size());
            return result;

        } catch (Exception e) {
            log.error("查询库存消耗报表数据失败", e);
            throw new RuntimeException("查询库存消耗报表数据失败：" + e.getMessage());
        }
    }

    @Override
    public ItemConsumptionStatisticsVo selectConsumptionStatistics(ItemConsumptionReportRequest request) {
        try {
            log.info("查询库存消耗统计数据，请求参数：{}", request);

            ItemConsumptionStatisticsVo statistics = new ItemConsumptionStatisticsVo();

            // 查询总体统计
            ItemConsumptionStatisticsVo.OverallStatistics overallStats =
                    consumptionReportMapper.selectOverallStatistics(request);
            statistics.setOverallStatistics(overallStats);

            // 查询消耗类型统计
            List<ItemConsumptionStatisticsVo.ConsumptionTypeStatistics> typeStats =
                    consumptionReportMapper.selectConsumptionTypeStatistics(request);
            statistics.setTypeStatistics(typeStats);

            // 查询效率等级统计
            List<ItemConsumptionStatisticsVo.EfficiencyLevelStatistics> efficiencyStats =
                    consumptionReportMapper.selectEfficiencyLevelStatistics(request);
            statistics.setEfficiencyStatistics(efficiencyStats);

            // 查询仓库统计
            List<ItemConsumptionStatisticsVo.WarehouseStatistics> warehouseStats =
                    consumptionReportMapper.selectWarehouseStatistics(request);
            statistics.setWarehouseStatistics(warehouseStats);

            // 查询部门统计
            List<ItemConsumptionStatisticsVo.DepartmentStatistics> departmentStats =
                    consumptionReportMapper.selectDepartmentStatistics(request);
            statistics.setDepartmentStatistics(departmentStats);

            // 生成图表数据
            Map<String, Object> chartData = generateChartData(request);
            statistics.setChartData(chartData);

            log.info("查询库存消耗统计数据成功");
            return statistics;

        } catch (Exception e) {
            log.error("查询库存消耗统计数据失败", e);
            throw new RuntimeException("查询库存消耗统计数据失败：" + e.getMessage());
        }
    }

    @Override
    public List<ItemConsumptionReportVo.ConsumptionTrendVo> selectConsumptionTrend(String itemId, Date startDate, Date endDate, Integer timeDimension) {
        try {
            log.info("查询消耗趋势数据，物品ID：{}，时间维度：{}", itemId, timeDimension);

            if (timeDimension == null) {
                timeDimension = 3; // 默认按月统计
            }

            List<ItemConsumptionReportVo.ConsumptionTrendVo> trendData =
                    consumptionReportMapper.selectConsumptionTrend(itemId, startDate, endDate, timeDimension);

            log.info("查询消耗趋势数据成功，返回{}条记录", trendData.size());
            return trendData;

        } catch (Exception e) {
            log.error("查询消耗趋势数据失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<ItemConsumptionReportVo.ConsumptionTypeVo> selectConsumptionTypeDistribution(String itemId, Date startDate, Date endDate) {
        try {
            log.info("查询消耗类型分布数据，物品ID：{}", itemId);

            List<ItemConsumptionReportVo.ConsumptionTypeVo> typeDistribution =
                    consumptionReportMapper.selectConsumptionTypeDistribution(itemId, startDate, endDate);

            log.info("查询消耗类型分布数据成功，返回{}条记录", typeDistribution.size());
            return typeDistribution;

        } catch (Exception e) {
            log.error("查询消耗类型分布数据失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<ItemConsumptionReportVo> selectEfficiencyRanking(ItemConsumptionReportRequest request, Integer limit) {
        try {
            log.info("查询消耗效率排行榜，限制数量：{}", limit);

            if (limit == null || limit <= 0) {
                limit = 10; // 默认返回前10名
            }

            List<ItemConsumptionReportVo> ranking =
                    consumptionReportMapper.selectEfficiencyRanking(request, limit);

            log.info("查询消耗效率排行榜成功，返回{}条记录", ranking.size());
            return ranking;

        } catch (Exception e) {
            log.error("查询消耗效率排行榜失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<ItemConsumptionReportVo> selectConsumptionRanking(ItemConsumptionReportRequest request, Integer limit) {
        try {
            log.info("查询消耗量排行榜，限制数量：{}", limit);

            if (limit == null || limit <= 0) {
                limit = 10; // 默认返回前10名
            }

            List<ItemConsumptionReportVo> ranking =
                    consumptionReportMapper.selectConsumptionRanking(request, limit);

            log.info("查询消耗量排行榜成功，返回{}条记录", ranking.size());
            return ranking;

        } catch (Exception e) {
            log.error("查询消耗量排行榜失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public int generateConsumptionSummary(Date startDate, Date endDate) {
        try {
            log.info("生成消耗汇总数据，时间范围：{} - {}", startDate, endDate);

            int count = consumptionReportMapper.generateConsumptionSummary(startDate, endDate);

            log.info("生成消耗汇总数据成功，生成{}条记录", count);
            return count;

        } catch (Exception e) {
            log.error("生成消耗汇总数据失败", e);
            throw new RuntimeException("生成消耗汇总数据失败：" + e.getMessage());
        }
    }

    @Override
    public int updateEfficiencyAnalysis(String itemId, String analysisPeriod, Date startDate, Date endDate) {
        try {
            log.info("更新效率分析数据，物品ID：{}，分析周期：{}", itemId, analysisPeriod);

            int count = consumptionReportMapper.updateEfficiencyAnalysis(itemId, analysisPeriod, startDate, endDate);

            log.info("更新效率分析数据成功，更新{}条记录", count);
            return count;

        } catch (Exception e) {
            log.error("更新效率分析数据失败", e);
            throw new RuntimeException("更新效率分析数据失败：" + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> generateChartData(ItemConsumptionReportRequest request) {
        try {
            log.info("生成图表数据，请求参数：{}", request);

            Map<String, Object> chartData = new HashMap<>();

            // 消耗趋势图数据
            List<Map<String, Object>> trendChartData = consumptionReportMapper.selectChartData("trend", request);
            chartData.put("trendChart", formatTrendChartData(trendChartData));

            // 消耗类型分布饼图数据
            List<Map<String, Object>> typeChartData = consumptionReportMapper.selectChartData("type", request);
            chartData.put("typeChart", formatPieChartData(typeChartData, "consumptionTypeName", "amount"));

            // 效率等级分布饼图数据
            List<Map<String, Object>> efficiencyChartData = consumptionReportMapper.selectChartData("efficiency", request);
            chartData.put("efficiencyChart", formatPieChartData(efficiencyChartData, "efficiencyLevelName", "itemCount"));

            // 仓库消耗对比柱状图数据
            List<Map<String, Object>> warehouseChartData = consumptionReportMapper.selectChartData("warehouse", request);
            chartData.put("warehouseChart", formatBarChartData(warehouseChartData, "warehouseName", "consumption"));

            // 部门消耗对比柱状图数据
            List<Map<String, Object>> departmentChartData = consumptionReportMapper.selectChartData("department", request);
            chartData.put("departmentChart", formatBarChartData(departmentChartData, "deptName", "consumption"));

            log.info("生成图表数据成功");
            return chartData;

        } catch (Exception e) {
            log.error("生成图表数据失败", e);
            return new HashMap<>();
        }
    }

    @Override
    public Map<String, Object> generateConsumptionAnalysisReport(ItemConsumptionReportRequest request) {
        try {
            log.info("生成消耗分析报告，请求参数：{}", request);

            Map<String, Object> report = new HashMap<>();

            // 基础统计数据
            ItemConsumptionStatisticsVo statistics = selectConsumptionStatistics(request);
            report.put("statistics", statistics);

            // 效率排行榜
            List<ItemConsumptionReportVo> efficiencyRanking = selectEfficiencyRanking(request, 10);
            report.put("efficiencyRanking", efficiencyRanking);

            // 消耗量排行榜
            List<ItemConsumptionReportVo> consumptionRanking = selectConsumptionRanking(request, 10);
            report.put("consumptionRanking", consumptionRanking);

            // 预警数据
            List<ItemConsumptionReportVo> alerts = getConsumptionAlerts(request);
            report.put("alerts", alerts);

            // 分析结论
            Map<String, Object> analysis = generateAnalysisConclusions(statistics);
            report.put("analysis", analysis);

            log.info("生成消耗分析报告成功");
            return report;

        } catch (Exception e) {
            log.error("生成消耗分析报告失败", e);
            return new HashMap<>();
        }
    }

    @Override
    public List<ItemConsumptionReportVo> exportConsumptionReport(ItemConsumptionReportRequest request) {
        try {
            log.info("导出消耗报表数据，请求参数：{}", request);

            // 设置大页面大小以获取所有数据
            request.setPageNum(1);
            request.setPageSize(10000);

            IPage<ItemConsumptionReportVo> result = selectConsumptionReportList(request);

            log.info("导出消耗报表数据成功，导出{}条记录", result.getRecords().size());
            return result.getRecords();

        } catch (Exception e) {
            log.error("导出消耗报表数据失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public boolean batchUpdateConsumptionSummary(Date startDate, Date endDate) {
        try {
            log.info("批量更新消耗汇总数据，时间范围：{} - {}", startDate, endDate);

            int count = generateConsumptionSummary(startDate, endDate);

            log.info("批量更新消耗汇总数据成功，更新{}条记录", count);
            return count > 0;

        } catch (Exception e) {
            log.error("批量更新消耗汇总数据失败", e);
            return false;
        }
    }

    @Override
    public boolean batchCalculateEfficiencyAnalysis(String analysisPeriod, Date startDate, Date endDate) {
        try {
            log.info("批量计算效率分析数据，分析周期：{}，时间范围：{} - {}", analysisPeriod, startDate, endDate);

            // 这里可以实现批量计算逻辑
            // 例如：查询所有物品，然后逐个计算效率分析
            int totalCount = 0;

            log.info("批量计算效率分析数据成功，计算{}条记录", totalCount);
            return true;

        } catch (Exception e) {
            log.error("批量计算效率分析数据失败", e);
            return false;
        }
    }

    @Override
    public List<ItemConsumptionReportVo> getConsumptionAlerts(ItemConsumptionReportRequest request) {
        try {
            log.info("获取消耗预警数据，请求参数：{}", request);

            // 设置预警条件：效率评分低于60分或消耗量异常高
            request.setMaxEfficiencyScore(60.0);
            request.setOrderBy("efficiency_score");
            request.setOrderDirection("asc");

            IPage<ItemConsumptionReportVo> result = selectConsumptionReportList(request);

            log.info("获取消耗预警数据成功，返回{}条记录", result.getRecords().size());
            return result.getRecords();

        } catch (Exception e) {
            log.error("获取消耗预警数据失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public String getEfficiencyOptimizationSuggestions(String itemId, String analysisPeriod) {
        try {
            log.info("获取效率优化建议，物品ID：{}，分析周期：{}", itemId, analysisPeriod);

            // 这里可以实现基于AI或规则的优化建议生成逻辑
            StringBuilder suggestions = new StringBuilder();

            // 基础建议模板
            suggestions.append("基于当前消耗数据分析，建议：\n");
            suggestions.append("1. 定期检查库存消耗模式，识别异常消耗\n");
            suggestions.append("2. 优化采购计划，减少库存积压\n");
            suggestions.append("3. 加强消耗品使用培训，提高使用效率\n");
            suggestions.append("4. 建立消耗品使用标准，规范使用流程\n");

            String result = suggestions.toString();
            log.info("获取效率优化建议成功");
            return result;

        } catch (Exception e) {
            log.error("获取效率优化建议失败", e);
            return "暂无优化建议";
        }
    }

    /**
     * 格式化趋势图表数据
     */
    private Map<String, Object> formatTrendChartData(List<Map<String, Object>> data) {
        Map<String, Object> chartData = new HashMap<>();
        List<String> labels = new ArrayList<>();
        List<BigDecimal> consumptionData = new ArrayList<>();
        List<BigDecimal> amountData = new ArrayList<>();
        List<BigDecimal> efficiencyData = new ArrayList<>();

        for (Map<String, Object> item : data) {
            labels.add(String.valueOf(item.get("dateLabel")));
            consumptionData.add((BigDecimal) item.get("consumption"));
            amountData.add((BigDecimal) item.get("amount"));
            efficiencyData.add((BigDecimal) item.get("efficiencyScore"));
        }

        chartData.put("labels", labels);
        chartData.put("datasets", Arrays.asList(
            Map.of("label", "消耗数量", "data", consumptionData, "borderColor", "#409EFF"),
            Map.of("label", "消耗金额", "data", amountData, "borderColor", "#67C23A"),
            Map.of("label", "效率评分", "data", efficiencyData, "borderColor", "#E6A23C")
        ));

        return chartData;
    }

    /**
     * 格式化饼图数据
     */
    private Map<String, Object> formatPieChartData(List<Map<String, Object>> data, String labelField, String valueField) {
        Map<String, Object> chartData = new HashMap<>();
        List<String> labels = new ArrayList<>();
        List<Object> values = new ArrayList<>();
        List<String> colors = Arrays.asList("#409EFF", "#67C23A", "#E6A23C", "#F56C6C", "#909399");

        for (int i = 0; i < data.size(); i++) {
            Map<String, Object> item = data.get(i);
            labels.add(String.valueOf(item.get(labelField)));
            values.add(item.get(valueField));
        }

        chartData.put("labels", labels);
        chartData.put("datasets", Arrays.asList(
            Map.of("data", values, "backgroundColor", colors.subList(0, Math.min(colors.size(), data.size())))
        ));

        return chartData;
    }

    /**
     * 格式化柱状图数据
     */
    private Map<String, Object> formatBarChartData(List<Map<String, Object>> data, String labelField, String valueField) {
        Map<String, Object> chartData = new HashMap<>();
        List<String> labels = new ArrayList<>();
        List<Object> values = new ArrayList<>();

        for (Map<String, Object> item : data) {
            labels.add(String.valueOf(item.get(labelField)));
            values.add(item.get(valueField));
        }

        chartData.put("labels", labels);
        chartData.put("datasets", Arrays.asList(
            Map.of("label", "消耗量", "data", values, "backgroundColor", "#409EFF")
        ));

        return chartData;
    }

    /**
     * 生成分析结论
     */
    private Map<String, Object> generateAnalysisConclusions(ItemConsumptionStatisticsVo statistics) {
        Map<String, Object> analysis = new HashMap<>();

        if (statistics.getOverallStatistics() != null) {
            ItemConsumptionStatisticsVo.OverallStatistics overall = statistics.getOverallStatistics();

            // 总体评价
            String overallAssessment = "良好";
            if (overall.getAvgEfficiencyScore() != null) {
                BigDecimal avgScore = overall.getAvgEfficiencyScore();
                if (avgScore.compareTo(new BigDecimal("80")) >= 0) {
                    overallAssessment = "优秀";
                } else if (avgScore.compareTo(new BigDecimal("60")) < 0) {
                    overallAssessment = "需要改进";
                }
            }
            analysis.put("overallAssessment", overallAssessment);

            // 主要发现
            List<String> keyFindings = new ArrayList<>();
            keyFindings.add("总消耗量：" + overall.getTotalConsumption() + "，涉及" + overall.getItemCount() + "种物品");
            keyFindings.add("平均效率评分：" + overall.getAvgEfficiencyScore() + "分");
            if (overall.getTotalSavings() != null && overall.getTotalSavings().compareTo(BigDecimal.ZERO) > 0) {
                keyFindings.add("累计节约金额：" + overall.getTotalSavings() + "元");
            }
            analysis.put("keyFindings", keyFindings);

            // 改进建议
            List<String> recommendations = new ArrayList<>();
            if (overall.getAvgEfficiencyScore() != null && overall.getAvgEfficiencyScore().compareTo(new BigDecimal("70")) < 0) {
                recommendations.add("建议加强消耗品使用培训，提高整体使用效率");
            }
            recommendations.add("建议定期分析消耗模式，识别优化机会");
            recommendations.add("建议建立消耗品使用标准和规范");
            analysis.put("recommendations", recommendations);
        }

        return analysis;
    }
}