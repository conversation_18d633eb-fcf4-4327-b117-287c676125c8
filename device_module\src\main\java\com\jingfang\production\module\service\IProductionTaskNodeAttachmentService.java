package com.jingfang.production.module.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jingfang.production.module.dto.ProductionTaskNodeAttachmentDto;
import com.jingfang.production.module.entity.ProductionTaskNodeAttachment;
import com.jingfang.production.module.vo.ProductionTaskNodeAttachmentVo;

import java.util.List;

/**
 * 任务节点附件Service接口
 */
public interface IProductionTaskNodeAttachmentService extends IService<ProductionTaskNodeAttachment> {
    
    /**
     * 根据节点ID查询附件列表
     * 
     * @param nodeId 节点ID
     * @return 附件列表
     */
    List<ProductionTaskNodeAttachmentVo> selectAttachmentsByNodeId(String nodeId);
    
    /**
     * 根据任务ID查询所有附件
     * 
     * @param taskId 任务ID
     * @return 附件列表
     */
    List<ProductionTaskNodeAttachmentVo> selectAttachmentsByTaskId(String taskId);
    
    /**
     * 根据附件ID查询附件详情
     * 
     * @param attachmentId 附件ID
     * @return 附件详情
     */
    ProductionTaskNodeAttachmentVo selectAttachmentById(String attachmentId);
    
    /**
     * 新增节点附件
     * 
     * @param attachmentDto 附件信息
     * @param uploadUserId 上传人ID
     * @return 结果
     */
    boolean insertNodeAttachment(ProductionTaskNodeAttachmentDto attachmentDto, Long uploadUserId);
    
    /**
     * 修改节点附件
     * 
     * @param attachmentDto 附件信息
     * @return 结果
     */
    boolean updateNodeAttachment(ProductionTaskNodeAttachmentDto attachmentDto);
    
    /**
     * 删除节点附件
     * 
     * @param attachmentId 附件ID
     * @return 结果
     */
    boolean deleteNodeAttachment(String attachmentId);
    
    /**
     * 批量删除节点附件
     * 
     * @param attachmentIds 附件ID列表
     * @return 结果
     */
    boolean deleteNodeAttachments(List<String> attachmentIds);
    
    /**
     * 根据节点ID删除所有附件
     * 
     * @param nodeId 节点ID
     * @return 结果
     */
    boolean deleteAttachmentsByNodeId(String nodeId);
    
    /**
     * 根据任务ID删除所有附件
     * 
     * @param taskId 任务ID
     * @return 结果
     */
    boolean deleteAttachmentsByTaskId(String taskId);
    
    /**
     * 统计节点的附件数量
     * 
     * @param nodeId 节点ID
     * @return 附件数量
     */
    int countAttachmentsByNodeId(String nodeId);
    
    /**
     * 统计任务的附件数量
     * 
     * @param taskId 任务ID
     * @return 附件数量
     */
    int countAttachmentsByTaskId(String taskId);
    
    /**
     * 批量上传附件
     * 
     * @param nodeId 节点ID
     * @param attachmentDtoList 附件信息列表
     * @param uploadUserId 上传人ID
     * @return 结果
     */
    boolean batchUploadAttachments(String nodeId, List<ProductionTaskNodeAttachmentDto> attachmentDtoList, Long uploadUserId);
    
    /**
     * 下载附件
     * 
     * @param attachmentId 附件ID
     * @return 附件文件路径
     */
    String downloadAttachment(String attachmentId);
    
    /**
     * 检查附件是否可以删除
     * 
     * @param attachmentId 附件ID
     * @param userId 用户ID
     * @return 是否可以删除
     */
    boolean canDeleteAttachment(String attachmentId, Long userId);
}
