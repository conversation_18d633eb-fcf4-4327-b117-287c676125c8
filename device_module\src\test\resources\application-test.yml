# 测试环境配置
spring:
  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ************************************************************************************************************************************************************
    username: root
    password: 123456
    
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        
  # Redis配置（测试环境使用内存数据库）
  data:
    redis:
      host: localhost
      port: 6379
      database: 1
      password: 
      timeout: 10s
      
  # 事务配置
  transaction:
    rollback-on-commit-failure: true
    
# MyBatis-Plus配置
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      logic-delete-field: delFlag
      logic-delete-value: 2
      logic-not-delete-value: 0
      
# 日志配置
logging:
  level:
    com.jingfang: debug
    org.springframework.security: debug
    org.springframework.web: debug
    org.mybatis: debug
    
# 测试专用配置
test:
  # 测试数据配置
  data:
    cleanup: true
    auto-rollback: true
    
  # 性能测试配置
  performance:
    enabled: false
    max-response-time: 1000
    
  # Mock配置
  mock:
    enabled: true
    external-services: true
