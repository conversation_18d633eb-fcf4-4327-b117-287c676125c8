package com.jingfang.production.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jingfang.production.module.entity.ProductionTaskInventory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 生产任务库存关联Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-16
 */
@Mapper
public interface ProductionTaskInventoryMapper extends BaseMapper<ProductionTaskInventory> {

    /**
     * 根据任务ID查询关联的库存列表
     *
     * @param taskId 任务ID
     * @return 库存关联列表
     */
    List<ProductionTaskInventory> selectInventoriesByTaskId(@Param("taskId") String taskId);

    /**
     * 批量插入任务库存关联
     *
     * @param inventoryList 库存关联列表
     * @return 插入结果
     */
    int batchInsertTaskInventories(@Param("inventoryList") List<ProductionTaskInventory> inventoryList);

    /**
     * 根据任务ID删除所有库存关联
     *
     * @param taskId 任务ID
     * @return 删除结果
     */
    int deleteInventoriesByTaskId(@Param("taskId") String taskId);
}