# 故障申报功能完整测试脚本
# 使用PowerShell进行API测试

param(
    [string]$BaseUrl = "http://localhost:8080",
    [string]$Token = "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiYWY1MjdjY2UtMmIwMy00N2NjLWE1N2ItZDhkZTQ5YzIyY2YwIn0.8orGj_5UWKg6HuKPk9bQSBJsS0c_H25VAdEh_htY9YmxEsUvfrKLmBtt_1YRHulHiry6UH7dVtqaopeVPa1fow"
)

Write-Host "=== 故障申报功能完整测试 ===" -ForegroundColor Green
Write-Host "测试服务器: $BaseUrl" -ForegroundColor Yellow
Write-Host ""

# 设置请求头
$headers = @{
    'Authorization' = "Bearer $Token"
    'Content-Type' = 'application/json'
}

# 测试计数器
$testCount = 0
$passCount = 0

function Test-API {
    param(
        [string]$TestName,
        [string]$Method,
        [string]$Url,
        [hashtable]$Headers,
        [string]$Body = $null
    )
    
    $global:testCount++
    Write-Host "[$global:testCount] 测试: $TestName" -ForegroundColor Cyan
    
    try {
        if ($Body) {
            $response = Invoke-WebRequest -Uri $Url -Method $Method -Headers $Headers -Body $Body
        } else {
            $response = Invoke-WebRequest -Uri $Url -Method $Method -Headers $Headers
        }
        
        if ($response.StatusCode -eq 200) {
            $content = $response.Content | ConvertFrom-Json
            if ($content.code -eq 200) {
                Write-Host "  ✓ 通过: $($content.msg)" -ForegroundColor Green
                $global:passCount++
                return $content.data
            } else {
                Write-Host "  ✗ 失败: $($content.msg)" -ForegroundColor Red
                return $null
            }
        } else {
            Write-Host "  ✗ HTTP错误: $($response.StatusCode)" -ForegroundColor Red
            return $null
        }
    }
    catch {
        Write-Host "  ✗ 异常: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# 1. 基础功能检查
Write-Host "`n=== 1. 基础功能检查 ===" -ForegroundColor Yellow
$checkResult = Test-API -TestName "故障申报功能检查" -Method "GET" -Url "$BaseUrl/maintenance/fault/test/check" -Headers $headers

if ($checkResult) {
    Write-Host "  故障类型数量: $($checkResult.faultTypes.Count)" -ForegroundColor Gray
    Write-Host "  紧急程度数量: $($checkResult.urgencyLevels.Count)" -ForegroundColor Gray
    Write-Host "  服务状态: $($checkResult.serviceStatus)" -ForegroundColor Gray
}

# 2. 获取选项数据
Write-Host "`n=== 2. 获取选项数据 ===" -ForegroundColor Yellow
$faultTypes = Test-API -TestName "获取故障类型选项" -Method "GET" -Url "$BaseUrl/maintenance/fault/types" -Headers $headers
$urgencyLevels = Test-API -TestName "获取紧急程度选项" -Method "GET" -Url "$BaseUrl/maintenance/fault/urgency-levels" -Headers $headers

# 3. 测试故障申报提交
Write-Host "`n=== 3. 故障申报提交测试 ===" -ForegroundColor Yellow

# 测试数据
$faultReportData = @{
    assetId = "AS20230001"
    assetName = "中央空调主机"
    faultTitle = "PowerShell测试故障申报"
    faultDescription = "这是通过PowerShell脚本提交的测试故障申报，用于验证接口功能"
    faultType = 2
    urgencyLevel = 3
    reportLocation = "测试位置-PowerShell"
    contactPhone = "13800138000"
    images = @("test1.jpg", "test2.jpg")
    remark = "PowerShell自动化测试"
} | ConvertTo-Json -Depth 3

$submitResult = Test-API -TestName "提交故障申报" -Method "POST" -Url "$BaseUrl/maintenance/fault/report" -Headers $headers -Body $faultReportData

if ($submitResult) {
    Write-Host "  生成的故障ID: $($submitResult.faultId)" -ForegroundColor Gray
    Write-Host "  生成的任务ID: $($submitResult.taskId)" -ForegroundColor Gray
    $faultId = $submitResult.faultId
}

# 4. 查询功能测试
Write-Host "`n=== 4. 查询功能测试 ===" -ForegroundColor Yellow

# 查询我的故障申报
$myReports = Test-API -TestName "查询我的故障申报" -Method "GET" -Url "$BaseUrl/maintenance/fault/my-reports" -Headers $headers

if ($myReports -and $myReports.records) {
    Write-Host "  我的故障申报总数: $($myReports.total)" -ForegroundColor Gray
    Write-Host "  当前页记录数: $($myReports.records.Count)" -ForegroundColor Gray
}

# 查询故障申报详情（如果有故障ID）
if ($faultId) {
    $faultDetail = Test-API -TestName "查询故障申报详情" -Method "GET" -Url "$BaseUrl/maintenance/fault/$faultId" -Headers $headers
    
    if ($faultDetail) {
        Write-Host "  故障标题: $($faultDetail.faultTitle)" -ForegroundColor Gray
        Write-Host "  故障状态: $($faultDetail.statusName)" -ForegroundColor Gray
        Write-Host "  申报时间: $($faultDetail.reportTime)" -ForegroundColor Gray
    }
}

# 5. 统计信息测试
Write-Host "`n=== 5. 统计信息测试 ===" -ForegroundColor Yellow
$statistics = Test-API -TestName "获取故障申报统计" -Method "GET" -Url "$BaseUrl/maintenance/fault/statistics" -Headers $headers

if ($statistics) {
    Write-Host "  已提交: $($statistics.submitted)" -ForegroundColor Gray
    Write-Host "  已受理: $($statistics.accepted)" -ForegroundColor Gray
    Write-Host "  处理中: $($statistics.processing)" -ForegroundColor Gray
    Write-Host "  已完成: $($statistics.completed)" -ForegroundColor Gray
    Write-Host "  已关闭: $($statistics.closed)" -ForegroundColor Gray
}

# 6. 测试控制器功能
Write-Host "`n=== 6. 测试控制器功能 ===" -ForegroundColor Yellow
$testSubmit = Test-API -TestName "测试控制器-提交故障申报" -Method "POST" -Url "$BaseUrl/maintenance/fault/test/submit" -Headers $headers
$testMyReports = Test-API -TestName "测试控制器-查询我的申报" -Method "GET" -Url "$BaseUrl/maintenance/fault/test/my-reports" -Headers $headers
$testFaultTypes = Test-API -TestName "测试控制器-故障类型" -Method "GET" -Url "$BaseUrl/maintenance/fault/test/fault-types" -Headers $headers
$testUrgencyLevels = Test-API -TestName "测试控制器-紧急程度" -Method "GET" -Url "$BaseUrl/maintenance/fault/test/urgency-levels" -Headers $headers

# 7. 分页查询测试
Write-Host "`n=== 7. 分页查询测试 ===" -ForegroundColor Yellow
$pageTest1 = Test-API -TestName "分页查询-第1页" -Method "GET" -Url "$BaseUrl/maintenance/fault/my-reports?pageNum=1&pageSize=5" -Headers $headers
$pageTest2 = Test-API -TestName "分页查询-第2页" -Method "GET" -Url "$BaseUrl/maintenance/fault/my-reports?pageNum=2&pageSize=5" -Headers $headers

# 8. 状态筛选测试
Write-Host "`n=== 8. 状态筛选测试 ===" -ForegroundColor Yellow
$statusTest1 = Test-API -TestName "筛选已提交状态" -Method "GET" -Url "$BaseUrl/maintenance/fault/my-reports?status=1" -Headers $headers
$statusTest2 = Test-API -TestName "筛选处理中状态" -Method "GET" -Url "$BaseUrl/maintenance/fault/my-reports?status=3" -Headers $headers

# 测试结果汇总
Write-Host "`n=== 测试结果汇总 ===" -ForegroundColor Green
Write-Host "总测试数: $testCount" -ForegroundColor White
Write-Host "通过数: $passCount" -ForegroundColor Green
Write-Host "失败数: $($testCount - $passCount)" -ForegroundColor Red
Write-Host "通过率: $([math]::Round($passCount / $testCount * 100, 2))%" -ForegroundColor Yellow

if ($passCount -eq $testCount) {
    Write-Host "`n🎉 所有测试通过！故障申报功能正常工作。" -ForegroundColor Green
} elseif ($passCount -gt 0) {
    Write-Host "`n⚠️  部分测试通过，请检查失败的测试项。" -ForegroundColor Yellow
} else {
    Write-Host "`n❌ 所有测试失败，请检查服务状态和配置。" -ForegroundColor Red
}

Write-Host "`n测试完成！" -ForegroundColor Cyan
