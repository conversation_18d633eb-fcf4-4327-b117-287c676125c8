package com.jingfang.production.module.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 任务节点附件DTO
 */
@Data
public class ProductionTaskNodeAttachmentDto implements Serializable {
    
    /**
     * 附件ID
     */
    private String attachmentId;
    
    /**
     * 节点ID
     */
    @NotBlank(message = "节点ID不能为空")
    private String nodeId;
    
    /**
     * 文件名
     */
    @NotBlank(message = "文件名不能为空")
    private String fileName;
    
    /**
     * 文件类型
     */
    private String fileType;
    
    /**
     * 文件大小(字节)
     */
    @NotNull(message = "文件大小不能为空")
    private Long fileSize;
    
    /**
     * 文件存储路径
     */
    @NotBlank(message = "文件存储路径不能为空")
    private String filePath;
    
    /**
     * 上传人ID
     */
    private Long uploadUserId;
    
    /**
     * 备注
     */
    private String remark;
    
    private static final long serialVersionUID = 1L;
}
