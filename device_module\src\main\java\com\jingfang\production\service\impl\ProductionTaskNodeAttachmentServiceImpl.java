package com.jingfang.production.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jingfang.common.utils.uuid.IdUtils;
import com.jingfang.production.dto.ProductionTaskNodeAttachmentDto;
import com.jingfang.production.mapper.ProductionTaskNodeAttachmentMapper;
import com.jingfang.production.module.entity.ProductionTaskNodeAttachment;
import com.jingfang.production.service.ProductionTaskNodeAttachmentService;
import com.jingfang.production.vo.ProductionTaskNodeAttachmentVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Date;
import java.util.List;

/**
 * 任务节点附件Service实现类
 *
 * <AUTHOR>
 * @date 2025-01-16
 */
@Slf4j
@Service
public class ProductionTaskNodeAttachmentServiceImpl extends ServiceImpl<ProductionTaskNodeAttachmentMapper, ProductionTaskNodeAttachment>
        implements ProductionTaskNodeAttachmentService {

    @Autowired
    private ProductionTaskNodeAttachmentMapper attachmentMapper;

    @Value("${file.upload.path:/uploads/production/attachments/}")
    private String uploadPath;

    @Value("${file.download.base-url:http://localhost:8080}")
    private String baseUrl;

    @Override
    @Transactional(readOnly = true)
    public List<ProductionTaskNodeAttachmentVo> selectAttachmentsByNodeId(String nodeId) {
        return attachmentMapper.selectAttachmentsByNodeId(nodeId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<ProductionTaskNodeAttachmentVo> selectAttachmentsByTaskId(String taskId) {
        return attachmentMapper.selectAttachmentsByTaskId(taskId);
    }

    @Override
    @Transactional(readOnly = true)
    public ProductionTaskNodeAttachmentVo selectAttachmentById(String attachmentId) {
        return attachmentMapper.selectAttachmentById(attachmentId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean uploadAttachment(String nodeId, MultipartFile file, Long uploadUserId, String remark) {
        try {
            if (file.isEmpty()) {
                throw new RuntimeException("上传文件不能为空");
            }

            // 生成文件名
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || originalFilename.trim().isEmpty()) {
                throw new RuntimeException("文件名不能为空");
            }
            String fileName = System.currentTimeMillis() + "_" + originalFilename;

            // 创建上传目录
            Path uploadDir = Paths.get(uploadPath);
            if (!Files.exists(uploadDir)) {
                Files.createDirectories(uploadDir);
            }

            // 保存文件
            Path filePath = uploadDir.resolve(fileName);
            Files.copy(file.getInputStream(), filePath);

            // 保存附件记录
            ProductionTaskNodeAttachment attachment = new ProductionTaskNodeAttachment();
            attachment.setAttachmentId(IdUtils.fastSimpleUUID());
            attachment.setNodeId(nodeId);
            attachment.setFileName(originalFilename);
            attachment.setFileType(file.getContentType());
            attachment.setFileSize(file.getSize());
            attachment.setFilePath(filePath.toString());
            attachment.setUploadUserId(uploadUserId);
            attachment.setUploadTime(new Date());
            attachment.setRemark(remark);

            boolean result = this.save(attachment);
            log.info("上传附件成功，节点ID：{}，文件名：{}", nodeId, originalFilename);
            return result;

        } catch (IOException e) {
            log.error("上传附件失败，节点ID：{}", nodeId, e);
            throw new RuntimeException("上传附件失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertAttachment(ProductionTaskNodeAttachmentDto attachmentDto) {
        try {
            ProductionTaskNodeAttachment attachment = new ProductionTaskNodeAttachment();
            BeanUtils.copyProperties(attachmentDto, attachment);
            attachment.setAttachmentId(IdUtils.fastSimpleUUID());
            attachment.setUploadTime(new Date());

            boolean result = this.save(attachment);
            log.info("新增附件记录成功，附件ID：{}", attachment.getAttachmentId());
            return result;
        } catch (Exception e) {
            log.error("新增附件记录失败", e);
            throw new RuntimeException("新增附件记录失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAttachment(ProductionTaskNodeAttachmentDto attachmentDto) {
        try {
            ProductionTaskNodeAttachment attachment = new ProductionTaskNodeAttachment();
            BeanUtils.copyProperties(attachmentDto, attachment);

            boolean result = this.updateById(attachment);
            log.info("修改附件信息成功，附件ID：{}", attachment.getAttachmentId());
            return result;
        } catch (Exception e) {
            log.error("修改附件信息失败，附件ID：{}", attachmentDto.getAttachmentId(), e);
            throw new RuntimeException("修改附件信息失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteAttachment(String attachmentId) {
        try {
            // 获取附件信息
            ProductionTaskNodeAttachment attachment = this.getById(attachmentId);
            if (attachment == null) {
                throw new RuntimeException("附件不存在");
            }

            // 删除物理文件
            try {
                Path filePath = Paths.get(attachment.getFilePath());
                if (Files.exists(filePath)) {
                    Files.delete(filePath);
                }
            } catch (IOException e) {
                log.warn("删除物理文件失败，文件路径：{}", attachment.getFilePath(), e);
            }

            // 删除数据库记录
            boolean result = this.removeById(attachmentId);
            log.info("删除附件成功，附件ID：{}", attachmentId);
            return result;
        } catch (Exception e) {
            log.error("删除附件失败，附件ID：{}", attachmentId, e);
            throw new RuntimeException("删除附件失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteAttachments(List<String> attachmentIds) {
        try {
            for (String attachmentId : attachmentIds) {
                deleteAttachment(attachmentId);
            }
            return true;
        } catch (Exception e) {
            log.error("批量删除附件失败", e);
            throw new RuntimeException("批量删除附件失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(readOnly = true)
    public byte[] downloadAttachment(String attachmentId) {
        try {
            ProductionTaskNodeAttachment attachment = this.getById(attachmentId);
            if (attachment == null) {
                throw new RuntimeException("附件不存在");
            }

            Path filePath = Paths.get(attachment.getFilePath());
            if (!Files.exists(filePath)) {
                throw new RuntimeException("文件不存在");
            }

            byte[] fileBytes = Files.readAllBytes(filePath);
            log.info("下载附件成功，附件ID：{}，文件大小：{} bytes", attachmentId, fileBytes.length);
            return fileBytes;
        } catch (IOException e) {
            log.error("下载附件失败，附件ID：{}", attachmentId, e);
            throw new RuntimeException("下载附件失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(readOnly = true)
    public String getDownloadUrl(String attachmentId) {
        try {
            ProductionTaskNodeAttachment attachment = this.getById(attachmentId);
            if (attachment == null) {
                throw new RuntimeException("附件不存在");
            }

            String downloadUrl = baseUrl + "/production/attachment/download/" + attachmentId;
            log.debug("生成下载链接，附件ID：{}，链接：{}", attachmentId, downloadUrl);
            return downloadUrl;
        } catch (Exception e) {
            log.error("生成下载链接失败，附件ID：{}", attachmentId, e);
            throw new RuntimeException("生成下载链接失败：" + e.getMessage());
        }
    }
}