package com.jingfang.production.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jingfang.production.mapper.ProductionTaskNodeAttachmentMapper;
import com.jingfang.production.module.entity.ProductionTaskNodeAttachment;
import com.jingfang.production.service.ProductionTaskNodeAttachmentService;
import org.springframework.stereotype.Service;

/**
 * 任务节点附件Service实现类
 *
 * <AUTHOR>
 * @date 2025-01-16
 */
@Service
public class ProductionTaskNodeAttachmentServiceImpl extends ServiceImpl<ProductionTaskNodeAttachmentMapper, ProductionTaskNodeAttachment>
        implements ProductionTaskNodeAttachmentService {

}