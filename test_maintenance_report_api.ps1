# 维护报表功能API测试脚本

$baseUrl = "http://localhost:8080"
$token = "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiOGExYmRkOWEtZmFjNy00MDYzLThhNmQtY2RhMDAzOTM4YmM4In0.0Qu0IP7T3-nGp3O8qBm2q5eHvTlloOEZGEFmFACsOOIAfGC3CzTXa_-RAEd2m6wKBaCqftBgknOLRIsHXWIK1w"

$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

Write-Host "=== 维护报表功能API测试 ===" -ForegroundColor Green
Write-Host ""

# 测试1: 获取报表配置选项
Write-Host "测试1: 获取报表配置选项" -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/maintenance/report/config-options" -Method GET -Headers $headers
    Write-Host "✅ 成功获取配置选项" -ForegroundColor Green
    Write-Host ($response | ConvertTo-Json -Depth 3)
} catch {
    Write-Host "❌ 失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "状态码: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
}
Write-Host ""

# 测试2: 获取维护任务完成率统计
Write-Host "测试2: 获取维护任务完成率统计" -ForegroundColor Yellow
try {
    $startDate = (Get-Date).AddDays(-30).ToString("yyyy-MM-dd")
    $endDate = (Get-Date).ToString("yyyy-MM-dd")
    $url = "$baseUrl/maintenance/report/task-completion?startDate=$startDate&endDate=$endDate"
    
    $response = Invoke-RestMethod -Uri $url -Method GET -Headers $headers
    Write-Host "✅ 成功获取任务完成率统计" -ForegroundColor Green
    Write-Host ($response | ConvertTo-Json -Depth 3)
} catch {
    Write-Host "❌ 失败: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "状态码: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}
Write-Host ""

# 测试3: 获取故障响应时间统计
Write-Host "测试3: 获取故障响应时间统计" -ForegroundColor Yellow
try {
    $startDate = (Get-Date).AddDays(-30).ToString("yyyy-MM-dd")
    $endDate = (Get-Date).ToString("yyyy-MM-dd")
    $url = "$baseUrl/maintenance/report/fault-response?startDate=$startDate&endDate=$endDate"
    
    $response = Invoke-RestMethod -Uri $url -Method GET -Headers $headers
    Write-Host "✅ 成功获取故障响应时间统计" -ForegroundColor Green
    Write-Host ($response | ConvertTo-Json -Depth 3)
} catch {
    Write-Host "❌ 失败: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "状态码: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}
Write-Host ""

# 测试4: 获取维护成本统计
Write-Host "测试4: 获取维护成本统计" -ForegroundColor Yellow
try {
    $startDate = (Get-Date).AddDays(-30).ToString("yyyy-MM-dd")
    $endDate = (Get-Date).ToString("yyyy-MM-dd")
    $url = "$baseUrl/maintenance/report/maintenance-cost?startDate=$startDate&endDate=$endDate"
    
    $response = Invoke-RestMethod -Uri $url -Method GET -Headers $headers
    Write-Host "✅ 成功获取维护成本统计" -ForegroundColor Green
    Write-Host ($response | ConvertTo-Json -Depth 3)
} catch {
    Write-Host "❌ 失败: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "状态码: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}
Write-Host ""

# 测试5: 生成图表数据
Write-Host "测试5: 生成图表数据" -ForegroundColor Yellow
try {
    $requestBody = @{
        reportType = 1
        startDate = (Get-Date).AddDays(-30).ToString("yyyy-MM-dd")
        endDate = (Get-Date).ToString("yyyy-MM-dd")
        timeDimension = 3
        filters = @{}
    } | ConvertTo-Json
    
    $response = Invoke-RestMethod -Uri "$baseUrl/maintenance/report/chart-data" -Method POST -Headers $headers -Body $requestBody
    Write-Host "✅ 成功生成图表数据" -ForegroundColor Green
    Write-Host ($response | ConvertTo-Json -Depth 3)
} catch {
    Write-Host "❌ 失败: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "状态码: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}
Write-Host ""

# 测试6: 获取任务完成率趋势
Write-Host "测试6: 获取任务完成率趋势" -ForegroundColor Yellow
try {
    $startDate = (Get-Date).AddDays(-30).ToString("yyyy-MM-dd")
    $endDate = (Get-Date).ToString("yyyy-MM-dd")
    $url = "$baseUrl/maintenance/report/task-completion-trends?startDate=$startDate&endDate=$endDate&timeDimension=3"
    
    $response = Invoke-RestMethod -Uri $url -Method GET -Headers $headers
    Write-Host "✅ 成功获取任务完成率趋势" -ForegroundColor Green
    Write-Host ($response | ConvertTo-Json -Depth 3)
} catch {
    Write-Host "❌ 失败: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "状态码: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}
Write-Host ""

# 测试7: 获取部门任务完成率统计
Write-Host "测试7: 获取部门任务完成率统计" -ForegroundColor Yellow
try {
    $startDate = (Get-Date).AddDays(-30).ToString("yyyy-MM-dd")
    $endDate = (Get-Date).ToString("yyyy-MM-dd")
    $url = "$baseUrl/maintenance/report/dept-completion?startDate=$startDate&endDate=$endDate"
    
    $response = Invoke-RestMethod -Uri $url -Method GET -Headers $headers
    Write-Host "✅ 成功获取部门任务完成率统计" -ForegroundColor Green
    Write-Host ($response | ConvertTo-Json -Depth 3)
} catch {
    Write-Host "❌ 失败: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "状态码: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}
Write-Host ""

# 测试8: 获取成本类型分布
Write-Host "测试8: 获取成本类型分布" -ForegroundColor Yellow
try {
    $startDate = (Get-Date).AddDays(-30).ToString("yyyy-MM-dd")
    $endDate = (Get-Date).ToString("yyyy-MM-dd")
    $url = "$baseUrl/maintenance/report/cost-type-distribution?startDate=$startDate&endDate=$endDate"
    
    $response = Invoke-RestMethod -Uri $url -Method GET -Headers $headers
    Write-Host "✅ 成功获取成本类型分布" -ForegroundColor Green
    Write-Host ($response | ConvertTo-Json -Depth 3)
} catch {
    Write-Host "❌ 失败: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "状态码: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}
Write-Host ""

Write-Host "=== 测试完成 ===" -ForegroundColor Green
