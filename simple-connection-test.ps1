# 简单连接测试
Write-Host "测试后端连接..." -ForegroundColor Green

# 测试基本连接
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8080/getInfo" -Method GET -Headers @{"Authorization"="Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiZjM0NDU2YTktYmU2Mi00MTY1LTk4ZjUtN2VhODNlM2VhNjBhIn0.xBoYuAG7vY_hqXeL7Siz_xiIG3ZjEOh_49L5cmvIFUvTHGWul0C33aZWLUbhXj6a4NHWO5aiIggbxC6MXerh_A"}
    Write-Host "连接成功!" -ForegroundColor Green
    Write-Host "用户信息: $($response.user.userName)" -ForegroundColor Cyan
} catch {
    Write-Host "连接失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试我们的新接口
Write-Host "`n测试消耗报表接口..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8080/item/consumption-report/optimization-suggestions?itemId=TEST&analysisPeriod=monthly" -Method GET -Headers @{"Authorization"="Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiZjM0NDU2YTktYmU2Mi00MTY1LTk4ZjUtN2VhODNlM2VhNjBhIn0.xBoYuAG7vY_hqXeL7Siz_xiIG3ZjEOh_49L5cmvIFUvTHGWul0C33aZWLUbhXj6a4NHWO5aiIggbxC6MXerh_A"}
    Write-Host "接口调用成功!" -ForegroundColor Green
    Write-Host "响应: $($response.msg)" -ForegroundColor Cyan
    Write-Host "数据: $($response.data)" -ForegroundColor White
} catch {
    Write-Host "接口调用失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n测试完成!" -ForegroundColor Green
