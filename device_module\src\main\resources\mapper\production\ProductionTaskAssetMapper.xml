<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.production.module.mapper.ProductionTaskAssetMapper">
    
    <resultMap type="com.jingfang.production.module.entity.ProductionTaskAsset" id="ProductionTaskAssetResult">
        <result property="relationId" column="relation_id" />
        <result property="taskId" column="task_id" />
        <result property="assetId" column="asset_id" />
        <result property="assetCode" column="asset_code" />
        <result property="assetName" column="asset_name" />
        <result property="usageType" column="usage_type" />
        <result property="createTime" column="create_time" />
        <result property="remark" column="remark" />
    </resultMap>
    
    <sql id="selectProductionTaskAssetVo">
        SELECT 
            pta.relation_id,
            pta.task_id,
            pta.asset_id,
            pta.asset_code,
            pta.asset_name,
            pta.usage_type,
            pta.create_time,
            pta.remark
        FROM production_task_asset pta
    </sql>
    
    <select id="selectAssetsByTaskId" resultMap="ProductionTaskAssetResult">
        <include refid="selectProductionTaskAssetVo"/>
        WHERE pta.task_id = #{taskId}
        ORDER BY pta.usage_type ASC, pta.create_time ASC
    </select>
    
    <select id="selectTasksByAssetId" resultMap="ProductionTaskAssetResult">
        <include refid="selectProductionTaskAssetVo"/>
        WHERE pta.asset_id = #{assetId}
        ORDER BY pta.create_time DESC
    </select>
    
    <select id="selectAssetUsageStatistics" resultType="java.util.Map">
        SELECT 
            pta.usage_type,
            CASE pta.usage_type 
                WHEN 1 THEN '主要设备'
                WHEN 2 THEN '辅助设备'
                WHEN 3 THEN '工具'
                WHEN 4 THEN '模具'
                ELSE '未知'
            END as usage_type_name,
            COUNT(*) as count,
            GROUP_CONCAT(pta.asset_name SEPARATOR ', ') as asset_names
        FROM production_task_asset pta
        WHERE pta.task_id = #{taskId}
        GROUP BY pta.usage_type
        ORDER BY pta.usage_type ASC
    </select>
    
    <select id="selectAssetTaskUsage" resultType="java.util.Map">
        SELECT 
            pta.task_id,
            pt.task_name,
            pt.status,
            CASE pt.status 
                WHEN 1 THEN '待执行'
                WHEN 2 THEN '已分配'
                WHEN 3 THEN '执行中'
                WHEN 4 THEN '质检中'
                WHEN 5 THEN '异常处理'
                WHEN 6 THEN '已完工'
                WHEN 7 THEN '已核算'
                ELSE '未知'
            END as status_name,
            pta.usage_type,
            CASE pta.usage_type 
                WHEN 1 THEN '主要设备'
                WHEN 2 THEN '辅助设备'
                WHEN 3 THEN '工具'
                WHEN 4 THEN '模具'
                ELSE '未知'
            END as usage_type_name,
            pt.plan_start_date,
            pt.plan_end_date,
            pta.create_time
        FROM production_task_asset pta
        LEFT JOIN production_task pt ON pta.task_id = pt.task_id
        WHERE pta.asset_id = #{assetId}
        ORDER BY pta.create_time DESC
    </select>
    
    <insert id="batchInsertTaskAssets">
        INSERT INTO production_task_asset (
            relation_id, task_id, asset_id, asset_code, asset_name, usage_type, create_time, remark
        ) VALUES
        <foreach collection="assetList" item="asset" separator=",">
            (
                #{asset.relationId}, #{asset.taskId}, #{asset.assetId}, 
                #{asset.assetCode}, #{asset.assetName}, #{asset.usageType}, 
                #{asset.createTime}, #{asset.remark}
            )
        </foreach>
    </insert>
    
    <delete id="deleteAssetsByTaskId">
        DELETE FROM production_task_asset WHERE task_id = #{taskId}
    </delete>
    
    <select id="checkAssetInUse" resultType="int">
        SELECT COUNT(*)
        FROM production_task_asset pta
        LEFT JOIN production_task pt ON pta.task_id = pt.task_id
        WHERE pta.asset_id = #{assetId}
        AND pt.status IN (1, 2, 3, 4, 5)
        <if test="excludeTaskId != null and excludeTaskId != ''">
            AND pta.task_id != #{excludeTaskId}
        </if>
    </select>

</mapper>
