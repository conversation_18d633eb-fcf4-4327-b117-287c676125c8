# 测试消耗效率排行榜接口
$token = "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiZjM0NDU2YTktYmU2Mi00MTY1LTk4ZjUtN2VhODNlM2VhNjBhIn0.xBoYuAG7vY_hqXeL7Siz_xiIG3ZjEOh_49L5cmvIFUvTHGWul0C33aZWLUbhXj6a4NHWO5aiIggbxC6MXerh_A"

Write-Host "测试消耗效率排行榜接口..." -ForegroundColor Green

try {
    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }
    
    $body = @{
        startDate = "2024-01-01"
        endDate = "2024-12-31"
        analysisPeriod = "monthly"
    } | ConvertTo-Json
    
    $url = "http://localhost:8080/item/consumption-report/efficiency-ranking?limit=5"
    Write-Host "请求URL: $url" -ForegroundColor Gray
    Write-Host "请求体: $body" -ForegroundColor Gray
    
    $response = Invoke-RestMethod -Uri $url -Method POST -Headers $headers -Body $body
    
    Write-Host "✅ 成功" -ForegroundColor Green
    Write-Host "响应消息: $($response.msg)" -ForegroundColor White
    Write-Host "响应代码: $($response.code)" -ForegroundColor White
    if ($response.data) {
        Write-Host "数据条数: $($response.data.Count)" -ForegroundColor White
        if ($response.data.Count -gt 0) {
            Write-Host "示例数据: $($response.data[0] | ConvertTo-Json)" -ForegroundColor White
        } else {
            Write-Host "返回空数据（这是正常的，因为没有实际的效率数据）" -ForegroundColor Yellow
        }
    }
}
catch {
    Write-Host "❌ 失败" -ForegroundColor Red
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode
        Write-Host "状态码: $statusCode" -ForegroundColor Red
        
        # 尝试读取详细错误信息
        try {
            $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
            $responseBody = $reader.ReadToEnd()
            Write-Host "详细错误: $responseBody" -ForegroundColor Red
        } catch {
            Write-Host "无法读取详细错误信息" -ForegroundColor Red
        }
    }
}

Write-Host "`n测试完成!" -ForegroundColor Green
