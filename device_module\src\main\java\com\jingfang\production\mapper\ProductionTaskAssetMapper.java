package com.jingfang.production.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jingfang.production.module.entity.ProductionTaskAsset;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 生产任务资产关联Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-16
 */
@Mapper
public interface ProductionTaskAssetMapper extends BaseMapper<ProductionTaskAsset> {

    /**
     * 根据任务ID查询关联的资产列表
     *
     * @param taskId 任务ID
     * @return 资产关联列表
     */
    List<ProductionTaskAsset> selectAssetsByTaskId(@Param("taskId") String taskId);

    /**
     * 批量插入任务资产关联
     *
     * @param assetList 资产关联列表
     * @return 插入结果
     */
    int batchInsertTaskAssets(@Param("assetList") List<ProductionTaskAsset> assetList);

    /**
     * 根据任务ID删除所有资产关联
     *
     * @param taskId 任务ID
     * @return 删除结果
     */
    int deleteAssetsByTaskId(@Param("taskId") String taskId);
}