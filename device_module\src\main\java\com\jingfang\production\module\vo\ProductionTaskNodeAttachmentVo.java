package com.jingfang.production.module.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 任务节点附件VO
 */
@Data
public class ProductionTaskNodeAttachmentVo implements Serializable {
    
    /**
     * 附件ID
     */
    private String attachmentId;
    
    /**
     * 节点ID
     */
    private String nodeId;
    
    /**
     * 节点名称
     */
    private String nodeName;
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 任务名称
     */
    private String taskName;
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 文件类型
     */
    private String fileType;
    
    /**
     * 文件大小(字节)
     */
    private Long fileSize;
    
    /**
     * 文件大小描述
     */
    private String fileSizeDesc;
    
    /**
     * 文件存储路径
     */
    private String filePath;
    
    /**
     * 上传人ID
     */
    private Long uploadUserId;
    
    /**
     * 上传人姓名
     */
    private String uploadUserName;
    
    /**
     * 上传时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date uploadTime;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 文件下载URL
     */
    private String downloadUrl;
    
    /**
     * 是否可以删除
     */
    private Boolean canDelete;
    
    private static final long serialVersionUID = 1L;
}
