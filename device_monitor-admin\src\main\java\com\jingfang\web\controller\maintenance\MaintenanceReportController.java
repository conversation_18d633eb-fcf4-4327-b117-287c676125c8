package com.jingfang.web.controller.maintenance;

import com.jingfang.common.core.controller.BaseController;
import com.jingfang.common.core.domain.AjaxResult;
import com.jingfang.maintenance_report.module.dto.MaintenanceReportRequest;
import com.jingfang.maintenance_report.module.vo.MaintenanceReportVo;
import com.jingfang.maintenance_report.service.MaintenanceReportService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 维护报表控制器
 */
@Slf4j
@RestController
@RequestMapping("/maintenance/report")
public class MaintenanceReportController extends BaseController {
    
    @Resource
    private MaintenanceReportService reportService;
    
    /**
     * 获取维护任务完成率统计
     */
    @PreAuthorize("@ss.hasPermi('maintenance:report:completion')")
    @GetMapping("/task-completion")
    public AjaxResult getTaskCompletionStatistics(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
            @RequestParam(required = false) String deptId,
            @RequestParam(required = false) String assetType,
            @RequestParam(required = false) List<Integer> priorities) {
        
        try {
            log.info("获取维护任务完成率统计，时间范围：{} - {}，部门：{}，资产类型：{}，优先级：{}", 
                    startDate, endDate, deptId, assetType, priorities);
            
            MaintenanceReportVo.TaskCompletionVo statistics = 
                reportService.getTaskCompletionStatistics(startDate, endDate, deptId, assetType, priorities);
            return success(statistics);
        } catch (Exception e) {
            log.error("获取维护任务完成率统计失败", e);
            return error("获取统计数据失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取故障响应时间统计
     */
    @PreAuthorize("@ss.hasPermi('maintenance:report:response')")
    @GetMapping("/fault-response")
    public AjaxResult getFaultResponseStatistics(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
            @RequestParam(required = false) List<Integer> faultTypes,
            @RequestParam(required = false) List<Integer> urgencyLevels,
            @RequestParam(required = false) List<Long> handlerIds) {
        
        try {
            log.info("获取故障响应时间统计，时间范围：{} - {}，故障类型：{}，紧急程度：{}，处理人员：{}", 
                    startDate, endDate, faultTypes, urgencyLevels, handlerIds);
            
            MaintenanceReportVo.FaultResponseVo statistics = 
                reportService.getFaultResponseStatistics(startDate, endDate, faultTypes, urgencyLevels, handlerIds);
            return success(statistics);
        } catch (Exception e) {
            log.error("获取故障响应时间统计失败", e);
            return error("获取统计数据失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取维护成本统计
     */
    @PreAuthorize("@ss.hasPermi('maintenance:report:cost')")
    @GetMapping("/maintenance-cost")
    public AjaxResult getMaintenanceCostStatistics(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
            @RequestParam(required = false) String deptId,
            @RequestParam(required = false) String assetId,
            @RequestParam(required = false) List<Integer> costTypes) {
        
        try {
            log.info("获取维护成本统计，时间范围：{} - {}，部门：{}，资产：{}，成本类型：{}", 
                    startDate, endDate, deptId, assetId, costTypes);
            
            MaintenanceReportVo.MaintenanceCostVo statistics = 
                reportService.getMaintenanceCostStatistics(startDate, endDate, deptId, assetId, costTypes);
            return success(statistics);
        } catch (Exception e) {
            log.error("获取维护成本统计失败", e);
            return error("获取统计数据失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取综合报表数据
     */
    @PreAuthorize("@ss.hasPermi('maintenance:report:comprehensive')")
    @PostMapping("/comprehensive")
    public AjaxResult getComprehensiveReport(@RequestBody @Validated MaintenanceReportRequest request) {
        try {
            log.info("获取综合报表数据，请求参数：{}", request);
            
            MaintenanceReportVo.ComprehensiveReportVo report = reportService.getComprehensiveReport(request);
            return success(report);
        } catch (Exception e) {
            log.error("获取综合报表数据失败", e);
            return error("获取报表数据失败：" + e.getMessage());
        }
    }
    
    /**
     * 生成维护报表图表数据
     */
    @PreAuthorize("@ss.hasPermi('maintenance:report:chart')")
    @PostMapping("/chart-data")
    public AjaxResult generateChartData(@RequestBody @Validated MaintenanceReportRequest request) {
        try {
            log.info("生成维护报表图表数据，请求参数：{}", request);
            
            Map<String, Object> chartData = reportService.generateMaintenanceChartData(
                request.getReportType(), request.getStartDate(), request.getEndDate(), 
                request.getTimeDimension(), request.getFilters());
            return success(chartData);
        } catch (Exception e) {
            log.error("生成维护报表图表数据失败", e);
            return error("生成图表数据失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取任务完成率趋势数据
     */
    @PreAuthorize("@ss.hasPermi('maintenance:report:completion')")
    @GetMapping("/task-completion-trends")
    public AjaxResult getTaskCompletionTrends(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
            @RequestParam(defaultValue = "3") Integer timeDimension,
            @RequestParam(required = false) String deptId,
            @RequestParam(required = false) List<Integer> priorities) {
        
        try {
            List<MaintenanceReportVo.CompletionTrendVo> trends = 
                reportService.getTaskCompletionTrends(startDate, endDate, timeDimension, deptId, priorities);
            return success(trends);
        } catch (Exception e) {
            log.error("获取任务完成率趋势数据失败", e);
            return error("获取趋势数据失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取部门任务完成率统计
     */
    @PreAuthorize("@ss.hasPermi('maintenance:report:completion')")
    @GetMapping("/dept-completion")
    public AjaxResult getDeptCompletionStatistics(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
            @RequestParam(required = false) List<String> deptIds,
            @RequestParam(required = false) List<Integer> priorities) {
        
        try {
            List<MaintenanceReportVo.DeptCompletionVo> statistics = 
                reportService.getDeptCompletionStatistics(startDate, endDate, deptIds, priorities);
            return success(statistics);
        } catch (Exception e) {
            log.error("获取部门任务完成率统计失败", e);
            return error("获取统计数据失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取优先级任务完成率统计
     */
    @PreAuthorize("@ss.hasPermi('maintenance:report:completion')")
    @GetMapping("/priority-completion")
    public AjaxResult getPriorityCompletionStatistics(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
            @RequestParam(required = false) String deptId) {
        
        try {
            List<MaintenanceReportVo.PriorityCompletionVo> statistics = 
                reportService.getPriorityCompletionStatistics(startDate, endDate, deptId);
            return success(statistics);
        } catch (Exception e) {
            log.error("获取优先级任务完成率统计失败", e);
            return error("获取统计数据失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取故障类型响应时间统计
     */
    @PreAuthorize("@ss.hasPermi('maintenance:report:response')")
    @GetMapping("/fault-type-response")
    public AjaxResult getFaultTypeResponseStatistics(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
            @RequestParam(required = false) String deptId) {
        
        try {
            List<MaintenanceReportVo.FaultTypeResponseVo> statistics = 
                reportService.getFaultTypeResponseStatistics(startDate, endDate, deptId);
            return success(statistics);
        } catch (Exception e) {
            log.error("获取故障类型响应时间统计失败", e);
            return error("获取统计数据失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取紧急程度响应时间统计
     */
    @PreAuthorize("@ss.hasPermi('maintenance:report:response')")
    @GetMapping("/urgency-response")
    public AjaxResult getUrgencyResponseStatistics(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
            @RequestParam(required = false) String deptId) {
        
        try {
            List<MaintenanceReportVo.UrgencyResponseVo> statistics = 
                reportService.getUrgencyResponseStatistics(startDate, endDate, deptId);
            return success(statistics);
        } catch (Exception e) {
            log.error("获取紧急程度响应时间统计失败", e);
            return error("获取统计数据失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取响应时间趋势数据
     */
    @PreAuthorize("@ss.hasPermi('maintenance:report:response')")
    @GetMapping("/response-trends")
    public AjaxResult getResponseTimeTrends(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
            @RequestParam(defaultValue = "3") Integer timeDimension,
            @RequestParam(required = false) List<Integer> faultTypes) {
        
        try {
            List<MaintenanceReportVo.ResponseTrendVo> trends = 
                reportService.getResponseTimeTrends(startDate, endDate, timeDimension, faultTypes);
            return success(trends);
        } catch (Exception e) {
            log.error("获取响应时间趋势数据失败", e);
            return error("获取趋势数据失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取成本趋势数据
     */
    @PreAuthorize("@ss.hasPermi('maintenance:report:cost')")
    @GetMapping("/cost-trends")
    public AjaxResult getCostTrends(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
            @RequestParam(defaultValue = "3") Integer timeDimension,
            @RequestParam(required = false) String deptId,
            @RequestParam(required = false) String assetId,
            @RequestParam(required = false) List<Integer> costTypes) {
        
        try {
            List<MaintenanceReportVo.CostTrendVo> trends = 
                reportService.getCostTrends(startDate, endDate, timeDimension, deptId, assetId, costTypes);
            return success(trends);
        } catch (Exception e) {
            log.error("获取成本趋势数据失败", e);
            return error("获取趋势数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取部门成本统计
     */
    @PreAuthorize("@ss.hasPermi('maintenance:report:cost')")
    @GetMapping("/dept-cost")
    public AjaxResult getDeptCostStatistics(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
            @RequestParam(required = false) List<String> deptIds,
            @RequestParam(required = false) List<Integer> costTypes) {

        try {
            List<MaintenanceReportVo.DeptCostVo> statistics =
                reportService.getDeptCostStatistics(startDate, endDate, deptIds, costTypes);
            return success(statistics);
        } catch (Exception e) {
            log.error("获取部门成本统计失败", e);
            return error("获取统计数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取资产成本统计
     */
    @PreAuthorize("@ss.hasPermi('maintenance:report:cost')")
    @GetMapping("/asset-cost")
    public AjaxResult getAssetCostStatistics(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
            @RequestParam(required = false) String deptId,
            @RequestParam(required = false) List<String> assetIds,
            @RequestParam(required = false) List<Integer> costTypes) {

        try {
            List<MaintenanceReportVo.AssetCostVo> statistics =
                reportService.getAssetCostStatistics(startDate, endDate, deptId, assetIds, costTypes);
            return success(statistics);
        } catch (Exception e) {
            log.error("获取资产成本统计失败", e);
            return error("获取统计数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取成本类型分布
     */
    @PreAuthorize("@ss.hasPermi('maintenance:report:cost')")
    @GetMapping("/cost-type-distribution")
    public AjaxResult getCostTypeDistribution(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
            @RequestParam(required = false) String deptId,
            @RequestParam(required = false) String assetId) {

        try {
            List<MaintenanceReportVo.CostTypeVo> distribution =
                reportService.getCostTypeDistribution(startDate, endDate, deptId, assetId);
            return success(distribution);
        } catch (Exception e) {
            log.error("获取成本类型分布失败", e);
            return error("获取分布数据失败：" + e.getMessage());
        }
    }

    /**
     * 导出维护报表
     */
    @PreAuthorize("@ss.hasPermi('maintenance:report:export')")
    @PostMapping("/export")
    public void exportReport(HttpServletResponse response, @RequestBody @Validated MaintenanceReportRequest request) {
        try {
            log.info("导出维护报表，请求参数：{}", request);
            reportService.exportMaintenanceReport(response, request);
        } catch (Exception e) {
            log.error("导出维护报表失败", e);
        }
    }

    /**
     * 获取报表配置选项
     */
    @PreAuthorize("@ss.hasPermi('maintenance:report:view')")
    @GetMapping("/config-options")
    public AjaxResult getReportConfigOptions() {
        try {
            Map<String, Object> options = reportService.getReportConfigOptions();
            return success(options);
        } catch (Exception e) {
            log.error("获取报表配置选项失败", e);
            return error("获取配置选项失败：" + e.getMessage());
        }
    }

    /**
     * 保存报表配置
     */
    @PreAuthorize("@ss.hasPermi('maintenance:report:config')")
    @PostMapping("/save-config")
    public AjaxResult saveReportConfig(
            @RequestParam String configName,
            @RequestParam Integer reportType,
            @RequestBody Map<String, Object> config) {

        try {
            boolean result = reportService.saveReportConfig(configName, reportType, config);
            return result ? success("保存成功") : error("保存失败");
        } catch (Exception e) {
            log.error("保存报表配置失败", e);
            return error("保存配置失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户报表配置列表
     */
    @PreAuthorize("@ss.hasPermi('maintenance:report:view')")
    @GetMapping("/user-configs")
    public AjaxResult getUserReportConfigs(@RequestParam Integer reportType) {
        try {
            List<Map<String, Object>> configs = reportService.getUserReportConfigs(reportType);
            return success(configs);
        } catch (Exception e) {
            log.error("获取用户报表配置失败", e);
            return error("获取配置失败：" + e.getMessage());
        }
    }
}
