package com.jingfang.web.controller.production;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jingfang.common.utils.uuid.IdUtils;
import com.jingfang.production.module.dto.ProductionTaskDto;
import com.jingfang.production.module.dto.ProductionTaskQueryDto;
import com.jingfang.production.module.service.IProductionTaskService;
import com.jingfang.production.module.vo.ProductionTaskVo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 生产任务Controller测试类
 */
@WebMvcTest(ProductionTaskController.class)
@ActiveProfiles("test")
public class ProductionTaskControllerTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @MockBean
    private IProductionTaskService productionTaskService;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    private ProductionTaskDto testTaskDto;
    private ProductionTaskVo testTaskVo;
    private String testTaskId;
    
    @BeforeEach
    void setUp() {
        testTaskId = IdUtils.fastSimpleUUID();
        
        // 准备测试数据
        testTaskDto = new ProductionTaskDto();
        testTaskDto.setTaskId(testTaskId);
        testTaskDto.setTaskName("测试生产任务");
        testTaskDto.setTaskType(1);
        testTaskDto.setProductName("测试产品");
        testTaskDto.setPlanStartDate(new Date());
        testTaskDto.setPlanEndDate(new Date(System.currentTimeMillis() + 86400000));
        testTaskDto.setPriorityLevel(2);
        testTaskDto.setResponsibleUserId(1L);
        testTaskDto.setEstimatedHours(new BigDecimal("8.0"));
        testTaskDto.setRemark("测试备注");
        
        testTaskVo = new ProductionTaskVo();
        testTaskVo.setTaskId(testTaskId);
        testTaskVo.setTaskName("测试生产任务");
        testTaskVo.setTaskCode("SCRW202501160001");
        testTaskVo.setTaskType(1);
        testTaskVo.setTaskTypeName("正常生产");
        testTaskVo.setProductName("测试产品");
        testTaskVo.setPlanStartDate(new Date());
        testTaskVo.setPlanEndDate(new Date(System.currentTimeMillis() + 86400000));
        testTaskVo.setPriorityLevel(2);
        testTaskVo.setPriorityLevelName("中");
        testTaskVo.setResponsibleUserId(1L);
        testTaskVo.setResponsibleUserName("测试用户");
        testTaskVo.setEstimatedHours(new BigDecimal("8.0"));
        testTaskVo.setStatus(1);
        testTaskVo.setStatusName("待执行");
        testTaskVo.setProgressRate(BigDecimal.ZERO);
        testTaskVo.setCreateBy("admin");
        testTaskVo.setCreateTime(new Date());
        testTaskVo.setRemark("测试备注");
    }
    
    @Test
    @WithMockUser(username = "admin", authorities = {"production:task:query"})
    void testList() throws Exception {
        // 准备查询条件
        ProductionTaskQueryDto queryDto = new ProductionTaskQueryDto();
        queryDto.setPageNum(1);
        queryDto.setPageSize(10);
        queryDto.setTaskName("测试");
        
        // 模拟分页结果
        IPage<ProductionTaskVo> mockPage = new Page<>();
        mockPage.setRecords(List.of(testTaskVo));
        mockPage.setTotal(1L);
        
        when(productionTaskService.selectProductionTaskList(any(ProductionTaskQueryDto.class)))
                .thenReturn(mockPage);
        
        // 执行测试
        mockMvc.perform(post("/production/task/list")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(queryDto)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("查询成功"))
                .andExpect(jsonPath("$.total").value(1))
                .andExpect(jsonPath("$.rows[0].taskId").value(testTaskId))
                .andExpect(jsonPath("$.rows[0].taskName").value("测试生产任务"));
    }
    
    @Test
    @WithMockUser(username = "admin", authorities = {"production:task:query"})
    void testGetInfo() throws Exception {
        // 模拟返回数据
        when(productionTaskService.selectProductionTaskById(testTaskId)).thenReturn(testTaskVo);
        
        // 执行测试
        mockMvc.perform(get("/production/task/{taskId}", testTaskId))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.taskId").value(testTaskId))
                .andExpect(jsonPath("$.data.taskName").value("测试生产任务"))
                .andExpect(jsonPath("$.data.taskCode").value("SCRW202501160001"));
    }
    
    @Test
    @WithMockUser(username = "admin", authorities = {"production:task:add"})
    void testAdd() throws Exception {
        // 模拟返回结果
        when(productionTaskService.insertProductionTask(any(ProductionTaskDto.class), anyString()))
                .thenReturn(true);
        
        // 执行测试
        mockMvc.perform(post("/production/task")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testTaskDto)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }
    
    @Test
    @WithMockUser(username = "admin", authorities = {"production:task:edit"})
    void testEdit() throws Exception {
        // 模拟返回结果
        when(productionTaskService.updateProductionTask(any(ProductionTaskDto.class), anyString()))
                .thenReturn(true);
        
        // 执行测试
        mockMvc.perform(put("/production/task")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testTaskDto)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }
    
    @Test
    @WithMockUser(username = "admin", authorities = {"production:task:remove"})
    void testRemove() throws Exception {
        // 模拟返回结果
        when(productionTaskService.deleteProductionTasks(anyList())).thenReturn(true);
        
        // 执行测试
        mockMvc.perform(delete("/production/task/{taskIds}", testTaskId)
                        .with(csrf()))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }
    
    @Test
    @WithMockUser(username = "admin", authorities = {"production:task:edit"})
    void testStartTask() throws Exception {
        // 模拟返回结果
        when(productionTaskService.startTask(testTaskId, "admin")).thenReturn(true);
        
        // 执行测试
        mockMvc.perform(post("/production/task/start/{taskId}", testTaskId)
                        .with(csrf()))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }
    
    @Test
    @WithMockUser(username = "admin", authorities = {"production:task:edit"})
    void testCompleteTask() throws Exception {
        // 模拟返回结果
        when(productionTaskService.completeTask(testTaskId, "admin")).thenReturn(true);
        
        // 执行测试
        mockMvc.perform(post("/production/task/complete/{taskId}", testTaskId)
                        .with(csrf()))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }
    
    @Test
    @WithMockUser(username = "admin", authorities = {"production:task:edit"})
    void testUpdateProgress() throws Exception {
        // 模拟返回结果
        BigDecimal progressRate = new BigDecimal("50.0");
        when(productionTaskService.updateTaskProgress(testTaskId, progressRate)).thenReturn(true);
        
        // 执行测试
        mockMvc.perform(post("/production/task/progress/{taskId}", testTaskId)
                        .with(csrf())
                        .param("progressRate", "50.0"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }
    
    @Test
    @WithMockUser(username = "admin", authorities = {"production:task:edit"})
    void testAssignTask() throws Exception {
        // 模拟返回结果
        Long responsibleUserId = 2L;
        when(productionTaskService.assignTask(testTaskId, responsibleUserId, "admin")).thenReturn(true);
        
        // 执行测试
        mockMvc.perform(post("/production/task/assign/{taskId}", testTaskId)
                        .with(csrf())
                        .param("responsibleUserId", "2"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }
    
    @Test
    @WithMockUser(username = "admin", authorities = {"production:task:query"})
    void testGetStatusStatistics() throws Exception {
        // 准备模拟数据
        List<Map<String, Object>> mockStatistics = List.of(
                Map.of("status", 1, "status_name", "待执行", "count", 5),
                Map.of("status", 2, "status_name", "已分配", "count", 3)
        );
        
        // 模拟返回结果
        when(productionTaskService.selectTaskStatusStatistics()).thenReturn(mockStatistics);
        
        // 执行测试
        mockMvc.perform(get("/production/task/statistics/status"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data[0].status").value(1))
                .andExpect(jsonPath("$.data[0].status_name").value("待执行"))
                .andExpect(jsonPath("$.data[0].count").value(5));
    }
    
    @Test
    @WithMockUser(username = "admin", authorities = {"production:task:query"})
    void testGetUpcomingTasks() throws Exception {
        // 准备模拟数据
        List<ProductionTaskVo> mockTasks = List.of(testTaskVo);
        
        // 模拟返回结果
        when(productionTaskService.selectUpcomingTasks(7)).thenReturn(mockTasks);
        
        // 执行测试
        mockMvc.perform(get("/production/task/upcoming")
                        .param("days", "7"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data[0].taskId").value(testTaskId))
                .andExpect(jsonPath("$.data[0].taskName").value("测试生产任务"));
    }
    
    @Test
    @WithMockUser(username = "admin", authorities = {"production:task:add"})
    void testCopyTask() throws Exception {
        // 模拟返回结果
        when(productionTaskService.copyTask(testTaskId, "复制的任务", "admin")).thenReturn(true);
        
        // 执行测试
        mockMvc.perform(post("/production/task/copy/{sourceTaskId}", testTaskId)
                        .with(csrf())
                        .param("taskName", "复制的任务"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }
    
    @Test
    @WithMockUser(username = "admin")
    void testUnauthorizedAccess() throws Exception {
        // 测试无权限访问
        mockMvc.perform(get("/production/task/{taskId}", testTaskId))
                .andDo(print())
                .andExpect(status().isForbidden());
    }
    
    @Test
    @WithMockUser(username = "admin", authorities = {"production:task:query"})
    void testInvalidTaskId() throws Exception {
        // 模拟返回空数据
        when(productionTaskService.selectProductionTaskById("invalid-id")).thenReturn(null);
        
        // 执行测试
        mockMvc.perform(get("/production/task/{taskId}", "invalid-id"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isEmpty());
    }
}
