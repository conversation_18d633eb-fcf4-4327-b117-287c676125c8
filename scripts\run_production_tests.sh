#!/bin/bash

# 生产管控模块测试运行脚本
# 用法: ./run_production_tests.sh [选项]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
生产管控模块测试运行脚本

用法: $0 [选项]

选项:
    -h, --help          显示此帮助信息
    -u, --unit          只运行单元测试
    -i, --integration   只运行集成测试
    -c, --controller    只运行控制器测试
    -a, --api           只运行API测试
    -r, --report        生成测试报告
    --start-server      启动测试服务器
    --stop-server       停止测试服务器
    --cleanup           清理测试环境
    --all               运行所有测试（默认）

示例:
    $0                  # 运行所有测试
    $0 --unit           # 只运行单元测试
    $0 --api            # 只运行API测试
    $0 --report         # 生成测试报告

EOF
}

# 检查Java环境
check_java() {
    if ! command -v java &> /dev/null; then
        log_error "Java未安装或未在PATH中"
        exit 1
    fi
    
    JAVA_VERSION=$(java -version 2>&1 | head -n1 | cut -d'"' -f2)
    log_info "Java版本: $JAVA_VERSION"
}

# 检查Maven环境
check_maven() {
    if ! command -v mvn &> /dev/null; then
        log_error "Maven未安装或未在PATH中"
        exit 1
    fi
    
    MVN_VERSION=$(mvn -version | head -n1 | cut -d' ' -f3)
    log_info "Maven版本: $MVN_VERSION"
}

# 检查Python环境（用于API测试）
check_python() {
    if ! command -v python3 &> /dev/null; then
        log_warning "Python3未安装，将跳过API测试"
        return 1
    fi
    
    if ! python3 -c "import requests" &> /dev/null; then
        log_warning "Python requests库未安装，将跳过API测试"
        log_info "安装命令: pip3 install requests"
        return 1
    fi
    
    PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
    log_info "Python版本: $PYTHON_VERSION"
    return 0
}

# 检查数据库连接
check_database() {
    log_info "检查数据库连接..."
    
    # 这里可以添加数据库连接检查逻辑
    # 例如：mysql -h localhost -u root -p123456 -e "SELECT 1" &> /dev/null
    
    log_success "数据库连接正常"
}

# 编译项目
compile_project() {
    log_info "编译项目..."
    
    if [ -f "pom.xml" ]; then
        mvn clean compile -q
        log_success "项目编译完成"
    else
        log_error "未找到pom.xml文件"
        exit 1
    fi
}

# 运行单元测试
run_unit_tests() {
    log_info "运行生产管控模块单元测试..."
    
    # 运行生产任务服务测试
    log_info "运行生产任务服务测试..."
    mvn test -Dtest=ProductionTaskServiceTest -Dspring.profiles.active=test -q
    
    # 运行任务节点服务测试
    log_info "运行任务节点服务测试..."
    mvn test -Dtest=ProductionTaskNodeServiceTest -Dspring.profiles.active=test -q
    
    # 运行附件服务测试
    log_info "运行附件服务测试..."
    mvn test -Dtest=ProductionTaskNodeAttachmentServiceTest -Dspring.profiles.active=test -q
    
    log_success "单元测试完成"
}

# 运行集成测试
run_integration_tests() {
    log_info "运行生产管控模块集成测试..."
    
    mvn test -Dtest=ProductionTaskIntegrationTest -Dspring.profiles.active=test -q
    
    log_success "集成测试完成"
}

# 运行控制器测试
run_controller_tests() {
    log_info "运行生产管控模块控制器测试..."
    
    mvn test -Dtest=ProductionTaskControllerTest -Dspring.profiles.active=test -q
    mvn test -Dtest=ProductionTaskNodeControllerTest -Dspring.profiles.active=test -q
    
    log_success "控制器测试完成"
}

# 启动应用服务器
start_server() {
    log_info "启动应用服务器..."
    
    # 检查端口是否被占用
    if lsof -Pi :8080 -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_warning "端口8080已被占用，尝试停止现有进程..."
        pkill -f "spring-boot" || true
        sleep 3
    fi
    
    # 启动服务器
    nohup mvn spring-boot:run -Dspring.profiles.active=test > server.log 2>&1 &
    SERVER_PID=$!
    
    # 等待服务器启动
    log_info "等待服务器启动..."
    for i in {1..30}; do
        if curl -s http://localhost:8080/actuator/health > /dev/null 2>&1; then
            log_success "服务器启动成功 (PID: $SERVER_PID)"
            echo $SERVER_PID > server.pid
            return 0
        fi
        sleep 2
    done
    
    log_error "服务器启动失败"
    return 1
}

# 停止应用服务器
stop_server() {
    log_info "停止应用服务器..."
    
    if [ -f "server.pid" ]; then
        SERVER_PID=$(cat server.pid)
        if kill -0 $SERVER_PID 2>/dev/null; then
            kill $SERVER_PID
            rm server.pid
            log_success "服务器已停止"
        else
            log_warning "服务器进程不存在"
            rm server.pid
        fi
    else
        # 尝试通过进程名停止
        pkill -f "spring-boot" || true
        log_success "服务器已停止"
    fi
}

# 运行API测试
run_api_tests() {
    if ! check_python; then
        log_warning "跳过API测试"
        return 0
    fi
    
    log_info "运行生产管控模块API测试..."
    
    # 启动服务器
    if ! start_server; then
        log_error "无法启动服务器，跳过API测试"
        return 1
    fi
    
    # 运行Python API测试脚本
    python3 scripts/test_production_api.py
    API_TEST_RESULT=$?
    
    # 停止服务器
    stop_server
    
    if [ $API_TEST_RESULT -eq 0 ]; then
        log_success "API测试完成"
        return 0
    else
        log_error "API测试失败"
        return 1
    fi
}

# 生成测试报告
generate_test_report() {
    log_info "生成测试报告..."
    
    # 生成Maven测试报告
    mvn surefire-report:report -q
    
    # 生成代码覆盖率报告
    mvn jacoco:report -q
    
    REPORT_DIR="target/site"
    if [ -d "$REPORT_DIR" ]; then
        log_success "测试报告已生成:"
        echo "  - 测试结果报告: $REPORT_DIR/surefire-report.html"
        echo "  - 代码覆盖率报告: $REPORT_DIR/jacoco/index.html"
    else
        log_warning "报告目录不存在，请检查Maven配置"
    fi
}

# 清理测试环境
cleanup() {
    log_info "清理测试环境..."
    
    # 停止服务器
    stop_server
    
    # 清理临时文件
    rm -f server.log
    rm -f *.pid
    
    # 清理Maven缓存
    mvn clean -q
    
    log_success "测试环境清理完成"
}

# 主函数
main() {
    # 切换到项目根目录
    cd "$(dirname "$0")/.."
    
    # 解析命令行参数
    case "${1:-all}" in
        -h|--help)
            show_help
            exit 0
            ;;
        -u|--unit)
            check_java
            check_maven
            compile_project
            run_unit_tests
            ;;
        -i|--integration)
            check_java
            check_maven
            check_database
            compile_project
            run_integration_tests
            ;;
        -c|--controller)
            check_java
            check_maven
            compile_project
            run_controller_tests
            ;;
        -a|--api)
            check_java
            check_maven
            check_database
            compile_project
            run_api_tests
            ;;
        -r|--report)
            generate_test_report
            ;;
        --start-server)
            check_java
            check_maven
            compile_project
            start_server
            ;;
        --stop-server)
            stop_server
            ;;
        --cleanup)
            cleanup
            ;;
        --all|all|"")
            # 运行所有测试
            check_java
            check_maven
            check_database
            compile_project
            
            log_info "开始运行生产管控模块完整测试套件..."
            
            # 运行单元测试
            run_unit_tests
            
            # 运行集成测试
            run_integration_tests
            
            # 运行控制器测试
            run_controller_tests
            
            # 运行API测试
            run_api_tests
            
            # 生成测试报告
            generate_test_report
            
            log_success "所有测试完成！"
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
