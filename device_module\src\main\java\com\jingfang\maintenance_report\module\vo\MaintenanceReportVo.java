package com.jingfang.maintenance_report.module.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 维护报表VO
 */
@Data
public class MaintenanceReportVo {
    
    /**
     * 维护任务完成率统计
     */
    @Data
    public static class TaskCompletionVo {
        /**
         * 总任务数量
         */
        private Integer totalTasks;
        
        /**
         * 已完成任务数量
         */
        private Integer completedTasks;
        
        /**
         * 完成率百分比
         */
        private BigDecimal completionRate;
        
        /**
         * 逾期任务数量
         */
        private Integer overdueTasks;
        
        /**
         * 逾期率百分比
         */
        private BigDecimal overdueRate;
        
        /**
         * 按时完成任务数量
         */
        private Integer ontimeCompletedTasks;
        
        /**
         * 按时完成率
         */
        private BigDecimal ontimeCompletionRate;
        
        /**
         * 平均完成天数
         */
        private BigDecimal avgCompletionDays;
        
        /**
         * 平均完成小时数
         */
        private BigDecimal avgCompletionHours;
        
        /**
         * 统计时间段
         */
        private Date startDate;
        private Date endDate;
        
        /**
         * 按时间维度的完成率趋势
         */
        private List<CompletionTrendVo> completionTrends;
        
        /**
         * 按部门的完成率统计
         */
        private List<DeptCompletionVo> deptCompletions;
        
        /**
         * 按优先级的完成率统计
         */
        private List<PriorityCompletionVo> priorityCompletions;
    }
    
    /**
     * 完成率趋势数据
     */
    @Data
    public static class CompletionTrendVo {
        private String period; // 时间段（如：2024-01, 2024-W01）
        private String periodName; // 时间段名称
        private Integer totalTasks;
        private Integer completedTasks;
        private BigDecimal completionRate;
        private Integer overdueTasks;
    }
    
    /**
     * 部门完成率统计
     */
    @Data
    public static class DeptCompletionVo {
        private String deptId;
        private String deptName;
        private Integer totalTasks;
        private Integer completedTasks;
        private BigDecimal completionRate;
        private Integer overdueTasks;
        private BigDecimal avgCompletionHours;
    }
    
    /**
     * 优先级完成率统计
     */
    @Data
    public static class PriorityCompletionVo {
        private Integer priority;
        private String priorityName;
        private Integer totalTasks;
        private Integer completedTasks;
        private BigDecimal completionRate;
        private Integer overdueTasks;
        private BigDecimal avgCompletionHours;
    }
    
    /**
     * 故障响应时间统计
     */
    @Data
    public static class FaultResponseVo {
        /**
         * 平均响应时间（小时）
         */
        private BigDecimal avgResponseHours;
        
        /**
         * 平均处理时间（小时）
         */
        private BigDecimal avgProcessingHours;
        
        /**
         * 总故障数量
         */
        private Integer totalFaults;
        
        /**
         * 已解决故障数量
         */
        private Integer resolvedFaults;
        
        /**
         * 解决率百分比
         */
        private BigDecimal resolutionRate;
        
        /**
         * 超时处理故障数量
         */
        private Integer overtimeFaults;
        
        /**
         * 超时率
         */
        private BigDecimal overtimeRate;
        
        /**
         * 重复故障数量
         */
        private Integer repeatFaults;
        
        /**
         * 重复故障率
         */
        private BigDecimal repeatFaultRate;
        
        /**
         * 统计时间段
         */
        private Date startDate;
        private Date endDate;
        
        /**
         * 按故障类型的响应时间统计
         */
        private List<FaultTypeResponseVo> faultTypeResponses;
        
        /**
         * 按紧急程度的响应时间统计
         */
        private List<UrgencyResponseVo> urgencyResponses;
        
        /**
         * 响应时间趋势
         */
        private List<ResponseTrendVo> responseTrends;
    }
    
    /**
     * 故障类型响应统计
     */
    @Data
    public static class FaultTypeResponseVo {
        private Integer faultType;
        private String faultTypeName;
        private Integer totalFaults;
        private Integer resolvedFaults;
        private BigDecimal avgResponseHours;
        private BigDecimal avgProcessingHours;
        private BigDecimal resolutionRate;
    }
    
    /**
     * 紧急程度响应统计
     */
    @Data
    public static class UrgencyResponseVo {
        private Integer urgencyLevel;
        private String urgencyLevelName;
        private Integer totalFaults;
        private Integer resolvedFaults;
        private BigDecimal avgResponseHours;
        private BigDecimal avgProcessingHours;
        private BigDecimal resolutionRate;
    }
    
    /**
     * 响应时间趋势
     */
    @Data
    public static class ResponseTrendVo {
        private String period;
        private String periodName;
        private BigDecimal avgResponseHours;
        private BigDecimal avgProcessingHours;
        private Integer totalFaults;
        private Integer resolvedFaults;
    }
    
    /**
     * 维护成本统计
     */
    @Data
    public static class MaintenanceCostVo {
        /**
         * 总成本
         */
        private BigDecimal totalCost;
        
        /**
         * 人工成本
         */
        private BigDecimal laborCost;
        
        /**
         * 材料成本
         */
        private BigDecimal materialCost;
        
        /**
         * 外包成本
         */
        private BigDecimal outsourceCost;
        
        /**
         * 停机成本
         */
        private BigDecimal downtimeCost;
        
        /**
         * 平均单次维护成本
         */
        private BigDecimal avgMaintenanceCost;
        
        /**
         * 预防性维护成本
         */
        private BigDecimal preventiveCost;
        
        /**
         * 故障维护成本
         */
        private BigDecimal correctiveCost;
        
        /**
         * 统计时间段
         */
        private Date startDate;
        private Date endDate;
        
        /**
         * 成本趋势数据
         */
        private List<CostTrendVo> costTrends;
        
        /**
         * 按部门的成本统计
         */
        private List<DeptCostVo> deptCosts;
        
        /**
         * 按资产的成本统计
         */
        private List<AssetCostVo> assetCosts;
        
        /**
         * 按成本类型的分布
         */
        private List<CostTypeVo> costTypeDistribution;
    }
    
    /**
     * 成本趋势数据
     */
    @Data
    public static class CostTrendVo {
        private String period;
        private String periodName;
        private BigDecimal totalCost;
        private BigDecimal laborCost;
        private BigDecimal materialCost;
        private BigDecimal outsourceCost;
        private BigDecimal downtimeCost;
        private Integer recordCount;
    }
    
    /**
     * 部门成本统计
     */
    @Data
    public static class DeptCostVo {
        private String deptId;
        private String deptName;
        private BigDecimal totalCost;
        private BigDecimal avgCost;
        private Integer recordCount;
        private BigDecimal costPercentage;
    }
    
    /**
     * 资产成本统计
     */
    @Data
    public static class AssetCostVo {
        private String assetId;
        private String assetName;
        private String assetCode;
        private BigDecimal totalCost;
        private BigDecimal avgCost;
        private Integer recordCount;
        private Date lastMaintenanceDate;
    }
    
    /**
     * 成本类型分布
     */
    @Data
    public static class CostTypeVo {
        private Integer costType;
        private String costTypeName;
        private BigDecimal amount;
        private BigDecimal percentage;
        private Integer recordCount;
    }
    
    /**
     * 综合报表数据
     */
    @Data
    public static class ComprehensiveReportVo {
        private TaskCompletionVo taskCompletion;
        private FaultResponseVo faultResponse;
        private MaintenanceCostVo maintenanceCost;
        private Date reportGenerateTime;
        private String reportPeriod;
    }
}
