package com.jingfang.asset_ledger.module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.jingfang.asset_ledger.module.dto.AttachmentDto;
import lombok.Data;

/**
 * 
 * @TableName asset_ledger
 */
@TableName(value ="asset_ledger")
@Data
public class AssetBaseInfo implements Serializable {
    /**
     * 资产编号
     */
    @TableId(type = IdType.AUTO)
    private String assetId;

    /**
     * 资产分类ID
     */
    private Integer categoryId;

    /**
     * 资产名称
     */
    private String assetName;

    /**
     * 资产状态
     */
    private Integer assetStatus;

    /**
     * 使用组织
     */
    private Long deptId;

    /**
     * 管理人员
     */
    @TableField(exist = false)
    private List<Long> managerIds;

    /**
     * 规格型号
     */
    private String specModel;

    /**
     * 品牌信息
     */
    private String assetBrand;

    /**
     * 资产用途
     */
    private String assetPurpose;

    /**
     * 存放位置
     */
    private Integer storageLocation;

    /**
     * 计量单位
     */
    private String assetUnit;

    /**
     * 详细地址
     */
    private String detailLocation;

    /**
     * 资产价值
     */
    private BigDecimal assetValue;

    /**
     * 备注
     */
    private String remark;

    /**
     *
     */
    private Date createTime;

    /**
     *
     */
    private Date updateTime;

    /**
     * 删除标志(0代表存在,1代表删除)
     */
    private String delFlag;

    @TableField(exist = false)
    private List<AttachmentDto> attachmentList;

    @TableField(exist = false)
    private List<String> pictureUrls;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;



}