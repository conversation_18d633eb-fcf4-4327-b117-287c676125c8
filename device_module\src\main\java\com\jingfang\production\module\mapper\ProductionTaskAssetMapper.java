package com.jingfang.production.module.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jingfang.production.module.entity.ProductionTaskAsset;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 生产任务资产关联Mapper接口
 */
@Mapper
public interface ProductionTaskAssetMapper extends BaseMapper<ProductionTaskAsset> {
    
    /**
     * 根据任务ID查询关联的资产列表
     * 
     * @param taskId 任务ID
     * @return 资产关联列表
     */
    List<ProductionTaskAsset> selectAssetsByTaskId(@Param("taskId") String taskId);
    
    /**
     * 根据资产ID查询关联的任务列表
     * 
     * @param assetId 资产ID
     * @return 任务关联列表
     */
    List<ProductionTaskAsset> selectTasksByAssetId(@Param("assetId") String assetId);
    
    /**
     * 查询任务的资产使用统计
     * 
     * @param taskId 任务ID
     * @return 资产使用统计
     */
    List<Map<String, Object>> selectAssetUsageStatistics(@Param("taskId") String taskId);
    
    /**
     * 查询资产的任务使用情况
     * 
     * @param assetId 资产ID
     * @return 任务使用情况
     */
    List<Map<String, Object>> selectAssetTaskUsage(@Param("assetId") String assetId);
    
    /**
     * 批量插入任务资产关联
     * 
     * @param assetList 资产关联列表
     * @return 插入结果
     */
    int batchInsertTaskAssets(@Param("assetList") List<ProductionTaskAsset> assetList);
    
    /**
     * 根据任务ID删除所有资产关联
     * 
     * @param taskId 任务ID
     * @return 删除结果
     */
    int deleteAssetsByTaskId(@Param("taskId") String taskId);
    
    /**
     * 检查资产是否被其他任务使用
     * 
     * @param assetId 资产ID
     * @param excludeTaskId 排除的任务ID
     * @return 使用中的任务数量
     */
    int checkAssetInUse(@Param("assetId") String assetId, @Param("excludeTaskId") String excludeTaskId);
}
