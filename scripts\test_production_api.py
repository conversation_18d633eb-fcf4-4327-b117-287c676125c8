#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生产管控模块API测试脚本
"""

import requests
import json
import time
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Any

class ProductionApiTester:
    def __init__(self, base_url: str = "http://localhost:8080", token: str = None):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
        
        if token:
            self.session.headers.update({
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json'
            })
    
    def log_test(self, test_name: str, success: bool, message: str = "", response_data: Any = None):
        """记录测试结果"""
        result = {
            'test_name': test_name,
            'success': success,
            'message': message,
            'timestamp': datetime.now().isoformat(),
            'response_data': response_data
        }
        self.test_results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if message:
            print(f"    {message}")
        if not success and response_data:
            print(f"    Response: {response_data}")
        print()
    
    def api_request(self, method: str, endpoint: str, data: Dict = None, params: Dict = None) -> Dict:
        """发送API请求"""
        url = f"{self.base_url}{endpoint}"
        
        try:
            if method.upper() == 'GET':
                response = self.session.get(url, params=params)
            elif method.upper() == 'POST':
                response = self.session.post(url, json=data, params=params)
            elif method.upper() == 'PUT':
                response = self.session.put(url, json=data)
            elif method.upper() == 'DELETE':
                response = self.session.delete(url)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            return {'error': str(e), 'status_code': getattr(e.response, 'status_code', None)}
    
    def test_task_crud_operations(self):
        """测试任务CRUD操作"""
        print("=== 测试任务CRUD操作 ===")
        
        # 1. 创建任务
        task_data = {
            'taskName': '测试生产任务',
            'taskType': 1,
            'productName': '测试产品',
            'planStartDate': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'planEndDate': (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S'),
            'priorityLevel': 2,
            'responsibleUserId': 1,
            'estimatedHours': 8.0,
            'remark': 'API测试任务'
        }
        
        response = self.api_request('POST', '/production/task', task_data)
        if 'error' not in response and response.get('code') == 200:
            self.log_test("创建生产任务", True, "任务创建成功")
            task_id = response.get('data', {}).get('taskId')
        else:
            self.log_test("创建生产任务", False, "任务创建失败", response)
            return None
        
        # 2. 查询任务详情
        response = self.api_request('GET', f'/production/task/{task_id}')
        if 'error' not in response and response.get('code') == 200:
            task_detail = response.get('data')
            if task_detail and task_detail.get('taskName') == '测试生产任务':
                self.log_test("查询任务详情", True, f"任务ID: {task_id}")
            else:
                self.log_test("查询任务详情", False, "任务信息不匹配", response)
        else:
            self.log_test("查询任务详情", False, "查询失败", response)
        
        # 3. 更新任务
        task_data['taskId'] = task_id
        task_data['taskName'] = '更新后的测试任务'
        task_data['remark'] = '已更新的API测试任务'
        
        response = self.api_request('PUT', '/production/task', task_data)
        if 'error' not in response and response.get('code') == 200:
            self.log_test("更新生产任务", True, "任务更新成功")
        else:
            self.log_test("更新生产任务", False, "任务更新失败", response)
        
        # 4. 查询任务列表
        query_data = {
            'pageNum': 1,
            'pageSize': 10,
            'taskName': '更新后的测试任务'
        }
        
        response = self.api_request('POST', '/production/task/list', query_data)
        if 'error' not in response and response.get('code') == 200:
            rows = response.get('rows', [])
            if len(rows) > 0 and rows[0].get('taskName') == '更新后的测试任务':
                self.log_test("查询任务列表", True, f"找到 {len(rows)} 个任务")
            else:
                self.log_test("查询任务列表", False, "未找到匹配的任务", response)
        else:
            self.log_test("查询任务列表", False, "查询失败", response)
        
        return task_id
    
    def test_task_workflow_operations(self, task_id: str):
        """测试任务工作流操作"""
        print("=== 测试任务工作流操作 ===")
        
        # 1. 分配任务
        response = self.api_request('POST', f'/production/task/assign/{task_id}', params={'responsibleUserId': 2})
        if 'error' not in response and response.get('code') == 200:
            self.log_test("分配任务", True, "任务分配成功")
        else:
            self.log_test("分配任务", False, "任务分配失败", response)
        
        # 2. 开始执行任务
        response = self.api_request('POST', f'/production/task/start/{task_id}')
        if 'error' not in response and response.get('code') == 200:
            self.log_test("开始执行任务", True, "任务开始执行")
        else:
            self.log_test("开始执行任务", False, "开始执行失败", response)
        
        # 3. 更新任务进度
        response = self.api_request('POST', f'/production/task/progress/{task_id}', params={'progressRate': 50.0})
        if 'error' not in response and response.get('code') == 200:
            self.log_test("更新任务进度", True, "进度更新为50%")
        else:
            self.log_test("更新任务进度", False, "进度更新失败", response)
        
        # 4. 暂停任务
        response = self.api_request('POST', f'/production/task/pause/{task_id}')
        if 'error' not in response and response.get('code') == 200:
            self.log_test("暂停任务", True, "任务暂停成功")
        else:
            self.log_test("暂停任务", False, "任务暂停失败", response)
        
        # 5. 重新开始任务
        response = self.api_request('POST', f'/production/task/start/{task_id}')
        if 'error' not in response and response.get('code') == 200:
            self.log_test("重新开始任务", True, "任务重新开始")
        else:
            self.log_test("重新开始任务", False, "重新开始失败", response)
        
        # 6. 完成任务
        response = self.api_request('POST', f'/production/task/complete/{task_id}')
        if 'error' not in response and response.get('code') == 200:
            self.log_test("完成任务", True, "任务完成成功")
        else:
            self.log_test("完成任务", False, "任务完成失败", response)
    
    def test_task_node_operations(self, task_id: str):
        """测试任务节点操作"""
        print("=== 测试任务节点操作 ===")
        
        # 1. 创建节点
        node_data = {
            'taskId': task_id,
            'nodeName': '测试节点',
            'nodeType': 2,
            'sequenceNo': 1,
            'isRequired': 1,
            'estimatedDuration': 60
        }
        
        response = self.api_request('POST', '/production/task/node', node_data)
        if 'error' not in response and response.get('code') == 200:
            self.log_test("创建任务节点", True, "节点创建成功")
            node_id = response.get('data', {}).get('nodeId')
        else:
            self.log_test("创建任务节点", False, "节点创建失败", response)
            return
        
        # 2. 查询任务节点列表
        response = self.api_request('GET', f'/production/task/node/list/{task_id}')
        if 'error' not in response and response.get('code') == 200:
            nodes = response.get('data', [])
            if len(nodes) > 0:
                self.log_test("查询任务节点列表", True, f"找到 {len(nodes)} 个节点")
                node_id = nodes[0].get('nodeId')
            else:
                self.log_test("查询任务节点列表", False, "未找到节点", response)
                return
        else:
            self.log_test("查询任务节点列表", False, "查询失败", response)
            return
        
        # 3. 开始执行节点
        response = self.api_request('POST', f'/production/task/node/start/{node_id}')
        if 'error' not in response and response.get('code') == 200:
            self.log_test("开始执行节点", True, "节点开始执行")
        else:
            self.log_test("开始执行节点", False, "节点执行失败", response)
        
        # 4. 完成节点执行
        response = self.api_request('POST', f'/production/task/node/complete/{node_id}')
        if 'error' not in response and response.get('code') == 200:
            self.log_test("完成节点执行", True, "节点执行完成")
        else:
            self.log_test("完成节点执行", False, "节点完成失败", response)
    
    def test_statistics_operations(self):
        """测试统计功能"""
        print("=== 测试统计功能 ===")
        
        # 1. 查询任务状态统计
        response = self.api_request('GET', '/production/task/statistics/status')
        if 'error' not in response and response.get('code') == 200:
            stats = response.get('data', [])
            self.log_test("查询任务状态统计", True, f"获取到 {len(stats)} 项统计数据")
        else:
            self.log_test("查询任务状态统计", False, "统计查询失败", response)
        
        # 2. 查询任务进度统计
        response = self.api_request('GET', '/production/task/statistics/progress')
        if 'error' not in response and response.get('code') == 200:
            stats = response.get('data', [])
            self.log_test("查询任务进度统计", True, f"获取到 {len(stats)} 项统计数据")
        else:
            self.log_test("查询任务进度统计", False, "统计查询失败", response)
        
        # 3. 查询即将到期的任务
        response = self.api_request('GET', '/production/task/upcoming', params={'days': 7})
        if 'error' not in response and response.get('code') == 200:
            tasks = response.get('data', [])
            self.log_test("查询即将到期任务", True, f"找到 {len(tasks)} 个即将到期的任务")
        else:
            self.log_test("查询即将到期任务", False, "查询失败", response)
        
        # 4. 查询已过期的任务
        response = self.api_request('GET', '/production/task/overdue')
        if 'error' not in response and response.get('code') == 200:
            tasks = response.get('data', [])
            self.log_test("查询已过期任务", True, f"找到 {len(tasks)} 个已过期的任务")
        else:
            self.log_test("查询已过期任务", False, "查询失败", response)
    
    def test_copy_task_operation(self, task_id: str):
        """测试任务复制功能"""
        print("=== 测试任务复制功能 ===")
        
        response = self.api_request('POST', f'/production/task/copy/{task_id}', params={'taskName': '复制的测试任务'})
        if 'error' not in response and response.get('code') == 200:
            self.log_test("复制任务", True, "任务复制成功")
        else:
            self.log_test("复制任务", False, "任务复制失败", response)
    
    def cleanup_test_data(self, task_id: str):
        """清理测试数据"""
        print("=== 清理测试数据 ===")
        
        response = self.api_request('DELETE', f'/production/task/{task_id}')
        if 'error' not in response and response.get('code') == 200:
            self.log_test("删除测试任务", True, "测试数据清理完成")
        else:
            self.log_test("删除测试任务", False, "清理失败", response)
    
    def generate_test_report(self):
        """生成测试报告"""
        print("\n" + "="*60)
        print("测试报告")
        print("="*60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {(passed_tests/total_tests*100):.1f}%")
        
        if failed_tests > 0:
            print("\n失败的测试:")
            for result in self.test_results:
                if not result['success']:
                    print(f"  - {result['test_name']}: {result['message']}")
        
        # 保存详细报告到文件
        report_file = f"production_api_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n详细报告已保存到: {report_file}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("开始生产管控模块API测试")
        print("="*60)
        
        # 1. 测试任务CRUD操作
        task_id = self.test_task_crud_operations()
        
        if task_id:
            # 2. 测试任务工作流操作
            self.test_task_workflow_operations(task_id)
            
            # 3. 测试任务节点操作
            self.test_task_node_operations(task_id)
            
            # 4. 测试任务复制功能
            self.test_copy_task_operation(task_id)
            
            # 5. 清理测试数据
            self.cleanup_test_data(task_id)
        
        # 6. 测试统计功能
        self.test_statistics_operations()
        
        # 7. 生成测试报告
        self.generate_test_report()

def main():
    """主函数"""
    # 配置参数
    BASE_URL = "http://localhost:8080"
    TOKEN = "your_jwt_token_here"  # 请替换为实际的JWT token
    
    # 创建测试器实例
    tester = ProductionApiTester(BASE_URL, TOKEN)
    
    # 运行所有测试
    tester.run_all_tests()

if __name__ == "__main__":
    main()
