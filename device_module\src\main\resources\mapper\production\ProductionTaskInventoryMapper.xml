<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.production.mapper.ProductionTaskInventoryMapper">
    
    <resultMap type="com.jingfang.production.module.entity.ProductionTaskInventory" id="ProductionTaskInventoryResult">
        <result property="relationId" column="relation_id" />
        <result property="taskId" column="task_id" />
        <result property="itemId" column="item_id" />
        <result property="itemCode" column="item_code" />
        <result property="itemName" column="item_name" />
        <result property="plannedQuantity" column="planned_quantity" />
        <result property="createTime" column="create_time" />
        <result property="remark" column="remark" />
    </resultMap>
    
    <sql id="selectProductionTaskInventoryVo">
        SELECT 
            pti.relation_id,
            pti.task_id,
            pti.item_id,
            pti.item_code,
            pti.item_name,
            pti.planned_quantity,
            pti.create_time,
            pti.remark
        FROM production_task_inventory pti
    </sql>
    
    <select id="selectInventoriesByTaskId" resultMap="ProductionTaskInventoryResult">
        <include refid="selectProductionTaskInventoryVo"/>
        WHERE pti.task_id = #{taskId}
        ORDER BY pti.create_time ASC
    </select>
    
    <select id="selectTasksByItemId" resultMap="ProductionTaskInventoryResult">
        <include refid="selectProductionTaskInventoryVo"/>
        WHERE pti.item_id = #{itemId}
        ORDER BY pti.create_time DESC
    </select>
    
    <select id="selectInventoryUsageStatistics" resultType="java.util.Map">
        SELECT 
            pti.item_id,
            pti.item_code,
            pti.item_name,
            SUM(pti.planned_quantity) as total_planned_quantity,
            COUNT(*) as usage_count
        FROM production_task_inventory pti
        WHERE pti.task_id = #{taskId}
        GROUP BY pti.item_id, pti.item_code, pti.item_name
        ORDER BY total_planned_quantity DESC
    </select>
    
    <select id="selectItemTaskUsage" resultType="java.util.Map">
        SELECT 
            pti.task_id,
            pt.task_name,
            pt.status,
            CASE pt.status 
                WHEN 1 THEN '待执行'
                WHEN 2 THEN '已分配'
                WHEN 3 THEN '执行中'
                WHEN 4 THEN '质检中'
                WHEN 5 THEN '异常处理'
                WHEN 6 THEN '已完工'
                WHEN 7 THEN '已核算'
                ELSE '未知'
            END as status_name,
            pti.planned_quantity,
            pt.plan_start_date,
            pt.plan_end_date,
            pti.create_time
        FROM production_task_inventory pti
        LEFT JOIN production_task pt ON pti.task_id = pt.task_id
        WHERE pti.item_id = #{itemId}
        ORDER BY pti.create_time DESC
    </select>
    
    <select id="selectInventoryConsumptionSummary" resultType="java.util.Map">
        SELECT 
            pti.item_id,
            pti.item_code,
            pti.item_name,
            SUM(pti.planned_quantity) as total_consumption,
            COUNT(DISTINCT pti.task_id) as task_count,
            MIN(pt.plan_start_date) as earliest_usage,
            MAX(pt.plan_end_date) as latest_usage
        FROM production_task_inventory pti
        LEFT JOIN production_task pt ON pti.task_id = pt.task_id
        WHERE 1=1
        <if test="startDate != null and startDate != ''">
            AND DATE(pt.plan_start_date) >= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND DATE(pt.plan_end_date) &lt;= #{endDate}
        </if>
        GROUP BY pti.item_id, pti.item_code, pti.item_name
        ORDER BY total_consumption DESC
    </select>
    
    <insert id="batchInsertTaskInventories">
        INSERT INTO production_task_inventory (
            relation_id, task_id, item_id, item_code, item_name, planned_quantity, create_time, remark
        ) VALUES
        <foreach collection="inventoryList" item="inventory" separator=",">
            (
                #{inventory.relationId}, #{inventory.taskId}, #{inventory.itemId}, 
                #{inventory.itemCode}, #{inventory.itemName}, #{inventory.plannedQuantity}, 
                #{inventory.createTime}, #{inventory.remark}
            )
        </foreach>
    </insert>
    
    <delete id="deleteInventoriesByTaskId">
        DELETE FROM production_task_inventory WHERE task_id = #{taskId}
    </delete>
    
    <select id="checkInventoryAvailability" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(current_stock), 0) as available_quantity
        FROM wh_item_inventory 
        WHERE item_id = #{itemId}
        AND current_stock >= #{requiredQuantity}
    </select>

</mapper>
