package com.jingfang.item_consumption_report.module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 库存消耗基准数据表
 * @TableName item_consumption_benchmark
 */
@TableName(value = "item_consumption_benchmark")
@Data
public class ItemConsumptionBenchmark implements Serializable {
    
    /**
     * 基准数据ID
     */
    @TableId(type = IdType.INPUT)
    private String benchmarkId;
    
    /**
     * 物品ID
     */
    private String itemId;
    
    /**
     * 基准类型(standard/target/historical)
     */
    private String benchmarkType;
    
    /**
     * 单位产量标准消耗
     */
    private BigDecimal consumptionPerUnit;
    
    /**
     * 单位产量标准成本
     */
    private BigDecimal costPerUnit;
    
    /**
     * 优秀效率阈值
     */
    private BigDecimal efficiencyThresholdExcellent;
    
    /**
     * 良好效率阈值
     */
    private BigDecimal efficiencyThresholdGood;
    
    /**
     * 一般效率阈值
     */
    private BigDecimal efficiencyThresholdAverage;
    
    /**
     * 有效开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date validStartDate;
    
    /**
     * 有效结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date validEndDate;
    
    /**
     * 是否启用(0-否,1-是)
     */
    private Integer isActive;
    
    /**
     * 备注说明
     */
    private String remark;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    
    /**
     * 更新人
     */
    private String updateBy;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    
    private static final long serialVersionUID = 1L;
}
