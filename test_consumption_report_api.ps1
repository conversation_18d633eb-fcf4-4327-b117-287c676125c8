# 库存消耗报表API测试脚本

# 设置基础变量
$baseUrl = "http://localhost:8080"
$token = "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiZjM0NDU2YTktYmU2Mi00MTY1LTk4ZjUtN2VhODNlM2VhNjBhIn0.xBoYuAG7vY_hqXeL7Siz_xiIG3ZjEOh_49L5cmvIFUvTHGWul0C33aZWLUbhXj6a4NHWO5aiIggbxC6MXerh_A"

# 设置请求头
$headers = @{
    'Content-Type' = 'application/json'
    'Authorization' = "Bearer $token"
}

Write-Host "开始测试库存消耗报表API..." -ForegroundColor Green

# 测试1: 分页查询消耗报表数据
Write-Host "`n1. 测试分页查询消耗报表数据" -ForegroundColor Yellow
$body1 = @{
    pageNum = 1
    pageSize = 10
    startDate = "2024-01-01"
    endDate = "2024-12-31"
} | ConvertTo-Json

try {
    $response1 = Invoke-WebRequest -Uri "$baseUrl/item/consumption-report/list" -Method POST -Headers $headers -Body $body1
    Write-Host "状态码: $($response1.StatusCode)" -ForegroundColor Green
    Write-Host "响应内容: $($response1.Content)" -ForegroundColor Cyan
} catch {
    Write-Host "请求失败: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "错误响应: $responseBody" -ForegroundColor Red
    }
}

# 测试2: 查询消耗统计数据
Write-Host "`n2. 测试查询消耗统计数据" -ForegroundColor Yellow
$body2 = @{
    startDate = "2024-01-01"
    endDate = "2024-12-31"
} | ConvertTo-Json

try {
    $response2 = Invoke-WebRequest -Uri "$baseUrl/item/consumption-report/statistics" -Method POST -Headers $headers -Body $body2
    Write-Host "状态码: $($response2.StatusCode)" -ForegroundColor Green
    Write-Host "响应内容: $($response2.Content)" -ForegroundColor Cyan
} catch {
    Write-Host "请求失败: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "错误响应: $responseBody" -ForegroundColor Red
    }
}

# 测试3: 查询消耗趋势数据
Write-Host "`n3. 测试查询消耗趋势数据" -ForegroundColor Yellow
try {
    $response3 = Invoke-WebRequest -Uri "$baseUrl/item/consumption-report/trend?itemId=ITEM001&startDate=2024-01-01&endDate=2024-12-31&timeDimension=3" -Method GET -Headers $headers
    Write-Host "状态码: $($response3.StatusCode)" -ForegroundColor Green
    Write-Host "响应内容: $($response3.Content)" -ForegroundColor Cyan
} catch {
    Write-Host "请求失败: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "错误响应: $responseBody" -ForegroundColor Red
    }
}

# 测试4: 查询消耗类型分布数据
Write-Host "`n4. 测试查询消耗类型分布数据" -ForegroundColor Yellow
try {
    $response4 = Invoke-WebRequest -Uri "$baseUrl/item/consumption-report/type-distribution?itemId=ITEM001&startDate=2024-01-01&endDate=2024-12-31" -Method GET -Headers $headers
    Write-Host "状态码: $($response4.StatusCode)" -ForegroundColor Green
    Write-Host "响应内容: $($response4.Content)" -ForegroundColor Cyan
} catch {
    Write-Host "请求失败: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "错误响应: $responseBody" -ForegroundColor Red
    }
}

# 测试5: 查询效率排行榜
Write-Host "`n5. 测试查询效率排行榜" -ForegroundColor Yellow
$body5 = @{
    startDate = "2024-01-01"
    endDate = "2024-12-31"
    analysisPeriod = "monthly"
} | ConvertTo-Json

try {
    $response5 = Invoke-WebRequest -Uri "$baseUrl/item/consumption-report/efficiency-ranking?limit=5" -Method POST -Headers $headers -Body $body5
    Write-Host "状态码: $($response5.StatusCode)" -ForegroundColor Green
    Write-Host "响应内容: $($response5.Content)" -ForegroundColor Cyan
} catch {
    Write-Host "请求失败: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "错误响应: $responseBody" -ForegroundColor Red
    }
}

# 测试6: 生成图表数据
Write-Host "`n6. 测试生成图表数据" -ForegroundColor Yellow
$body6 = @{
    startDate = "2024-01-01"
    endDate = "2024-12-31"
} | ConvertTo-Json

try {
    $response6 = Invoke-WebRequest -Uri "$baseUrl/item/consumption-report/chart-data" -Method POST -Headers $headers -Body $body6
    Write-Host "状态码: $($response6.StatusCode)" -ForegroundColor Green
    Write-Host "响应内容: $($response6.Content)" -ForegroundColor Cyan
} catch {
    Write-Host "请求失败: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "错误响应: $responseBody" -ForegroundColor Red
    }
}

# 测试7: 生成消耗分析报告
Write-Host "`n7. 测试生成消耗分析报告" -ForegroundColor Yellow
$body7 = @{
    startDate = "2024-01-01"
    endDate = "2024-12-31"
    includeEfficiencyAnalysis = $true
    includeTrendAnalysis = $true
} | ConvertTo-Json

try {
    $response7 = Invoke-WebRequest -Uri "$baseUrl/item/consumption-report/analysis-report" -Method POST -Headers $headers -Body $body7
    Write-Host "状态码: $($response7.StatusCode)" -ForegroundColor Green
    Write-Host "响应内容: $($response7.Content)" -ForegroundColor Cyan
} catch {
    Write-Host "请求失败: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "错误响应: $responseBody" -ForegroundColor Red
    }
}

# 测试8: 获取优化建议
Write-Host "`n8. 测试获取优化建议" -ForegroundColor Yellow
try {
    $response8 = Invoke-WebRequest -Uri "$baseUrl/item/consumption-report/optimization-suggestions?itemId=ITEM001&analysisPeriod=monthly" -Method GET -Headers $headers
    Write-Host "状态码: $($response8.StatusCode)" -ForegroundColor Green
    Write-Host "响应内容: $($response8.Content)" -ForegroundColor Cyan
} catch {
    Write-Host "请求失败: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "错误响应: $responseBody" -ForegroundColor Red
    }
}

Write-Host "`n测试完成!" -ForegroundColor Green
