package com.jingfang.production.module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 生产任务资产关联表
 * @TableName production_task_asset
 */
@TableName(value = "production_task_asset")
@Data
public class ProductionTaskAsset implements Serializable {
    
    /**
     * 关联ID
     */
    @TableId(type = IdType.INPUT)
    private String relationId;
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 资产ID
     */
    private String assetId;
    
    /**
     * 资产编码
     */
    private String assetCode;
    
    /**
     * 资产名称
     */
    private String assetName;
    
    /**
     * 使用类型(1-主要设备,2-辅助设备,3-工具,4-模具)
     */
    private Integer usageType;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    
    /**
     * 备注
     */
    private String remark;
    
    private static final long serialVersionUID = 1L;
}