package com.jingfang.production.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jingfang.production.dto.ProductionTaskDto;
import com.jingfang.production.dto.ProductionTaskQueryDto;
import com.jingfang.production.module.entity.ProductionTask;
import com.jingfang.production.module.entity.vo.ProductionTaskVo;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 生产任务Service接口
 *
 * <AUTHOR>
 * @date 2025-01-16
 */
public interface ProductionTaskService extends IService<ProductionTask> {

    /**
     * 分页查询生产任务列表
     *
     * @param queryDto 查询条件
     * @return 生产任务列表
     */
    IPage<ProductionTaskVo> selectProductionTaskList(ProductionTaskQueryDto queryDto);

    /**
     * 根据ID查询生产任务详情
     *
     * @param taskId 任务ID
     * @return 生产任务详情
     */
    ProductionTaskVo selectProductionTaskById(String taskId);

    /**
     * 新增生产任务
     *
     * @param taskDto 任务信息
     * @param createBy 创建人
     * @return 结果
     */
    boolean insertProductionTask(ProductionTaskDto taskDto, String createBy);

    /**
     * 修改生产任务
     *
     * @param taskDto 任务信息
     * @param updateBy 更新人
     * @return 结果
     */
    boolean updateProductionTask(ProductionTaskDto taskDto, String updateBy);

    /**
     * 删除生产任务
     *
     * @param taskId 任务ID
     * @return 结果
     */
    boolean deleteProductionTask(String taskId);

    /**
     * 批量删除生产任务
     *
     * @param taskIds 任务ID列表
     * @return 结果
     */
    boolean deleteProductionTasks(List<String> taskIds);

    /**
     * 开始执行任务
     *
     * @param taskId 任务ID
     * @param updateBy 更新人
     * @return 结果
     */
    boolean startTask(String taskId, String updateBy);

    /**
     * 暂停任务
     *
     * @param taskId 任务ID
     * @param updateBy 更新人
     * @return 结果
     */
    boolean pauseTask(String taskId, String updateBy);

    /**
     * 完成任务
     *
     * @param taskId 任务ID
     * @param updateBy 更新人
     * @return 结果
     */
    boolean completeTask(String taskId, String updateBy);

    /**
     * 取消任务
     *
     * @param taskId 任务ID
     * @param updateBy 更新人
     * @return 结果
     */
    boolean cancelTask(String taskId, String updateBy);

    /**
     * 更新任务进度
     *
     * @param taskId 任务ID
     * @param progressRate 进度百分比
     * @return 结果
     */
    boolean updateTaskProgress(String taskId, BigDecimal progressRate);

    /**
     * 分配任务
     *
     * @param taskId 任务ID
     * @param responsibleUserId 负责人ID
     * @param updateBy 更新人
     * @return 结果
     */
    boolean assignTask(String taskId, Long responsibleUserId, String updateBy);

    /**
     * 查询任务状态统计
     *
     * @return 状态统计数据
     */
    List<Map<String, Object>> selectTaskStatusStatistics();

    /**
     * 查询任务进度统计
     *
     * @return 进度统计数据
     */
    List<Map<String, Object>> selectTaskProgressStatistics();

    /**
     * 查询即将到期的任务
     *
     * @param days 提前天数
     * @return 即将到期的任务列表
     */
    List<ProductionTaskVo> selectUpcomingTasks(Integer days);

    /**
     * 查询已过期的任务
     *
     * @return 已过期的任务列表
     */
    List<ProductionTaskVo> selectOverdueTasks();

    /**
     * 查询负责人的任务列表
     *
     * @param userId 负责人ID
     * @param status 任务状态（可选）
     * @return 任务列表
     */
    List<ProductionTaskVo> selectTasksByResponsibleUser(Long userId, Integer status);

    /**
     * 查询任务的资源使用情况
     *
     * @param taskId 任务ID
     * @return 资源使用情况
     */
    Map<String, Object> selectTaskResourceUsage(String taskId);

    /**
     * 复制任务
     *
     * @param sourceTaskId 源任务ID
     * @param taskName 新任务名称
     * @param createBy 创建人
     * @return 结果
     */
    boolean copyTask(String sourceTaskId, String taskName, String createBy);

    /**
     * 导出任务数据
     *
     * @param queryDto 查询条件
     * @return 导出数据
     */
    List<ProductionTaskVo> exportTaskData(ProductionTaskQueryDto queryDto);
}